-- ============================================================================
-- FIX MISSING PRESIDENTIAL ROOM IMAGES
-- This script adds images specifically to presidential rooms that are missing them
-- ============================================================================

-- Step 1: Add sample images to presidential rooms
UPDATE public.rooms 
SET images = ARRAY[
    'https://images.unsplash.com/photo-1582719478250-c89cae4dc85b?w=800&q=80',
    'https://images.unsplash.com/photo-1571896349842-33c89424de2d?w=800&q=80',
    'https://images.unsplash.com/photo-1590490360182-c33d57733427?w=800&q=80',
    'https://images.unsplash.com/photo-1578683010236-d716f9a3f461?w=800&q=80',
    'https://images.unsplash.com/photo-1611892440504-42a792e24d32?w=800&q=80'
]
WHERE room_type = 'presidential';

-- Step 2: Convert the presidential room images to enhanced format with IDs
DO $$
DECLARE
    room_record RECORD;
    image_url TEXT;
    new_images JSONB := '[]'::jsonb;
    image_index INTEGER;
    image_id TEXT;
    image_object JSONB;
    total_converted INTEGER := 0;
BEGIN
    RAISE NOTICE 'Converting presidential room images to enhanced format...';
    
    -- Loop through presidential rooms that have images
    FOR room_record IN 
        SELECT id, room_number, room_type, images 
        FROM public.rooms 
        WHERE room_type = 'presidential' 
        AND images IS NOT NULL 
        AND array_length(images, 1) > 0
    LOOP
        -- Reset for each room
        new_images := '[]'::jsonb;
        image_index := 1;
        
        -- Convert each image URL to a RoomImage object
        FOREACH image_url IN ARRAY room_record.images
        LOOP
            -- Generate a unique ID for the image
            image_id := 'img_' || 
                       extract(epoch from now())::bigint || '_' || 
                       image_index || '_' || 
                       substr(md5(random()::text || room_record.id::text), 1, 8);
            
            -- Create the image object with proper structure
            image_object := jsonb_build_object(
                'id', image_id,
                'url', image_url,
                'alt_text', 'Presidential Suite ' || room_record.room_number || ' - Luxury image ' || image_index,
                'upload_date', now()::text,
                'file_name', 'presidential-' || room_record.room_number || '-' || image_index || '.jpg'
            );
            
            -- Add to the new images array
            new_images := new_images || image_object;
            
            image_index := image_index + 1;
        END LOOP;
        
        -- Update the room with the new image format
        UPDATE public.rooms 
        SET images_enhanced = new_images 
        WHERE id = room_record.id;
        
        total_converted := total_converted + 1;
        
        RAISE NOTICE 'Converted presidential room % with % images', 
                     room_record.room_number, 
                     array_length(room_record.images, 1);
    END LOOP;
    
    RAISE NOTICE 'Successfully converted % presidential rooms to enhanced format!', total_converted;
END;
$$;

-- Step 3: Verify the fix
SELECT 
    'Presidential Room Images Fix Complete!' as status,
    COUNT(*) as total_presidential_rooms,
    COUNT(*) FILTER (WHERE images_enhanced IS NOT NULL AND jsonb_array_length(images_enhanced) > 0) as presidential_rooms_with_images,
    SUM(jsonb_array_length(images_enhanced)) FILTER (WHERE images_enhanced IS NOT NULL) as total_presidential_images
FROM public.rooms 
WHERE room_type = 'presidential';

-- Show the presidential room images
SELECT 
    room_number,
    room_type,
    jsonb_array_length(COALESCE(images_enhanced, '[]'::jsonb)) as image_count,
    jsonb_pretty(images_enhanced) as enhanced_images
FROM public.rooms 
WHERE room_type = 'presidential'
ORDER BY room_number;

-- Final success message
DO $$
BEGIN
    RAISE NOTICE '🎉 PRESIDENTIAL ROOM IMAGES FIXED!';
    RAISE NOTICE '';
    RAISE NOTICE '✅ What was done:';
    RAISE NOTICE '- Added 5 luxury hotel images to presidential rooms';
    RAISE NOTICE '- Converted images to enhanced format with unique IDs';
    RAISE NOTICE '- Presidential rooms now have proper image display';
    RAISE NOTICE '';
    RAISE NOTICE '🏨 Presidential room images include:';
    RAISE NOTICE '- Luxury hotel suite interiors';
    RAISE NOTICE '- Premium room amenities';
    RAISE NOTICE '- High-end furnishings and decor';
    RAISE NOTICE '';
    RAISE NOTICE '🚀 Your presidential rooms now have images!';
END;
$$;
