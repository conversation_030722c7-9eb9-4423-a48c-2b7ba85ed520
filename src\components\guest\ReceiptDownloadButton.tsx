import React, { useState } from 'react';
import { Alert } from 'react-native';
import { Button, IconButton } from 'react-native-paper';
import { receiptService } from '../../services/receiptService';
import { colors } from '../../constants';

interface ReceiptDownloadButtonProps {
  reservationId: string;
  paymentId?: string;
  variant?: 'button' | 'icon';
  mode?: 'text' | 'outlined' | 'contained';
  size?: 'small' | 'medium' | 'large';
  style?: any;
  disabled?: boolean;
}

export const ReceiptDownloadButton: React.FC<ReceiptDownloadButtonProps> = ({
  reservationId,
  paymentId,
  variant = 'button',
  mode = 'outlined',
  size = 'medium',
  style,
  disabled = false,
}) => {
  const [downloading, setDownloading] = useState(false);

  const handleDownload = async () => {
    if (!reservationId || downloading || disabled) return;

    try {
      setDownloading(true);
      await receiptService.generateAndShareReceipt(reservationId, paymentId);
      Alert.alert(
        'Success',
        'Receipt has been generated and is ready to share!',
        [{ text: 'OK' }]
      );
    } catch (error) {
      console.error('Error downloading receipt:', error);
      Alert.alert(
        'Error',
        'Failed to generate receipt. Please try again.',
        [{ text: 'OK' }]
      );
    } finally {
      setDownloading(false);
    }
  };

  if (variant === 'icon') {
    return (
      <IconButton
        icon="download"
        size={size === 'small' ? 16 : size === 'large' ? 28 : 20}
        onPress={handleDownload}
        disabled={downloading || disabled}
        style={style}
      />
    );
  }

  return (
    <Button
      mode={mode}
      onPress={handleDownload}
      loading={downloading}
      disabled={downloading || disabled}
      icon="download"
      style={[
        {
          minHeight: 48,
          backgroundColor: mode === 'contained' ? colors.primary : 'transparent',
          borderWidth: mode === 'outlined' ? 1 : 0,
          borderColor: mode === 'outlined' ? colors.primary : 'transparent',
          elevation: mode === 'contained' ? 2 : 0,
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 1 },
          shadowOpacity: 0.2,
          shadowRadius: 2,
        },
        style
      ]}
      contentStyle={{ height: 48, justifyContent: 'center' }}
      buttonColor={mode === 'contained' ? colors.primary : undefined}
      textColor={mode === 'contained' ? colors.surface : colors.primary}
      compact={size === 'small'}
    >
      {downloading ? 'Generating...' : 'Download Receipt'}
    </Button>
  );
};
