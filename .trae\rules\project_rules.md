Think like a senior full-stack mobile app engineer with 10+ years of experience in React Native, Supabase, and Paystack integrations. Your goal is to build a modern and scalable mobile hotel reservation system called "Sunset View Hotel Reservation App" using React Native (with Expo), Supabase as the backend and database, and Paystack for online payments. Use modern architecture and best practices in component design, navigation, state management, and authentication.

Build the system based on the following specifications:

🌍 General Overview:
Mobile hotel booking app where guests can view available rooms, make real-time bookings, pay securely via Paystack, and receive instant confirmation.

Admin/staff can manage reservations, rooms, and customers from a dashboard (within the same app under a different role).

Built with React Native (Expo) + Supabase (PostgreSQL, Auth, Storage, Realtime) + Paystack SDK/API for payments.

🧠 Functional Requirements:
1. User Roles & Auth
Role-based access: Guest, Receptionist, Admin.

Sign up, login, logout (via Supabase Auth).

Email verification and password reset.

2. Room Management (Admin Only)
Add, update, and delete room info (room type, price, description, amenities, images).

Manage room availability status (available, booked, maintenance).

Upload images using Supabase Storage.

3. Guest Booking Flow
Browse rooms with filters (dates, type, amenities).

View detailed room information and photos.

Select check-in/check-out dates.

Submit special requests (early check-in, extra bed).

Proceed to Paystack payment.

Get booking confirmation and email receipt.

4. Reservation System
Real-time room availability updates.

Store reservation details in Supabase (guest info, room ID, booking dates, payment status).

Allow guests to view, modify, or cancel bookings.

Notifications on booking creation, updates, and cancellations.

5. Admin Dashboard (Protected)
View upcoming reservations (filter by date/status).

Edit reservation info (guest name, dates, room).

Change room status manually.

View reports on revenue, occupancy, room popularity.

6. Payment Integration
Integrate Paystack SDK or API to allow:

Online payments for reservations.

Receipts generation.

Payment status update in Supabase on success/failure.

Allow admin to mark walk-in payments as “Paid” manually.

7. Analytics & Reports (Admin only)
Generate basic analytics:

Occupancy rates.

Revenue tracking.

Peak booking times.

Most booked room types.

Export data as CSV or PDF (optional).

⚙️ Non-functional Requirements:
Responsive UI/UX (use React Native Paper or Tailwind RN).

Secure auth and data storage (Supabase Auth and RLS).

Scalable and modular code architecture (MVVM or atomic design).

Offline-friendly (cache room data with async storage or SQLite).

Error handling and user feedback (toasts, modals).

📦 Tools & Tech Stack:
Frontend: React Native (Expo SDK), React Navigation, Zustand or Redux Toolkit.

Backend: Supabase (Auth, Realtime, Storage, PostgreSQL).

Payments: Paystack SDK or REST API.

UI: Tailwind CSS for RN or React Native Paper.

Others: ESlint + Prettier, TypeScript (preferred), AsyncStorage.

📲 Deliverables (initial version):
Working mobile app (Expo managed).

Supabase schema for:

Users (with roles),

Rooms,

Reservations,

Payments.

Paystack integration (test mode).

Admin and guest UI with role switching.

Booking and room management flows.

🧪 Bonus (optional advanced features for later):

Contactless self-check-in (QR code or pin).

Guest profile & loyalty program points.

Multi-language support.

Dark mode toggle.

⚠️ Make sure the app is modular, scalable, and developer-friendly. Prioritize reusability, performance, and clean architecture.

