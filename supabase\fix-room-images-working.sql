-- ============================================================================
-- WORKING ROOM IMAGES FIX SCRIPT FOR SUPABASE
-- This script converts room images from string arrays to RoomImage objects
-- Fixed version that works properly in Supabase SQL Editor
-- ============================================================================

-- Step 1: Create a backup of current room data (optional but recommended)
CREATE TABLE IF NOT EXISTS rooms_backup AS 
SELECT * FROM public.rooms WHERE images IS NOT NULL AND array_length(images, 1) > 0;

-- Step 2: Add a temporary column for the new image format
ALTER TABLE public.rooms ADD COLUMN IF NOT EXISTS images_new JSONB DEFAULT '[]'::jsonb;

-- Step 3: Convert existing images to new format
DO $$
DECLARE
    room_record RECORD;
    image_url TEXT;
    new_images JSONB := '[]'::jsonb;
    image_index INTEGER;
    image_id TEXT;
    image_object JSONB;
    total_rooms INTEGER := 0;
    converted_rooms INTEGER := 0;
BEGIN
    -- Count total rooms with images
    SELECT COUNT(*) INTO total_rooms 
    FROM public.rooms 
    WHERE images IS NOT NULL AND array_length(images, 1) > 0;
    
    RAISE NOTICE 'Starting conversion of % rooms with images...', total_rooms;
    
    -- Loop through all rooms that have images
    FOR room_record IN 
        SELECT id, room_number, images 
        FROM public.rooms 
        WHERE images IS NOT NULL AND array_length(images, 1) > 0
    LOOP
        -- Reset for each room
        new_images := '[]'::jsonb;
        image_index := 1;
        
        -- Convert each image URL to a RoomImage object
        FOREACH image_url IN ARRAY room_record.images
        LOOP
            -- Generate a unique ID for the image
            image_id := 'img_' || 
                       extract(epoch from now())::bigint || '_' || 
                       image_index || '_' || 
                       substr(md5(random()::text || room_record.id::text), 1, 8);
            
            -- Create the image object with proper structure
            image_object := jsonb_build_object(
                'id', image_id,
                'url', image_url,
                'alt_text', 'Room ' || room_record.room_number || ' image ' || image_index,
                'upload_date', now()::text,
                'file_name', 'room-' || room_record.room_number || '-' || image_index || '.jpg'
            );
            
            -- Add to the new images array
            new_images := new_images || image_object;
            
            image_index := image_index + 1;
        END LOOP;
        
        -- Update the room with the new image format
        UPDATE public.rooms 
        SET images_new = new_images 
        WHERE id = room_record.id;
        
        converted_rooms := converted_rooms + 1;
        
        -- Log progress every 10 rooms
        IF converted_rooms % 10 = 0 THEN
            RAISE NOTICE 'Converted % of % rooms...', converted_rooms, total_rooms;
        END IF;
    END LOOP;
    
    RAISE NOTICE 'Successfully converted % rooms with % total images', 
                 converted_rooms, 
                 (SELECT SUM(jsonb_array_length(images_new)) FROM public.rooms WHERE images_new IS NOT NULL);
END;
$$;

-- Step 4: Verify the conversion worked correctly
DO $$
DECLARE
    legacy_count INTEGER;
    new_count INTEGER;
    sample_room RECORD;
BEGIN
    -- Count rooms with legacy images
    SELECT COUNT(*) INTO legacy_count 
    FROM public.rooms 
    WHERE images IS NOT NULL AND array_length(images, 1) > 0;
    
    -- Count rooms with new images
    SELECT COUNT(*) INTO new_count 
    FROM public.rooms 
    WHERE images_new IS NOT NULL AND jsonb_array_length(images_new) > 0;
    
    RAISE NOTICE 'Verification Results:';
    RAISE NOTICE '- Rooms with legacy images: %', legacy_count;
    RAISE NOTICE '- Rooms with new images: %', new_count;
    
    IF legacy_count = new_count THEN
        RAISE NOTICE '✅ Conversion successful! All rooms converted.';
    ELSE
        RAISE NOTICE '⚠️  Mismatch detected. Please review the conversion.';
    END IF;
    
    -- Show a sample of the converted data
    SELECT room_number, images_new 
    INTO sample_room
    FROM public.rooms 
    WHERE images_new IS NOT NULL AND jsonb_array_length(images_new) > 0 
    LIMIT 1;
    
    IF FOUND THEN
        RAISE NOTICE 'Sample converted room %: %', sample_room.room_number, sample_room.images_new;
    END IF;
END;
$$;

-- Step 5: Create helper functions for working with the new format

-- Function to get image URLs from RoomImage objects (for backward compatibility)
CREATE OR REPLACE FUNCTION get_room_image_urls(room_images JSONB)
RETURNS TEXT[]
LANGUAGE sql
IMMUTABLE
AS $$
    SELECT ARRAY(
        SELECT jsonb_extract_path_text(value, 'url')
        FROM jsonb_array_elements(room_images)
    );
$$;

-- Function to add a new image to a room
CREATE OR REPLACE FUNCTION add_room_image(
    room_id UUID,
    image_url TEXT,
    alt_text TEXT DEFAULT NULL
)
RETURNS JSONB
LANGUAGE plpgsql
AS $$
DECLARE
    current_images JSONB;
    new_image JSONB;
    image_id TEXT;
    room_number TEXT;
BEGIN
    -- Get current images and room number
    SELECT COALESCE(images_new, '[]'::jsonb), rooms.room_number 
    INTO current_images, room_number
    FROM public.rooms 
    WHERE id = room_id;
    
    IF NOT FOUND THEN
        RAISE EXCEPTION 'Room with ID % not found', room_id;
    END IF;
    
    -- Generate unique image ID
    image_id := 'img_' || 
               extract(epoch from now())::bigint || '_' || 
               (jsonb_array_length(current_images) + 1) || '_' || 
               substr(md5(random()::text || room_id::text), 1, 8);
    
    -- Create new image object
    new_image := jsonb_build_object(
        'id', image_id,
        'url', image_url,
        'alt_text', COALESCE(alt_text, 'Room ' || room_number || ' image'),
        'upload_date', now()::text,
        'file_name', 'room-' || room_number || '-' || (jsonb_array_length(current_images) + 1) || '.jpg'
    );
    
    -- Add to existing images
    current_images := current_images || new_image;
    
    -- Update the room
    UPDATE public.rooms 
    SET images_new = current_images 
    WHERE id = room_id;
    
    RETURN new_image;
END;
$$;

-- Function to remove an image from a room by image ID
CREATE OR REPLACE FUNCTION remove_room_image(
    room_id UUID,
    image_id TEXT
)
RETURNS BOOLEAN
LANGUAGE plpgsql
AS $$
DECLARE
    current_images JSONB;
    updated_images JSONB := '[]'::jsonb;
    image_obj JSONB;
    found_image BOOLEAN := FALSE;
BEGIN
    -- Get current images
    SELECT COALESCE(images_new, '[]'::jsonb) INTO current_images
    FROM public.rooms 
    WHERE id = room_id;
    
    IF NOT FOUND THEN
        RAISE EXCEPTION 'Room with ID % not found', room_id;
    END IF;
    
    -- Filter out the image with the specified ID
    FOR image_obj IN SELECT * FROM jsonb_array_elements(current_images)
    LOOP
        IF image_obj->>'id' = image_id THEN
            found_image := TRUE;
        ELSE
            updated_images := updated_images || image_obj;
        END IF;
    END LOOP;
    
    IF found_image THEN
        -- Update the room with filtered images
        UPDATE public.rooms 
        SET images_new = updated_images 
        WHERE id = room_id;
        
        RETURN TRUE;
    ELSE
        RETURN FALSE;
    END IF;
END;
$$;

-- Step 6: Create a view that uses the new format
CREATE OR REPLACE VIEW rooms_enhanced AS
SELECT 
    id,
    room_number,
    room_type,
    price_per_night,
    description,
    amenities,
    max_occupancy,
    COALESCE(images_new, '[]'::jsonb) as images,
    images as images_legacy,
    status,
    created_at,
    updated_at
FROM public.rooms;

-- Grant permissions on the view
GRANT SELECT ON rooms_enhanced TO authenticated;
GRANT SELECT ON rooms_enhanced TO anon;

-- Step 7: Show final results
SELECT 
    'Room Images Migration Summary' as status,
    COUNT(*) as total_rooms,
    COUNT(*) FILTER (WHERE images_new IS NOT NULL AND jsonb_array_length(images_new) > 0) as rooms_with_new_images,
    SUM(jsonb_array_length(images_new)) FILTER (WHERE images_new IS NOT NULL) as total_images_converted
FROM public.rooms;

-- Show sample of converted data
SELECT 
    room_number,
    array_length(images, 1) as legacy_image_count,
    jsonb_array_length(images_new) as new_image_count,
    jsonb_pretty(images_new) as sample_new_format
FROM public.rooms 
WHERE images_new IS NOT NULL AND jsonb_array_length(images_new) > 0
LIMIT 3;

-- Final completion message
DO $$
BEGIN
    RAISE NOTICE '✅ Room images migration script completed successfully!';
    RAISE NOTICE '📋 Next steps:';
    RAISE NOTICE '1. Review the verification results above';
    RAISE NOTICE '2. Update your app to use: supabase.from("rooms_enhanced")';
    RAISE NOTICE '3. Or use images_new column directly';
    RAISE NOTICE '4. When ready, you can replace the original images column';
    RAISE NOTICE '🎉 Your room images now have unique IDs!';
END;
$$;
