/**
 * Date utility functions for the hotel management system
 */

/**
 * Normalizes a date string to YYYY-MM-DD format
 * Handles both 'YYYY-MM-DD' and ISO datetime formats
 */
export const normalizeDate = (dateStr: string | null | undefined): string => {
  if (!dateStr) return '';
  // Handle both 'YYYY-MM-DD' and 'YYYY-MM-DDTHH:mm:ss.sssZ' formats
  return dateStr.split('T')[0];
};

/**
 * Gets today's date in YYYY-MM-DD format
 */
export const getTodayDateString = (): string => {
  return new Date().toISOString().split('T')[0];
};

/**
 * Checks if a date string represents today
 */
export const isToday = (dateStr: string | null | undefined): boolean => {
  const normalizedDate = normalizeDate(dateStr);
  const today = getTodayDateString();
  return normalizedDate === today;
};

/**
 * Checks if a date is within the current month
 */
export const isCurrentMonth = (dateStr: string | null | undefined): boolean => {
  if (!dateStr) return false;
  
  const date = new Date(dateStr);
  const today = new Date();
  
  return (
    date.getMonth() === today.getMonth() &&
    date.getFullYear() === today.getFullYear()
  );
};

/**
 * Formats a date for display
 */
export const formatDateForDisplay = (dateStr: string | null | undefined): string => {
  if (!dateStr) return '';
  
  const date = new Date(dateStr);
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
};

/**
 * Checks if a reservation status is considered active for check-in counting
 */
export const isActiveCheckInStatus = (status: string): boolean => {
  return status === 'confirmed' || status === 'checked_in';
};

/**
 * Checks if a reservation status is considered active for check-out counting
 */
export const isActiveCheckOutStatus = (status: string): boolean => {
  return status === 'checked_in' || status === 'checked_out';
};
