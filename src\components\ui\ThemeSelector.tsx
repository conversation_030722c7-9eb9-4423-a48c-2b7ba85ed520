import React from 'react';
import { View, StyleSheet } from 'react-native';
import { List, RadioButton, Text, Card } from 'react-native-paper';
import { Ionicons } from '@expo/vector-icons';
import { useTheme, ThemeMode } from '../../contexts/ThemeContext';

interface ThemeSelectorProps {
  style?: any;
}

export const ThemeSelector: React.FC<ThemeSelectorProps> = ({ style }) => {
  const { themeMode, setThemeMode, colors, isDark } = useTheme();

  const themeOptions: { value: ThemeMode; label: string; icon: string; description: string }[] = [
    {
      value: 'light',
      label: 'Light',
      icon: 'sunny',
      description: 'Always use light theme'
    },
    {
      value: 'dark',
      label: 'Dark',
      icon: 'moon',
      description: 'Always use dark theme'
    },
    {
      value: 'system',
      label: 'System',
      icon: 'phone-portrait',
      description: 'Follow system setting'
    }
  ];

  return (
    <Card style={[styles.container, style]}>
      <Card.Content>
        <View style={styles.header}>
          <Ionicons 
            name="color-palette" 
            size={24} 
            color={colors.primary} 
            style={styles.headerIcon}
          />
          <Text variant="titleMedium" style={[styles.title, { color: colors.text }]}>
            Theme Preference
          </Text>
        </View>
        
        <RadioButton.Group
          onValueChange={(value) => setThemeMode(value as ThemeMode)}
          value={themeMode}
        >
          {themeOptions.map((option) => (
            <View key={option.value} style={styles.optionContainer}>
              <RadioButton.Item
                label=""
                value={option.value}
                style={styles.radioButton}
                labelStyle={{ color: colors.text }}
              />
              <View style={styles.optionContent}>
                <View style={styles.optionHeader}>
                  <Ionicons 
                    name={option.icon as any} 
                    size={20} 
                    color={colors.textSecondary} 
                    style={styles.optionIcon}
                  />
                  <Text 
                    variant="bodyLarge" 
                    style={[styles.optionLabel, { color: colors.text }]}
                  >
                    {option.label}
                  </Text>
                </View>
                <Text 
                  variant="bodySmall" 
                  style={[styles.optionDescription, { color: colors.textSecondary }]}
                >
                  {option.description}
                </Text>
              </View>
            </View>
          ))}
        </RadioButton.Group>

        <View style={[styles.currentThemeIndicator, { backgroundColor: colors.surfaceVariant }]}>
          <Ionicons 
            name={isDark ? "moon" : "sunny"} 
            size={16} 
            color={colors.primary} 
            style={styles.indicatorIcon}
          />
          <Text 
            variant="bodySmall" 
            style={[styles.indicatorText, { color: colors.textSecondary }]}
          >
            Currently using {isDark ? 'dark' : 'light'} theme
          </Text>
        </View>
      </Card.Content>
    </Card>
  );
};

const styles = StyleSheet.create({
  container: {
    margin: 16,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  headerIcon: {
    marginRight: 8,
  },
  title: {
    fontWeight: '600',
  },
  optionContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginVertical: 4,
  },
  radioButton: {
    marginRight: 8,
    marginTop: -8,
  },
  optionContent: {
    flex: 1,
    paddingTop: 8,
  },
  optionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  optionIcon: {
    marginRight: 8,
  },
  optionLabel: {
    fontWeight: '500',
  },
  optionDescription: {
    marginLeft: 28,
  },
  currentThemeIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    marginTop: 16,
  },
  indicatorIcon: {
    marginRight: 8,
  },
  indicatorText: {
    fontStyle: 'italic',
  },
});
