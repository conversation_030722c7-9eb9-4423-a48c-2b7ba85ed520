// Using require instead of import to avoid TypeScript errors with allowSyntheticDefaultImports
const React = require('react');
const { useState, useEffect } = React;
const ReactNative = require('react-native');
const {
  StyleSheet,
  Alert,
  View,
  ScrollView,
  TouchableOpacity,
} = ReactNative;
const ReactNativePaper = require('react-native-paper');
const {
  Text,
  Button,
  Card,
  Surface,
  Divider,
  ActivityIndicator,
  RadioButton,
} = ReactNativePaper;
const ReactNavigation = require('@react-navigation/native');
const { useRoute, useNavigation } = ReactNavigation;
import { Paystack } from 'react-native-paystack-webview';
const ExpoVectors = require('@expo/vector-icons');
const { MaterialIcons } = ExpoVectors;

import { useReservationStore } from '../../store/reservationStore';
import { useAuthStore } from '../../store/authStore';
import { paymentService } from '../../services/supabase';
import { Colors, Spacing, Typography, PAYSTACK_CONFIG } from '../../constants';
import { CustomButton } from '../../components/ui/CustomButton';
import { formatPrice, toCents } from '../../utils/currency';
import type { GuestNavigationProp } from '../../navigation/GuestNavigator';

interface RouteParams {
  reservationId: string;
  amount: number;
}

interface PaymentMethod {
  id: string;
  name: string;
  icon: string;
  description: string;
}

const PAYMENT_METHODS: PaymentMethod[] = [
  {
    id: 'mobile_money',
    name: 'M-Pesa',
    icon: 'phone-android',
    description: 'Pay with M-Pesa mobile money (Most popular in Kenya)'
  },
  {
    id: 'card',
    name: 'Credit/Debit Card',
    icon: 'credit-card',
    description: 'Pay securely with your card via Paystack'
  },
  {
    id: 'bank_transfer',
    name: 'Bank Transfer',
    icon: 'account-balance',
    description: 'Transfer directly from your bank account'
  },
  {
    id: 'ussd',
    name: 'USSD',
    icon: 'dialpad',
    description: 'Pay using your mobile banking USSD code'
  },
];

export const PaymentScreen = () => {
  const route = useRoute();
  const navigation = useNavigation();
  const { reservationId, amount } = route.params as RouteParams;
  
  const { user } = useAuthStore();
  const { selectedReservation, fetchReservationById } = useReservationStore();
  
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState('mobile_money');
  const [processing, setProcessing] = useState(false);
  const [paymentReference, setPaymentReference] = useState('');
  const [showPaystack, setShowPaystack] = useState(false);
  
  useEffect(() => {
    fetchReservationById(reservationId);
    generatePaymentReference();
  }, [reservationId]);

  const generatePaymentReference = () => {
    const ref = `HTV_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    setPaymentReference(ref);
  };



  const handlePayment = async () => {
    if (!user || !selectedReservation) {
      Alert.alert('Error', 'Missing user or reservation information');
      return;
    }

    setProcessing(true);

    // For M-Pesa, simulate the payment directly since WebView doesn't support it properly
    if (selectedPaymentMethod === 'mobile_money') {
      handleMpesaSimulation();
    } else {
      setShowPaystack(true);
    }
  };

  const handleMpesaSimulation = () => {
    Alert.alert(
      'M-Pesa Payment Simulation',
      'Since this is test mode and the WebView doesn\'t show M-Pesa properly, we\'ll simulate the M-Pesa payment flow.',
      [
        {
          text: 'Cancel',
          style: 'cancel',
          onPress: () => setProcessing(false)
        },
        {
          text: 'Simulate Success',
          onPress: () => simulateMpesaSuccess()
        },
        {
          text: 'Use WebView',
          onPress: () => setShowPaystack(true)
        }
      ]
    );
  };

  const simulateMpesaSuccess = async () => {
    try {
      // Simulate successful M-Pesa payment
      const mockResponse = {
        status: 'success',
        reference: paymentReference,
        transaction: `mpesa_${Date.now()}`,
        message: 'M-Pesa payment simulation successful'
      };

      await handlePaystackSuccess(mockResponse);
    } catch (error) {
      console.error('M-Pesa simulation error:', error);
      setProcessing(false);
    }
  };

  const handlePaystackSuccess = async (response: any) => {
    setShowPaystack(false);
    console.log('Paystack response:', response);

    try {
      if (response.status === 'success') {
        // Create payment record in database
        const paymentData = {
          reservation_id: reservationId,
          amount: amount,
          currency: 'KES',
          payment_method: selectedPaymentMethod as 'card' | 'bank_transfer' | 'cash' | 'pos' | 'mobile_money',
          paystack_reference: response.reference || paymentReference,
          paystack_transaction_id: response.transaction || response.reference,
          status: 'paid' as const,
          paid_at: new Date().toISOString(),
        };

        const { error: paymentError } = await paymentService.createPayment(paymentData);

        if (paymentError) {
          throw new Error(paymentError.message);
        }

        // Update reservation status to confirmed
        const { error: reservationError } = await paymentService.updateReservation(
          reservationId,
          { status: 'confirmed' }
        );

        if (reservationError) {
          console.warn('Failed to update reservation status:', reservationError);
        }

        Alert.alert(
          'Payment Successful!',
          'Your reservation has been confirmed. You will receive a confirmation email shortly.',
          [
            {
              text: 'Download Receipt',
              onPress: async () => {
                try {
                  const { receiptService } = await import('../../services/receiptService');
                  await receiptService.generateAndShareReceipt(reservationId);
                } catch (error) {
                  console.error('Error generating receipt:', error);
                }
                navigation.popToTop();
              }
            },
            {
              text: 'Continue',
              onPress: () => {
                // Navigate back to the home screen
                navigation.popToTop();
              }
            }
          ]
        );
      } else {
        throw new Error('Payment failed');
      }
    } catch (error: any) {
      console.error('Payment error:', error);

      // Create failed payment record
      try {
        const failedPaymentData = {
          reservation_id: reservationId,
          amount: amount,
          currency: 'KES',
          payment_method: selectedPaymentMethod as 'card' | 'bank_transfer' | 'cash' | 'pos' | 'mobile_money',
          paystack_reference: paymentReference,
          status: 'failed' as const,
        };

        await paymentService.createPayment(failedPaymentData);
      } catch (recordError) {
        console.error('Failed to record failed payment:', recordError);
      }

      Alert.alert(
        'Payment Failed',
        error.message || 'An error occurred during payment. Please try again.',
        [
          { text: 'Retry', onPress: handlePayment },
          { text: 'Cancel', style: 'cancel' }
        ]
      );
    } finally {
      setProcessing(false);
    }
  };

  const handlePaystackCancel = () => {
    setShowPaystack(false);
    setProcessing(false);
  };
  // Note: react-native-paystack only supports card payments
  // Additional payment methods would require a different implementation

  const handleCancelPayment = () => {
    Alert.alert(
      'Cancel Payment',
      'Are you sure you want to cancel this payment? Your reservation will remain as pending.',
      [
        { text: 'Continue Payment', style: 'cancel' },
        {
          text: 'Cancel Payment',
          style: 'destructive',
          onPress: () => {
            navigation.goBack();
          }
        }
      ]
    );
  };

  if (!selectedReservation) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={Colors.primary} />
        <Text style={styles.loadingText}>Loading payment details...</Text>
      </View>
    );
  }

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      {/* Payment Summary */}
      <Card style={styles.summaryCard}>
        <Card.Content>
          <Text style={styles.sectionTitle}>Payment Summary</Text>
          
          <View style={styles.summaryRow}>
            <Text style={styles.summaryLabel}>Room:</Text>
            <Text style={styles.summaryValue}>
              Room {selectedReservation.room_number}
            </Text>
          </View>
          
          <View style={styles.summaryRow}>
            <Text style={styles.summaryLabel}>Check-in:</Text>
            <Text style={styles.summaryValue}>
              {new Date(selectedReservation.check_in_date || selectedReservation.check_in).toLocaleDateString()}
            </Text>
          </View>

          <View style={styles.summaryRow}>
            <Text style={styles.summaryLabel}>Check-out:</Text>
            <Text style={styles.summaryValue}>
              {new Date(selectedReservation.check_out_date || selectedReservation.check_out).toLocaleDateString()}
            </Text>
          </View>
          
          <View style={styles.summaryRow}>
            <Text style={styles.summaryLabel}>Guests:</Text>
            <Text style={styles.summaryValue}>{selectedReservation.guests}</Text>
          </View>
          
          <Divider style={styles.divider} />
          
          <View style={[styles.summaryRow, styles.totalRow]}>
            <Text style={styles.totalLabel}>Total Amount:</Text>
            <Text style={styles.totalValue}>{formatPrice(amount)}</Text>
          </View>
        </Card.Content>
      </Card>

      {/* Payment Methods */}
      <Card style={styles.paymentMethodsCard}>
        <Card.Content>
          <Text style={styles.sectionTitle}>Payment Method</Text>
          
          <RadioButton.Group
            onValueChange={setSelectedPaymentMethod}
            value={selectedPaymentMethod}
          >
            {PAYMENT_METHODS.map((method) => (
              <TouchableOpacity
                key={method.id}
                style={[
                  styles.paymentMethodItem,
                  selectedPaymentMethod === method.id && styles.selectedPaymentMethod
                ]}
                onPress={() => {
                  console.log('Selected payment method:', method.id);
                  setSelectedPaymentMethod(method.id);
                }}
                activeOpacity={0.7}
              >
                <View style={styles.paymentMethodContent}>
                  <MaterialIcons
                    name={method.icon as any}
                    size={24}
                    color={Colors.primary}
                  />
                  <View style={styles.paymentMethodText}>
                    <Text style={styles.paymentMethodName}>{method.name}</Text>
                    <Text style={styles.paymentMethodDescription}>
                      {method.description}
                    </Text>
                  </View>
                </View>
                <RadioButton value={method.id} />
              </TouchableOpacity>
            ))}
          </RadioButton.Group>
        </Card.Content>
      </Card>

      {/* Security Notice */}
      <Surface style={styles.securityNotice}>
        <View style={styles.securityContent}>
          <MaterialIcons name="security" size={24} color={Colors.success} />
          <View style={styles.securityText}>
            <Text style={styles.securityTitle}>Secure Payment</Text>
            <Text style={styles.securityDescription}>
              Your payment is processed securely through Paystack. 
              We never store your card details.
            </Text>
          </View>
        </View>
      </Surface>

      {/* Payment Reference */}
      <Surface style={styles.referenceCard}>
        <Text style={styles.referenceLabel}>Payment Reference:</Text>
        <Text style={styles.referenceValue}>{paymentReference}</Text>
      </Surface>

      {/* M-Pesa Instructions */}
      {selectedPaymentMethod === 'mobile_money' && (
        <Card style={styles.mpesaInstructionsCard}>
          <Card.Content>
            <Text style={styles.sectionTitle}>📱 M-Pesa Payment Options</Text>
            <Text style={styles.instructionText}>
              <Text style={{ fontWeight: 'bold', color: '#FF6B35' }}>TEST MODE:</Text> Choose your testing method{'\n\n'}

              <Text style={{ fontWeight: 'bold' }}>Option 1: Quick Simulation</Text>{'\n'}
              • Click "Simulate Success" for instant M-Pesa payment{'\n'}
              • Fastest way to test the complete flow{'\n\n'}

              <Text style={{ fontWeight: 'bold' }}>Option 2: WebView Testing</Text>{'\n'}
              • Use the Paystack WebView interface{'\n'}
              • Click "Success" on the test screen{'\n\n'}

              <Text style={{ fontWeight: 'bold', color: '#28A745' }}>🇰🇪 Both options simulate real M-Pesa payments!</Text>
            </Text>
          </Card.Content>
        </Card>
      )}

      {/* Action Buttons */}
      <View style={styles.buttonContainer}>      <CustomButton
          title={processing ? 'Processing Payment...' :
                 selectedPaymentMethod === 'mobile_money' ? `Pay ${formatPrice(amount)} with M-Pesa` :
                 `Pay ${formatPrice(amount)}`}
          onPress={handlePayment}
          variant="primary"
          loading={processing}
          disabled={processing}
          style={styles.payButton}
        />
        
        <CustomButton
          title="Cancel Payment"
          onPress={handleCancelPayment}
          variant="outline"
          disabled={processing}
          style={styles.cancelButton}
        />
      </View>

      {/* Terms and Conditions */}
      <View style={styles.termsContainer}>
        <Text style={styles.termsText}>
          By proceeding with payment, you agree to our Terms of Service and Privacy Policy.
          Cancellation policy applies as per hotel booking terms.
        </Text>
      </View>

      {/* Paystack WebView */}
      {showPaystack && (
        <Paystack
          paystackKey={PAYSTACK_CONFIG.publicKey}
          amount={toCents(amount)}
          billingEmail={user?.email || ''}
          billingName={user?.full_name || ''}
          currency="KES"
          reference={paymentReference}
          onCancel={handlePaystackCancel}
          onSuccess={handlePaystackSuccess}
          autoStart={true}
          billingMobile={user?.phone || '254700000000'}
        />
      )}
    </ScrollView>
  );
};

// No need to redefine CustomButtonProps as it's already defined in the component

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.background,
  },
  loadingText: {
    ...Typography.body,
    marginTop: Spacing.medium,
    color: Colors.textSecondary,
  },
  summaryCard: {
    margin: Spacing.medium,
    marginBottom: Spacing.small,
  },
  sectionTitle: {
    ...Typography.h4,
    fontWeight: 'bold',
    color: Colors.textPrimary,
    marginBottom: Spacing.medium,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.small,
  },
  summaryLabel: {
    ...Typography.body,
    color: Colors.textSecondary,
  },
  summaryValue: {
    ...Typography.body,
    color: Colors.textPrimary,
    fontWeight: '500',
  },
  divider: {
    marginVertical: Spacing.medium,
  },
  totalRow: {
    marginTop: Spacing.small,
    paddingTop: Spacing.small,
    borderTopWidth: 1,
    borderTopColor: Colors.border,
  },
  totalLabel: {
    ...Typography.h4,
    fontWeight: 'bold',
    color: Colors.textPrimary,
  },
  totalValue: {
    ...Typography.h4,
    fontWeight: 'bold',
    color: Colors.primary,
  },
  paymentMethodsCard: {
    margin: Spacing.medium,
    marginVertical: Spacing.small,
  },
  paymentMethodItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: Spacing.medium,
    paddingHorizontal: Spacing.small,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
    borderRadius: 8,
    marginVertical: 2,
  },
  selectedPaymentMethod: {
    backgroundColor: Colors.primary + '10', // 10% opacity
    borderColor: Colors.primary,
    borderWidth: 1,
  },
  paymentMethodContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  paymentMethodText: {
    marginLeft: Spacing.medium,
    flex: 1,
  },
  paymentMethodName: {
    ...Typography.subtitle,
    fontWeight: '500',
    color: Colors.textPrimary,
    marginBottom: 2,
  },
  paymentMethodDescription: {
    ...Typography.caption,
    color: Colors.textSecondary,
  },
  securityNotice: {
    margin: Spacing.medium,
    marginVertical: Spacing.small,
    padding: Spacing.medium,
    borderRadius: 8,
    elevation: 2,
  },
  securityContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  securityText: {
    marginLeft: Spacing.medium,
    flex: 1,
  },
  securityTitle: {
    ...Typography.subtitle,
    fontWeight: 'bold',
    color: Colors.success,
    marginBottom: 2,
  },
  securityDescription: {
    ...Typography.caption,
    color: Colors.textSecondary,
  },
  referenceCard: {
    margin: Spacing.medium,
    marginVertical: Spacing.small,
    padding: Spacing.medium,
    borderRadius: 8,
    elevation: 2,
  },
  referenceLabel: {
    ...Typography.caption,
    color: Colors.textSecondary,
  },
  referenceValue: {
    ...Typography.body,
    fontWeight: 'bold',
    color: Colors.textPrimary,
    marginTop: 2,
  },
  buttonContainer: {
    padding: Spacing.medium,
    gap: Spacing.medium,
  },
  payButton: {
    paddingVertical: Spacing.small,
  },
  cancelButton: {
    paddingVertical: Spacing.small,
  },
  termsContainer: {
    padding: Spacing.medium,
    paddingTop: 0,
    paddingBottom: Spacing.xxl, // Updated from extraLarge to xxl
  },
  termsText: {
    ...Typography.caption,
    color: Colors.textSecondary,
    textAlign: 'center',
    lineHeight: 18,
  },
  mpesaInstructionsCard: {
    margin: Spacing.medium,
    marginVertical: Spacing.small,
    backgroundColor: '#E8F5E8', // Light green background for M-Pesa
  },
  instructionText: {
    ...Typography.body,
    color: Colors.textPrimary,
    lineHeight: 22,
  },
});
