-- ============================================================================
-- DIAGNOSTIC SCRIPT: Check Room Images Data
-- This script will help us understand what's in your rooms table
-- ============================================================================

-- Step 1: Check the structure of your rooms table
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'rooms' 
AND table_schema = 'public'
ORDER BY ordinal_position;

-- Step 2: Check all rooms and their image data
SELECT 
    'Room Data Analysis' as analysis_type,
    COUNT(*) as total_rooms,
    COUNT(*) FILTER (WHERE images IS NOT NULL) as rooms_with_images_not_null,
    COUNT(*) FILTER (WHERE images IS NOT NULL AND array_length(images, 1) > 0) as rooms_with_actual_images,
    COUNT(*) FILTER (WHERE images = '{}') as rooms_with_empty_array,
    COUNT(*) FILTER (WHERE images IS NULL) as rooms_with_null_images
FROM public.rooms;

-- Step 3: Show sample room data to see what we're working with
SELECT 
    room_number,
    images,
    array_length(images, 1) as image_count,
    CASE 
        WHEN images IS NULL THEN 'NULL'
        WHEN images = '{}' THEN 'EMPTY_ARRAY'
        WHEN array_length(images, 1) > 0 THEN 'HAS_IMAGES'
        ELSE 'UNKNOWN'
    END as image_status
FROM public.rooms
ORDER BY room_number
LIMIT 10;

-- Step 4: Check if images column contains any data at all
SELECT 
    room_number,
    images,
    pg_typeof(images) as images_type,
    CASE 
        WHEN images IS NULL THEN 'Images column is NULL'
        WHEN images = '{}' THEN 'Images column is empty array'
        WHEN array_length(images, 1) IS NULL THEN 'Images array length is NULL'
        WHEN array_length(images, 1) = 0 THEN 'Images array length is 0'
        WHEN array_length(images, 1) > 0 THEN 'Images array has ' || array_length(images, 1) || ' items'
        ELSE 'Unknown state'
    END as diagnosis
FROM public.rooms
WHERE room_number IS NOT NULL
ORDER BY room_number;

-- Step 5: Check if there are any rooms with images in a different format
SELECT 
    'Checking for non-standard image formats' as check_type,
    room_number,
    images,
    length(images::text) as images_text_length,
    images::text as images_as_text
FROM public.rooms
WHERE images IS NOT NULL 
AND images::text != '{}'
LIMIT 5;

-- Step 6: Check if images might be stored in a different column
SELECT column_name 
FROM information_schema.columns 
WHERE table_name = 'rooms' 
AND table_schema = 'public'
AND (column_name ILIKE '%image%' OR column_name ILIKE '%photo%' OR column_name ILIKE '%picture%');

-- Step 7: Show the exact content of the first few rooms
SELECT 
    id,
    room_number,
    room_type,
    images,
    created_at
FROM public.rooms
ORDER BY created_at
LIMIT 5;

-- Step 8: Try to understand the data better
DO $$
DECLARE
    room_record RECORD;
    total_rooms INTEGER := 0;
    rooms_with_data INTEGER := 0;
BEGIN
    SELECT COUNT(*) INTO total_rooms FROM public.rooms;
    
    RAISE NOTICE 'Total rooms in database: %', total_rooms;
    
    FOR room_record IN 
        SELECT room_number, images, array_length(images, 1) as img_count
        FROM public.rooms 
        ORDER BY room_number
    LOOP
        IF room_record.images IS NOT NULL AND array_length(room_record.images, 1) > 0 THEN
            rooms_with_data := rooms_with_data + 1;
            RAISE NOTICE 'Room % has % images: %', room_record.room_number, room_record.img_count, room_record.images;
        ELSE
            RAISE NOTICE 'Room % has no images (images = %)', room_record.room_number, room_record.images;
        END IF;
    END LOOP;
    
    RAISE NOTICE 'Summary: % total rooms, % rooms with image data', total_rooms, rooms_with_data;
    
    IF rooms_with_data = 0 THEN
        RAISE NOTICE '🔍 DIAGNOSIS: No rooms have images in the images column.';
        RAISE NOTICE '💡 SOLUTION: You need to add some sample images first, or check if images are stored elsewhere.';
    END IF;
END;
$$;
