-- ============================================================================
-- FASTEST FIX: Add guest_name and guest_email columns to reservations
-- This adds the missing columns that the frontend expects
-- ============================================================================

-- Add the missing guest columns
ALTER TABLE public.reservations ADD COLUMN IF NOT EXISTS guest_name TEXT;
ALTER TABLE public.reservations ADD COLUMN IF NOT EXISTS guest_email TEXT;
ALTER TABLE public.reservations ADD COLUMN IF NOT EXISTS guests INTEGER DEFAULT 1;
ALTER TABLE public.reservations ADD COLUMN IF NOT EXISTS room_number TEXT;

-- Create a function to sync guest data
CREATE OR REPLACE FUNCTION sync_guest_data_in_reservations()
RETURNS TRIGGER AS $$
BEGIN
    -- When a reservation is inserted or updated, populate guest info
    IF TG_OP = 'INSERT' OR TG_OP = 'UPDATE' THEN
        -- Get guest information
        SELECT u.full_name, u.email 
        INTO NEW.guest_name, NEW.guest_email
        FROM public.users u 
        WHERE u.id = NEW.guest_id;
        
        -- Get room number
        SELECT r.room_number 
        INTO NEW.room_number
        FROM public.rooms r 
        WHERE r.id = NEW.room_id;
        
        RETURN NEW;
    END IF;
    
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to auto-populate guest data
DROP TRIGGER IF EXISTS trigger_sync_guest_data ON public.reservations;
CREATE TRIGGER trigger_sync_guest_data
    BEFORE INSERT OR UPDATE ON public.reservations
    FOR EACH ROW
    EXECUTE FUNCTION sync_guest_data_in_reservations();

-- Populate existing reservations with guest data
UPDATE public.reservations 
SET 
    guest_name = u.full_name,
    guest_email = u.email,
    room_number = r.room_number
FROM public.users u, public.rooms r
WHERE 
    public.reservations.guest_id = u.id 
    AND public.reservations.room_id = r.id
    AND (public.reservations.guest_name IS NULL OR public.reservations.guest_email IS NULL OR public.reservations.room_number IS NULL);

-- Done!
SELECT '✅ FIXED! Guest columns added to reservations table!' as status;

-- Show the updated structure
SELECT 
    column_name,
    data_type,
    is_nullable
FROM information_schema.columns 
WHERE table_name = 'reservations' 
    AND table_schema = 'public'
    AND column_name IN ('guest_name', 'guest_email', 'guests', 'room_number')
ORDER BY column_name;
