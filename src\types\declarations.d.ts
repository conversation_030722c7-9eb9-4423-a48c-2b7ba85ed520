// Global type declarations for modules without type definitions

// React Native types for better compatibility
declare module 'react-native' {
  export * from 'react-native';
}

// For image imports with require()
declare module '*.png';
declare module '*.jpg';
declare module '*.jpeg';
declare module '*.gif';
declare module '*.svg';

// For modules without type definitions
declare module 'react-native-paper' {
  import { ComponentType } from 'react';
  import { ViewStyle, TextStyle } from 'react-native';

  export interface ButtonProps {
    mode?: 'text' | 'outlined' | 'contained' | 'elevated' | 'contained-tonal';
    children: React.ReactNode;
    onPress?: () => void;
    style?: ViewStyle;
    textColor?: string;
    icon?: string | ComponentType<any>;
    compact?: boolean;
    loading?: boolean;
    disabled?: boolean;
    buttonColor?: string;
    contentStyle?: ViewStyle;
    labelStyle?: TextStyle;
  }

  export interface CardProps {
    children: React.ReactNode;
    style?: ViewStyle;
    mode?: 'elevated' | 'outlined' | 'contained';
  }

  export interface ChipProps {
    children: React.ReactNode;
    style?: ViewStyle;
    textStyle?: TextStyle;
    mode?: 'flat' | 'outlined';
    selected?: boolean;
    onPress?: () => void;
    icon?: string;
    compact?: boolean;
    onClose?: () => void;
  }

  export interface SearchbarProps {
    placeholder?: string;
    value: string;
    onChangeText: (text: string) => void;
    style?: ViewStyle;
    iconColor?: string;
  }

  export interface MenuProps {
    visible: boolean;
    onDismiss: () => void;
    anchor: React.ReactNode;
    children: React.ReactNode;
  }

  export interface DialogProps {
    visible: boolean;
    onDismiss: () => void;
    children: React.ReactNode;
  }

  export interface FABProps {
    icon: string;
    onPress: () => void;
    style?: ViewStyle;
    label?: string;
  }

  export interface PortalProps {
    children: React.ReactNode;
  }

  export interface TextProps {
    children: React.ReactNode;
    style?: TextStyle;
    variant?: 'displayLarge' | 'displayMedium' | 'displaySmall' | 'headlineLarge' | 'headlineMedium' | 'headlineSmall' | 'titleLarge' | 'titleMedium' | 'titleSmall' | 'labelLarge' | 'labelMedium' | 'labelSmall' | 'bodyLarge' | 'bodyMedium' | 'bodySmall';
  }

  export const Button: ComponentType<ButtonProps>;
  export const Card: ComponentType<CardProps> & {
    Content: ComponentType<{ children: React.ReactNode; style?: ViewStyle }>;
    Actions: ComponentType<{ children: ReactNode; style?: ViewStyle }>;
  };
  export const Chip: ComponentType<ChipProps>;
  export const Searchbar: ComponentType<SearchbarProps>;
  export const Menu: ComponentType<MenuProps> & {
    Item: ComponentType<{ title: string; onPress: () => void; leadingIcon?: string }>;
  };
  export const Dialog: ComponentType<DialogProps> & {
    Title: ComponentType<{ children: React.ReactNode }>;
    Content: ComponentType<{ children: React.ReactNode }>;
    Actions: ComponentType<{ children: React.ReactNode }>;
    ScrollArea: ComponentType<{ children: React.ReactNode }>;
  };
  export const FAB: ComponentType<FABProps>;
  export const Portal: ComponentType<PortalProps>;
  export const Divider: ComponentType<{ style?: ViewStyle }>;
  export const Text: ComponentType<TextProps>;
  export const Modal: ComponentType<{ visible: boolean; onDismiss: () => void; children: ReactNode; contentContainerStyle?: ViewStyle }>;
  export const Title: ComponentType<{ children: ReactNode; style?: TextStyle }>;
  export const Badge: ComponentType<{ children: ReactNode; style?: ViewStyle; size?: number }>;
  export const Paragraph: ComponentType<{ children: ReactNode; style?: TextStyle }>;
  export const List: ComponentType<any> & {
    Item: ComponentType<{ title: string; description?: string; left?: (props: any) => ReactNode; right?: (props: any) => ReactNode; onPress?: () => void }>;
    Section: ComponentType<{ title?: string; children: ReactNode }>;
    Icon: ComponentType<{ icon: string; [key: string]: any }>;
  };
  export const Switch: ComponentType<{ value: boolean; onValueChange: (value: boolean) => void; style?: ViewStyle }>;
  export const TextInput: ComponentType<{ label?: string; value: string; onChangeText: (text: string) => void; mode?: string; style?: ViewStyle; multiline?: boolean; numberOfLines?: number }>;
  export const RadioButton: ComponentType<any> & {
    Group: ComponentType<{ onValueChange: (value: string) => void; value: string; children: ReactNode }>;
    Item: ComponentType<{ label: string; value: string; style?: ViewStyle; labelStyle?: TextStyle }>;
  };
}

declare module '@expo/vector-icons' {
  export * from '@expo/vector-icons';
}

// Add any other modules that need declarations
