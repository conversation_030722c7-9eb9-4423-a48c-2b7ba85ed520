/**
 * String utility functions for safe operations
 */

/**
 * Safely capitalize the first letter of a string
 * @param str - String to capitalize
 * @returns Capitalized string or empty string if invalid
 */
export const capitalizeString = (str: string | undefined | null): string => {
  if (!str || typeof str !== 'string' || str.length === 0) {
    return '';
  }
  return str.charAt(0).toUpperCase() + str.slice(1);
};

/**
 * Get initials from a name safely
 * @param name - Full name string
 * @returns Initials (max 2 characters) or 'G' for Guest if invalid
 */
export const getInitials = (name: string | undefined | null): string => {
  if (!name || typeof name !== 'string') {
    return 'G'; // Default to 'G' for Guest
  }
  
  const nameParts = name.trim().split(' ').filter(part => part.length > 0);
  if (nameParts.length === 0) {
    return 'G';
  }
  
  return nameParts
    .map(part => part.charAt(0).toUpperCase())
    .join('')
    .slice(0, 2); // Limit to 2 characters for avatar
};

/**
 * Format status text safely (replace underscores and capitalize)
 * @param status - Status string
 * @returns Formatted status text or 'Unknown' if invalid
 */
export const formatStatusText = (status: string | undefined | null): string => {
  if (!status || typeof status !== 'string') {
    return 'Unknown';
  }
  return status.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase());
};

/**
 * Safely get room type display text
 * @param type - Room type string
 * @returns Capitalized room type or 'N/A' if invalid
 */
export const formatRoomType = (type: string | undefined | null): string => {
  if (!type || typeof type !== 'string') {
    return 'N/A';
  }
  return capitalizeString(type);
};

/**
 * Safely get room status display text
 * @param status - Room status
 * @param isAvailable - Room availability flag
 * @returns Formatted status text
 */
export const formatRoomStatus = (status: string | undefined | null, isAvailable?: boolean): string => {
  if (!isAvailable) return 'Occupied';
  if (!status || typeof status !== 'string') {
    return 'Unknown';
  }
  return capitalizeString(status);
};

/**
 * Truncate string to specified length with ellipsis
 * @param str - String to truncate
 * @param maxLength - Maximum length
 * @returns Truncated string
 */
export const truncateString = (str: string | undefined | null, maxLength: number): string => {
  if (!str || typeof str !== 'string') {
    return '';
  }
  if (str.length <= maxLength) {
    return str;
  }
  return str.slice(0, maxLength - 3) + '...';
};

/**
 * Check if string is valid and not empty
 * @param str - String to check
 * @returns True if valid and not empty
 */
export const isValidString = (str: string | undefined | null): str is string => {
  return typeof str === 'string' && str.trim().length > 0;
};

/**
 * Safely convert string to lowercase
 * @param str - String to convert
 * @returns Lowercase string or empty string if invalid
 */
export const safeToLowerCase = (str: string | undefined | null): string => {
  if (!str || typeof str !== 'string') {
    return '';
  }
  return str.toLowerCase();
};

/**
 * Safely check if string includes substring (case insensitive)
 * @param str - String to search in
 * @param searchTerm - Term to search for
 * @returns True if found, false otherwise. Returns true if searchTerm is empty.
 */
export const safeIncludes = (str: string | undefined | null, searchTerm: string | undefined | null): boolean => {
  // If no search term or empty search term, consider it a match
  if (!searchTerm || searchTerm.trim() === '') {
    return true;
  }

  // If no string to search in, no match
  if (!str || typeof str !== 'string') {
    return false;
  }

  // If searchTerm is not a string, no match
  if (typeof searchTerm !== 'string') {
    return false;
  }

  return str.toLowerCase().includes(searchTerm.toLowerCase());
};
