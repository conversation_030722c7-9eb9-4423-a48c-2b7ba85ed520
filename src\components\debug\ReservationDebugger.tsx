import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { <PERSON>, Button, Divider } from 'react-native-paper';
import { useAuthStore } from '../../store/authStore';
import { useReservationStore } from '../../store/reservationStore';
import { usePermissions } from '../../hooks/usePermissions';
import { supabase } from '../../services/supabase';
import { colors, spacing, typography } from '../../constants';

interface ReservationDebuggerProps {
  visible?: boolean;
}

export const ReservationDebugger: React.FC<ReservationDebuggerProps> = ({ visible = true }) => {
  const { user, session } = useAuthStore();
  const { reservations, loading, error } = useReservationStore();
  const permissions = usePermissions();
  const [debugInfo, setDebugInfo] = useState<any>(null);
  const [expanded, setExpanded] = useState(false);

  useEffect(() => {
    if (visible) {
      gatherDebugInfo();
    }
  }, [visible, user, reservations]);

  const gatherDebugInfo = async () => {
    try {
      // Get session info
      const { data: sessionData } = await supabase.auth.getSession();
      
      // Try to query reservations directly
      const { data: directReservations, error: directError } = await supabase
        .from('reservations')
        .select('*')
        .limit(5);

      // Try to query with relations
      const { data: relatedReservations, error: relatedError } = await supabase
        .from('reservations')
        .select(`
          *,
          guest:users!guest_id(*),
          room:rooms(*),
          payment:payments(*)
        `)
        .limit(5);

      setDebugInfo({
        user: {
          id: user?.id,
          email: user?.email,
          role: user?.role,
          full_name: user?.full_name,
        },
        session: {
          exists: !!session,
          user_id: session?.user?.id,
          role: session?.user?.role,
          metadata: session?.user?.user_metadata,
        },
        permissions: {
          canViewAllReservations: permissions.canViewAllReservations,
          canManageReservations: permissions.canManageReservations,
          isAdmin: permissions.isAdmin,
          isStaff: permissions.isStaff,
        },
        store: {
          reservationsCount: reservations.length,
          loading,
          error,
        },
        directQuery: {
          success: !directError,
          error: directError?.message,
          count: directReservations?.length || 0,
          data: directReservations?.slice(0, 2), // Show first 2 for debugging
        },
        relatedQuery: {
          success: !relatedError,
          error: relatedError?.message,
          count: relatedReservations?.length || 0,
          data: relatedReservations?.slice(0, 2), // Show first 2 for debugging
        },
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      setDebugInfo({
        error: 'Failed to gather debug info',
        details: error,
        timestamp: new Date().toISOString(),
      });
    }
  };

  const testReservationAccess = async () => {
    console.log('🧪 Testing reservation access...');
    
    try {
      // Test 1: Simple count query
      const { count, error: countError } = await supabase
        .from('reservations')
        .select('*', { count: 'exact', head: true });
      
      console.log('📊 Count query result:', { count, error: countError });

      // Test 2: Full query with relations
      const { data, error } = await supabase
        .from('reservations')
        .select(`
          *,
          guest:users!guest_id(*),
          room:rooms(*),
          payment:payments(*)
        `)
        .limit(10);

      console.log('📊 Full query result:', { data, error, count: data?.length });

      // Test 3: Check RLS policies
      const { data: policies } = await supabase
        .rpc('get_table_policies', { table_name: 'reservations' })
        .single();

      console.log('🔒 RLS policies:', policies);

    } catch (error) {
      console.error('❌ Test failed:', error);
    }
  };

  if (!visible || !debugInfo) return null;

  return (
    <Card style={styles.container}>
      <Card.Content>
        <View style={styles.header}>
          <Text style={styles.title}>🐛 Reservation Debug Info</Text>
          <TouchableOpacity onPress={() => setExpanded(!expanded)}>
            <Text style={styles.toggle}>{expanded ? '▼' : '▶'}</Text>
          </TouchableOpacity>
        </View>

        {expanded && (
          <ScrollView style={styles.content}>
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>👤 User Info</Text>
              <Text style={styles.debugText}>
                {JSON.stringify(debugInfo.user, null, 2)}
              </Text>
            </View>

            <Divider style={styles.divider} />

            <View style={styles.section}>
              <Text style={styles.sectionTitle}>🔐 Session Info</Text>
              <Text style={styles.debugText}>
                {JSON.stringify(debugInfo.session, null, 2)}
              </Text>
            </View>

            <Divider style={styles.divider} />

            <View style={styles.section}>
              <Text style={styles.sectionTitle}>🔑 Permissions</Text>
              <Text style={styles.debugText}>
                {JSON.stringify(debugInfo.permissions, null, 2)}
              </Text>
            </View>

            <Divider style={styles.divider} />

            <View style={styles.section}>
              <Text style={styles.sectionTitle}>🏪 Store State</Text>
              <Text style={styles.debugText}>
                {JSON.stringify(debugInfo.store, null, 2)}
              </Text>
            </View>

            <Divider style={styles.divider} />

            <View style={styles.section}>
              <Text style={styles.sectionTitle}>📊 Direct Query</Text>
              <Text style={styles.debugText}>
                {JSON.stringify(debugInfo.directQuery, null, 2)}
              </Text>
            </View>

            <Divider style={styles.divider} />

            <View style={styles.section}>
              <Text style={styles.sectionTitle}>🔗 Related Query</Text>
              <Text style={styles.debugText}>
                {JSON.stringify(debugInfo.relatedQuery, null, 2)}
              </Text>
            </View>

            <View style={styles.actions}>
              <Button mode="outlined" onPress={gatherDebugInfo} style={styles.button}>
                🔄 Refresh Debug Info
              </Button>
              <Button mode="outlined" onPress={testReservationAccess} style={styles.button}>
                🧪 Test Access
              </Button>
            </View>
          </ScrollView>
        )}
      </Card.Content>
    </Card>
  );
};

const styles = StyleSheet.create({
  container: {
    margin: spacing.md,
    backgroundColor: colors.surface,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  title: {
    ...typography.subtitle1,
    fontWeight: 'bold',
    color: colors.primary,
  },
  toggle: {
    ...typography.body1,
    color: colors.primary,
    fontSize: 18,
  },
  content: {
    maxHeight: 400,
    marginTop: spacing.md,
  },
  section: {
    marginVertical: spacing.sm,
  },
  sectionTitle: {
    ...typography.subtitle2,
    fontWeight: 'bold',
    marginBottom: spacing.xs,
    color: colors.onSurface,
  },
  debugText: {
    ...typography.caption,
    fontFamily: 'monospace',
    backgroundColor: colors.background,
    padding: spacing.sm,
    borderRadius: 4,
    color: colors.onBackground,
  },
  divider: {
    marginVertical: spacing.sm,
  },
  actions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginTop: spacing.md,
  },
  button: {
    flex: 1,
    marginHorizontal: spacing.xs,
  },
});
