-- ============================================================================
-- FIX ROOMS SCHEMA - ADD MISSING FIELDS
-- This script adds missing fields to the rooms table that the frontend expects
-- ============================================================================

-- ============================================================================
-- STEP 1: Add missing columns to rooms table
-- ============================================================================

-- Add bed_type column if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'rooms' AND column_name = 'bed_type') THEN
        ALTER TABLE public.rooms ADD COLUMN bed_type TEXT DEFAULT 'Queen Bed';
        RAISE NOTICE 'Added bed_type column';
    ELSE
        RAISE NOTICE 'bed_type column already exists';
    END IF;
END $$;

-- Add size_sqm column if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'rooms' AND column_name = 'size_sqm') THEN
        ALTER TABLE public.rooms ADD COLUMN size_sqm DECIMAL(5,2) DEFAULT 25.0;
        RAISE NOTICE 'Added size_sqm column';
    ELSE
        RAISE NOTICE 'size_sqm column already exists';
    END IF;
END $$;

-- Add is_available column if it doesn't exist (computed from status)
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'rooms' AND column_name = 'is_available') THEN
        ALTER TABLE public.rooms ADD COLUMN is_available BOOLEAN DEFAULT true;
        RAISE NOTICE 'Added is_available column';
    ELSE
        RAISE NOTICE 'is_available column already exists';
    END IF;
END $$;

-- ============================================================================
-- STEP 2: Update existing rooms with default values
-- ============================================================================

-- Update bed_type based on room_type
UPDATE public.rooms 
SET bed_type = CASE 
    WHEN room_type = 'standard' THEN 'Double Bed'
    WHEN room_type = 'deluxe' THEN 'Queen Bed'
    WHEN room_type = 'suite' THEN 'King Bed'
    WHEN room_type = 'presidential' THEN 'King Bed'
    ELSE 'Queen Bed'
END
WHERE bed_type IS NULL OR bed_type = 'Queen Bed';

-- Update size_sqm based on room_type
UPDATE public.rooms 
SET size_sqm = CASE 
    WHEN room_type = 'standard' THEN 25.0
    WHEN room_type = 'deluxe' THEN 35.0
    WHEN room_type = 'suite' THEN 50.0
    WHEN room_type = 'presidential' THEN 80.0
    ELSE 25.0
END
WHERE size_sqm IS NULL OR size_sqm = 25.0;

-- Update is_available based on status
UPDATE public.rooms 
SET is_available = (status = 'available');

-- ============================================================================
-- STEP 3: Create/update trigger to keep is_available in sync with status
-- ============================================================================

-- Drop existing trigger if it exists
DROP TRIGGER IF EXISTS trigger_sync_room_availability ON public.rooms;
DROP FUNCTION IF EXISTS sync_room_availability();

-- Create function to sync is_available with status
CREATE OR REPLACE FUNCTION sync_room_availability()
RETURNS TRIGGER AS $$
BEGIN
    -- When a room status is updated, update is_available accordingly
    IF TG_OP = 'INSERT' OR TG_OP = 'UPDATE' THEN
        NEW.is_available := (NEW.status = 'available');
        RETURN NEW;
    END IF;
    
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to auto-sync is_available
CREATE TRIGGER trigger_sync_room_availability
    BEFORE INSERT OR UPDATE ON public.rooms
    FOR EACH ROW
    EXECUTE FUNCTION sync_room_availability();

-- ============================================================================
-- STEP 4: Verify the fix
-- ============================================================================

-- Show updated rooms with all columns
SELECT 
    'Updated Rooms with All Fields' as info,
    r.id,
    r.room_number,
    r.room_type,
    r.bed_type,
    r.size_sqm,
    r.price_per_night,
    r.max_occupancy,
    r.status,
    r.is_available,
    r.description,
    r.amenities,
    array_length(r.images, 1) as image_count,
    r.created_at
FROM public.rooms r
ORDER BY r.room_number;

-- Test the trigger by updating a room status
UPDATE public.rooms 
SET status = 'maintenance'
WHERE room_number = (SELECT room_number FROM public.rooms LIMIT 1);

-- Show final state after trigger test
SELECT 
    'Final State After Trigger Test' as info,
    r.room_number,
    r.status,
    r.is_available,
    'Should be false for maintenance status' as note
FROM public.rooms r
WHERE r.status = 'maintenance'
LIMIT 1;

-- Reset the test room back to available
UPDATE public.rooms 
SET status = 'available'
WHERE status = 'maintenance';

-- Instructions
DO $$
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '✅ ROOMS SCHEMA FIXED!';
    RAISE NOTICE '';
    RAISE NOTICE '📋 WHAT WAS ADDED:';
    RAISE NOTICE '- bed_type column with appropriate defaults';
    RAISE NOTICE '- size_sqm column with room-type-based defaults';
    RAISE NOTICE '- is_available column synced with status';
    RAISE NOTICE '- Trigger to keep is_available in sync with status';
    RAISE NOTICE '';
    RAISE NOTICE '🔄 NEXT STEPS:';
    RAISE NOTICE '1. Refresh the admin rooms screen in your app';
    RAISE NOTICE '2. All room data should now display correctly';
    RAISE NOTICE '3. Check the console logs for any remaining issues';
END;
$$;
