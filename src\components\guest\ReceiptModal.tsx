import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  Alert,
  Image,
  Dimensions,
} from 'react-native';
import {
  Modal,
  Portal,
  Card,
  Title,
  Text,
  Button,
  Divider,
  ActivityIndicator,
  Surface,
  Chip,
} from 'react-native-paper';
import { MaterialIcons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { colors, spacing, typography, APP_CONSTANTS } from '../../constants';
import { receiptService, ReceiptData } from '../../services/receiptService';
import { formatPrice } from '../../utils/currency';

const { width: screenWidth } = Dimensions.get('window');

interface ReceiptModalProps {
  visible: boolean;
  onDismiss: () => void;
  reservationId: string;
  paymentId?: string;
}

export const ReceiptModal: React.FC<ReceiptModalProps> = ({
  visible,
  onDismiss,
  reservationId,
  paymentId,
}) => {
  const [receiptData, setReceiptData] = useState<ReceiptData | null>(null);
  const [loading, setLoading] = useState(false);
  const [downloading, setDownloading] = useState(false);

  useEffect(() => {
    if (visible && reservationId) {
      fetchReceiptData();
    }
  }, [visible, reservationId, paymentId]);

  const fetchReceiptData = async () => {
    try {
      setLoading(true);
      const data = await receiptService.getReceiptData(reservationId, paymentId);
      setReceiptData(data);
    } catch (error) {
      console.error('Error fetching receipt data:', error);
      Alert.alert('Error', 'Failed to load receipt data');
    } finally {
      setLoading(false);
    }
  };

  const handleDownloadReceipt = async () => {
    if (!receiptData) return;

    try {
      setDownloading(true);
      await receiptService.generateAndShareReceipt(reservationId, paymentId);
      Alert.alert(
        'Success',
        'Receipt has been generated and is ready to share!',
        [{ text: 'OK' }]
      );
    } catch (error) {
      console.error('Error downloading receipt:', error);
      Alert.alert(
        'Error',
        'Failed to generate receipt. Please try again.',
        [{ text: 'OK' }]
      );
    } finally {
      setDownloading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'paid':
        return colors.success;
      case 'confirmed':
        return colors.primary;
      case 'pending':
        return colors.warning;
      case 'checked_in':
        return colors.info;
      case 'checked_out':
        return colors.onSurfaceVariant;
      case 'cancelled':
        return colors.error;
      case 'failed':
        return colors.error;
      case 'refunded':
        return colors.info;
      default:
        return colors.outline;
    }
  };

  const getStatusTextColor = (status: string) => {
    switch (status) {
      case 'paid':
        return '#FFFFFF'; // White text on green
      case 'confirmed':
        return '#FFFFFF'; // White text on primary
      case 'pending':
        return '#000000'; // Black text on yellow/orange
      case 'checked_in':
        return '#FFFFFF'; // White text on blue
      case 'checked_out':
        return '#FFFFFF'; // White text on gray
      case 'cancelled':
        return '#FFFFFF'; // White text on red
      case 'failed':
        return '#FFFFFF'; // White text on red
      case 'refunded':
        return '#FFFFFF'; // White text on blue
      default:
        return '#FFFFFF'; // White text on gray
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'paid':
        return 'Paid';
      case 'confirmed':
        return 'Confirmed';
      case 'pending':
        return 'Pending';
      case 'checked_in':
        return 'Checked In';
      case 'checked_out':
        return 'Checked Out';
      case 'cancelled':
        return 'Cancelled';
      case 'failed':
        return 'Failed';
      case 'refunded':
        return 'Refunded';
      default:
        return status?.charAt(0).toUpperCase() + status?.slice(1) || 'Unknown';
    }
  };

  if (loading) {
    return (
      <Portal>
        <Modal
          visible={visible}
          onDismiss={onDismiss}
          contentContainerStyle={styles.modalContainer}
        >
          <Card style={styles.loadingCard}>
            <Card.Content style={styles.loadingContent}>
              <ActivityIndicator size="large" color={colors.primary} />
              <Text style={styles.loadingText}>Loading receipt...</Text>
            </Card.Content>
          </Card>
        </Modal>
      </Portal>
    );
  }

  if (!receiptData) {
    return (
      <Portal>
        <Modal
          visible={visible}
          onDismiss={onDismiss}
          contentContainerStyle={styles.modalContainer}
        >
          <Card style={styles.errorCard}>
            <Card.Content style={styles.errorContent}>
              <MaterialIcons name="error-outline" size={48} color={colors.error} />
              <Text style={styles.errorText}>Receipt not found</Text>
              <Button mode="contained" onPress={onDismiss}>
                Close
              </Button>
            </Card.Content>
          </Card>
        </Modal>
      </Portal>
    );
  }

  const { reservation, payment, receiptNumber, nights } = receiptData;

  return (
    <Portal>
      <Modal
        visible={visible}
        onDismiss={onDismiss}
        contentContainerStyle={styles.modalContainer}
      >
        <Card style={styles.card}>
          <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
            {/* Enhanced Header with Logo and Branding */}
            <LinearGradient
              colors={[colors.primary, colors.primaryDark]}
              style={styles.headerGradient}
            >
              <View style={styles.headerContent}>
                {/* Hotel Logo */}
                <View style={styles.logoContainer}>
                  <Image
                    source={require('../../../assets/icon.png')}
                    style={styles.logo}
                    resizeMode="contain"
                  />
                  <View style={styles.hotelInfo}>
                    <Text style={styles.hotelName}>{APP_CONSTANTS.APP_NAME}</Text>
                    <Text style={styles.hotelTagline}>Luxury • Comfort • Excellence</Text>
                  </View>
                </View>

                {/* Receipt Badge */}
                <Surface style={styles.receiptBadge}>
                  <MaterialIcons name="receipt-long" size={20} color={colors.primary} />
                  <Text style={styles.receiptLabel}>RECEIPT</Text>
                  <Text style={styles.receiptNumber}>#{receiptNumber}</Text>
                </Surface>
              </View>

              {/* Contact Information */}
              <View style={styles.contactInfo}>
                <View style={styles.contactRow}>
                  <MaterialIcons name="phone" size={16} color={colors.white} />
                  <Text style={styles.contactText}>{APP_CONSTANTS.SUPPORT_PHONE}</Text>
                </View>
                <View style={styles.contactRow}>
                  <MaterialIcons name="email" size={16} color={colors.white} />
                  <Text style={styles.contactText}>{APP_CONSTANTS.SUPPORT_EMAIL}</Text>
                </View>
                <View style={styles.contactRow}>
                  <MaterialIcons name="location-on" size={16} color={colors.white} />
                  <Text style={styles.contactText}>Mombasa, Kenya</Text>
                </View>
              </View>
            </LinearGradient>

            <Card.Content style={styles.content}>
              {/* Receipt Information with Enhanced Design */}
              <Surface style={styles.infoSection}>
                <View style={styles.sectionHeader}>
                  <MaterialIcons name="info" size={20} color={colors.primary} />
                  <Text style={styles.sectionTitle}>Receipt Information</Text>
                </View>
                <View style={styles.infoGrid}>
                  <View style={styles.infoItem}>
                    <Text style={styles.infoLabel}>Date Issued</Text>
                    <Text style={styles.infoValue}>
                      {new Date(payment.paid_at || payment.created_at).toLocaleDateString('en-US', {
                        weekday: 'short',
                        year: 'numeric',
                        month: 'short',
                        day: 'numeric'
                      })}
                    </Text>
                  </View>
                  <View style={styles.infoItem}>
                    <Text style={styles.infoLabel}>Payment Method</Text>
                    <Text style={styles.infoValue}>
                      {payment.payment_method.replace('_', ' ').toUpperCase()}
                    </Text>
                  </View>
                  <View style={styles.infoItem}>
                    <Text style={styles.infoLabel}>Transaction ID</Text>
                    <Text style={styles.infoValue}>
                      {payment.paystack_reference || payment.id.slice(-8)}
                    </Text>
                  </View>
                  <View style={styles.infoItem}>
                    <Text style={styles.infoLabel}>Time Issued</Text>
                    <Text style={styles.infoValue}>
                      {new Date(payment.paid_at || payment.created_at).toLocaleTimeString('en-US', {
                        hour: '2-digit',
                        minute: '2-digit'
                      })}
                    </Text>
                  </View>
                </View>
              </Surface>

              <Divider style={styles.divider} />

              {/* Guest Information with Enhanced Design */}
              <Surface style={styles.infoSection}>
                <View style={styles.sectionHeader}>
                  <MaterialIcons name="person" size={20} color={colors.primary} />
                  <Text style={styles.sectionTitle}>Guest Information</Text>
                </View>
                <View style={styles.guestCard}>
                  <View style={styles.guestAvatar}>
                    <MaterialIcons name="account-circle" size={40} color={colors.primary} />
                  </View>
                  <View style={styles.guestDetails}>
                    <Text style={styles.guestName}>{reservation.guest.full_name}</Text>
                    <View style={styles.guestContactRow}>
                      <MaterialIcons name="email" size={16} color={colors.onSurfaceVariant} />
                      <Text style={styles.guestContact}>{reservation.guest.email}</Text>
                    </View>
                    {reservation.guest.phone && (
                      <View style={styles.guestContactRow}>
                        <MaterialIcons name="phone" size={16} color={colors.onSurfaceVariant} />
                        <Text style={styles.guestContact}>{reservation.guest.phone}</Text>
                      </View>
                    )}
                  </View>
                </View>
              </Surface>

              <Divider style={styles.divider} />

              {/* Reservation Details with Enhanced Design */}
              <Surface style={styles.infoSection}>
                <View style={styles.sectionHeader}>
                  <MaterialIcons name="hotel" size={20} color={colors.primary} />
                  <Text style={styles.sectionTitle}>Reservation Details</Text>
                </View>

                {/* Reservation ID Card */}
                <Surface style={styles.reservationCard}>
                  <Text style={styles.reservationIdLabel}>Reservation ID</Text>
                  <Text style={styles.reservationId}>#{reservation.id.slice(-8).toUpperCase()}</Text>
                </Surface>

                {/* Room Information */}
                <View style={styles.roomInfoCard}>
                  <View style={styles.roomHeader}>
                    <MaterialIcons name="bed" size={24} color={colors.primary} />
                    <View style={styles.roomDetails}>
                      <Text style={styles.roomNumber}>Room {reservation.room.room_number}</Text>
                      <Text style={styles.roomType}>
                        {reservation.room.room_type.charAt(0).toUpperCase() +
                         reservation.room.room_type.slice(1)} Room
                      </Text>
                    </View>
                  </View>
                </View>

                {/* Stay Dates */}
                <View style={styles.datesContainer}>
                  <View style={styles.dateCard}>
                    <MaterialIcons name="login" size={20} color={colors.success} />
                    <View style={styles.dateInfo}>
                      <Text style={styles.dateLabel}>Check-in</Text>
                      <Text style={styles.dateValue}>
                        {new Date(reservation.check_in_date).toLocaleDateString('en-US', {
                          weekday: 'short',
                          month: 'short',
                          day: 'numeric',
                          year: 'numeric'
                        })}
                      </Text>
                    </View>
                  </View>

                  <View style={styles.dateCard}>
                    <MaterialIcons name="logout" size={20} color={colors.error} />
                    <View style={styles.dateInfo}>
                      <Text style={styles.dateLabel}>Check-out</Text>
                      <Text style={styles.dateValue}>
                        {new Date(reservation.check_out_date).toLocaleDateString('en-US', {
                          weekday: 'short',
                          month: 'short',
                          day: 'numeric',
                          year: 'numeric'
                        })}
                      </Text>
                    </View>
                  </View>
                </View>

                {/* Nights Badge */}
                <View style={styles.nightsBadge}>
                  <Text style={styles.nightsText}>
                    {nights} night{nights > 1 ? 's' : ''}
                  </Text>
                </View>

                {/* Status Badges */}
                <View style={styles.statusContainer}>
                  <View style={styles.statusItem}>
                    <Text style={styles.statusLabel}>Reservation</Text>
                    <View style={[
                      styles.statusBadge,
                      { backgroundColor: getStatusColor(reservation.status) }
                    ]}>
                      <Text style={[
                        styles.statusText,
                        { color: getStatusTextColor(reservation.status) }
                      ]}>
                        {getStatusText(reservation.status)}
                      </Text>
                    </View>
                  </View>

                  <View style={styles.statusItem}>
                    <Text style={styles.statusLabel}>Payment</Text>
                    <View style={[
                      styles.statusBadge,
                      { backgroundColor: getStatusColor(payment.status) }
                    ]}>
                      <Text style={[
                        styles.statusText,
                        { color: getStatusTextColor(payment.status) }
                      ]}>
                        {getStatusText(payment.status)}
                      </Text>
                    </View>
                  </View>
                </View>
              </Surface>

              <Divider style={styles.divider} />

              {/* Enhanced Payment Summary */}
              <LinearGradient
                colors={[colors.primaryContainer || colors.surfaceVariant, colors.surface]}
                style={styles.paymentSummary}
              >
                <View style={styles.sectionHeader}>
                  <MaterialIcons name="payment" size={20} color={colors.primary} />
                  <Text style={styles.sectionTitle}>Payment Summary</Text>
                </View>

                <View style={styles.paymentBreakdown}>
                  <View style={styles.paymentRow}>
                    <Text style={styles.paymentLabel}>
                      Room Rate ({nights} night{nights > 1 ? 's' : ''})
                    </Text>
                    <Text style={styles.paymentValue}>{formatPrice(reservation.total_amount)}</Text>
                  </View>

                  {/* Add taxes/fees if applicable */}
                  <View style={styles.paymentRow}>
                    <Text style={styles.paymentLabel}>Service Charge</Text>
                    <Text style={styles.paymentValue}>Included</Text>
                  </View>

                  <View style={styles.paymentRow}>
                    <Text style={styles.paymentLabel}>VAT (16%)</Text>
                    <Text style={styles.paymentValue}>Included</Text>
                  </View>
                </View>

                <Divider style={styles.summaryDivider} />

                <View style={styles.totalContainer}>
                  <Text style={styles.totalLabel}>Total Paid</Text>
                  <Text style={styles.totalValue}>{formatPrice(payment.amount)}</Text>
                </View>

                {/* Payment Method Badge */}
                <View style={styles.paymentMethodBadge}>
                  <MaterialIcons
                    name={payment.payment_method === 'mpesa' ? 'phone-android' : 'credit-card'}
                    size={16}
                    color={colors.primary}
                  />
                  <Text style={styles.paymentMethodText}>
                    Paid via {payment.payment_method.replace('_', ' ').toUpperCase()}
                  </Text>
                </View>
              </LinearGradient>

              {/* Footer with QR Code and Thank You Message */}
              <View style={styles.receiptFooter}>
                <View style={styles.qrSection}>
                  <Surface style={styles.qrPlaceholder}>
                    <MaterialIcons name="qr-code" size={40} color={colors.primary} />
                    <Text style={styles.qrText}>Digital Receipt</Text>
                  </Surface>
                </View>

                <View style={styles.thankYouSection}>
                  <Text style={styles.thankYouTitle}>Thank You!</Text>
                  <Text style={styles.thankYouMessage}>
                    We appreciate your business and hope you enjoy your stay at {APP_CONSTANTS.APP_NAME}.
                  </Text>
                  <Text style={styles.supportText}>
                    For any inquiries, please contact us at {APP_CONSTANTS.SUPPORT_EMAIL}
                  </Text>
                </View>
              </View>
            </Card.Content>

            {/* Action Buttons */}
            <Card.Actions style={styles.actions}>
              <Button
                mode="outlined"
                onPress={onDismiss}
                style={styles.button}
              >
                Close
              </Button>
              <Button
                mode="contained"
                onPress={handleDownloadReceipt}
                loading={downloading}
                disabled={downloading}
                style={styles.button}
                icon="download"
              >
                {downloading ? 'Generating...' : 'Download PDF'}
              </Button>
            </Card.Actions>
          </ScrollView>
        </Card>
      </Modal>
    </Portal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    margin: spacing.md,
  },
  card: {
    maxHeight: '90%',
    borderRadius: 16,
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
  },
  scrollView: {
    maxHeight: '100%',
  },
  loadingCard: {
    padding: spacing.xl,
  },
  loadingContent: {
    alignItems: 'center',
  },
  loadingText: {
    marginTop: spacing.md,
    ...typography.body1,
  },
  errorCard: {
    padding: spacing.xl,
  },
  errorContent: {
    alignItems: 'center',
  },
  errorText: {
    marginVertical: spacing.md,
    ...typography.body1,
    textAlign: 'center',
  },
  // Enhanced Header Styles
  headerGradient: {
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    paddingVertical: spacing.xl,
    paddingHorizontal: spacing.lg,
  },
  headerContent: {
    marginBottom: spacing.lg,
  },
  logoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  logo: {
    width: 50,
    height: 50,
    marginRight: spacing.md,
  },
  hotelInfo: {
    flex: 1,
  },
  hotelName: {
    fontSize: 24,
    fontWeight: 'bold',
    color: colors.white,
    marginBottom: 2,
  },
  hotelTagline: {
    fontSize: 12,
    color: colors.white,
    opacity: 0.9,
    fontStyle: 'italic',
  },
  receiptBadge: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: spacing.md,
    alignItems: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  receiptLabel: {
    fontSize: 12,
    fontWeight: 'bold',
    color: colors.primary,
    marginTop: 4,
    letterSpacing: 1,
  },
  receiptNumber: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.primary,
    marginTop: 2,
  },
  contactInfo: {
    gap: spacing.sm,
  },
  contactRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.sm,
  },
  contactText: {
    color: colors.white,
    fontSize: 12,
    opacity: 0.9,
  },
  content: {
    padding: spacing.lg,
  },
  // Enhanced Section Styles
  infoSection: {
    marginBottom: spacing.lg,
    borderRadius: 12,
    padding: spacing.lg,
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.primary,
    marginLeft: spacing.sm,
  },
  // Info Grid Layout
  infoGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: spacing.md,
  },
  infoItem: {
    flex: 1,
    minWidth: '45%',
  },
  infoLabel: {
    fontSize: 12,
    color: colors.onSurfaceVariant,
    marginBottom: 4,
    fontWeight: '500',
  },
  infoValue: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.onSurface,
  },
  // Guest Card Styles
  guestCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.surfaceVariant,
    borderRadius: 12,
    padding: spacing.md,
  },
  guestAvatar: {
    marginRight: spacing.md,
  },
  guestDetails: {
    flex: 1,
  },
  guestName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.onSurface,
    marginBottom: spacing.xs,
  },
  guestContactRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.xs,
    marginBottom: 2,
  },
  guestContact: {
    fontSize: 12,
    color: colors.onSurfaceVariant,
  },
  // Reservation Card Styles
  reservationCard: {
    backgroundColor: colors.primary,
    borderRadius: 8,
    padding: spacing.md,
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  reservationIdLabel: {
    fontSize: 12,
    color: colors.white,
    opacity: 0.8,
    marginBottom: 4,
  },
  reservationId: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.white,
    letterSpacing: 1,
  },
  // Room Info Styles
  roomInfoCard: {
    backgroundColor: colors.surfaceVariant,
    borderRadius: 8,
    padding: spacing.md,
    marginBottom: spacing.md,
  },
  roomHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  roomDetails: {
    marginLeft: spacing.md,
  },
  roomNumber: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.onSurface,
  },
  roomType: {
    fontSize: 12,
    color: colors.onSurfaceVariant,
    textTransform: 'capitalize',
  },
  // Dates Container
  datesContainer: {
    flexDirection: 'row',
    gap: spacing.md,
    marginBottom: spacing.md,
  },
  dateCard: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.surfaceVariant,
    borderRadius: 8,
    padding: spacing.sm,
  },
  dateInfo: {
    marginLeft: spacing.sm,
  },
  dateLabel: {
    fontSize: 10,
    color: colors.onSurfaceVariant,
    marginBottom: 2,
  },
  dateValue: {
    fontSize: 12,
    fontWeight: '600',
    color: colors.onSurface,
  },
  // Nights Badge
  nightsBadge: {
    alignSelf: 'center',
    backgroundColor: colors.accent,
    borderRadius: 16,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.xs,
    marginBottom: spacing.md,
  },
  nightsText: {
    fontSize: 12,
    fontWeight: 'bold',
    color: colors.onPrimary,
  },
  // Status Container
  statusContainer: {
    flexDirection: 'row',
    gap: spacing.md,
  },
  statusItem: {
    flex: 1,
    alignItems: 'center',
  },
  statusLabel: {
    fontSize: 10,
    color: colors.onSurfaceVariant,
    marginBottom: spacing.xs,
  },
  statusBadge: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 12,
    minWidth: 60,
    alignItems: 'center',
    justifyContent: 'center',
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 1,
  },
  statusText: {
    fontSize: 10,
    fontWeight: '700',
    textAlign: 'center',
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  divider: {
    marginVertical: spacing.lg,
  },
  // Enhanced Payment Summary Styles
  paymentSummary: {
    borderRadius: 12,
    padding: spacing.lg,
    marginBottom: spacing.lg,
  },
  paymentBreakdown: {
    marginBottom: spacing.md,
  },
  paymentRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: spacing.xs,
  },
  paymentLabel: {
    fontSize: 14,
    color: colors.onSurfaceVariant,
  },
  paymentValue: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.onSurface,
  },
  summaryDivider: {
    marginVertical: spacing.md,
    backgroundColor: colors.primary,
    height: 2,
  },
  totalContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: colors.primary,
    borderRadius: 8,
    padding: spacing.md,
    marginBottom: spacing.md,
  },
  totalLabel: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.white,
  },
  totalValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.white,
  },
  paymentMethodBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.surfaceVariant,
    borderRadius: 8,
    padding: spacing.sm,
    gap: spacing.xs,
  },
  paymentMethodText: {
    fontSize: 12,
    fontWeight: '600',
    color: colors.primary,
  },
  // Receipt Footer Styles
  receiptFooter: {
    marginTop: spacing.lg,
    paddingTop: spacing.lg,
    borderTopWidth: 1,
    borderTopColor: colors.outline,
  },
  qrSection: {
    alignItems: 'center',
    marginBottom: spacing.lg,
  },
  qrPlaceholder: {
    width: 80,
    height: 80,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.surfaceVariant,
    elevation: 1,
  },
  qrText: {
    fontSize: 10,
    color: colors.primary,
    marginTop: 4,
    fontWeight: '500',
  },
  thankYouSection: {
    alignItems: 'center',
    paddingHorizontal: spacing.md,
  },
  thankYouTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.primary,
    marginBottom: spacing.sm,
  },
  thankYouMessage: {
    fontSize: 14,
    color: colors.onSurface,
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: spacing.sm,
  },
  supportText: {
    fontSize: 12,
    color: colors.onSurfaceVariant,
    textAlign: 'center',
    fontStyle: 'italic',
  },
  // Action Buttons
  actions: {
    padding: spacing.lg,
    justifyContent: 'space-between',
    backgroundColor: colors.surfaceVariant,
    borderBottomLeftRadius: 16,
    borderBottomRightRadius: 16,
  },
  button: {
    flex: 1,
    marginHorizontal: spacing.xs,
  },
});
