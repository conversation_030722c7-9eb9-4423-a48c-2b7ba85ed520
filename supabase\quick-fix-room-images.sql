-- ============================================================================
-- QUICK FIX: Room Images with Unique IDs
-- Safe script to run immediately - creates new column without affecting existing data
-- ============================================================================

-- Step 1: Add new column for enhanced images (safe - doesn't modify existing data)
ALTER TABLE public.rooms ADD COLUMN IF NOT EXISTS images_enhanced JSONB DEFAULT '[]'::jsonb;

-- Step 2: Convert existing images to new format with unique IDs
UPDATE public.rooms 
SET images_enhanced = (
    SELECT jsonb_agg(
        jsonb_build_object(
            'id', 'img_' || extract(epoch from now())::bigint || '_' || (row_number() OVER ()) || '_' || substr(md5(random()::text || id::text), 1, 8),
            'url', image_url,
            'alt_text', 'Room ' || room_number || ' image ' || (row_number() OVER ()),
            'upload_date', now()::text,
            'file_name', 'room-' || room_number || '-' || (row_number() OVER ()) || '.jpg'
        )
    )
    FROM unnest(images) WITH ORDINALITY AS t(image_url, ord)
)
WHERE images IS NOT NULL AND array_length(images, 1) > 0;

-- Step 3: Verify the conversion
SELECT 
    'Conversion Results' as status,
    COUNT(*) as total_rooms,
    COUNT(*) FILTER (WHERE images IS NOT NULL AND array_length(images, 1) > 0) as rooms_with_legacy_images,
    COUNT(*) FILTER (WHERE images_enhanced IS NOT NULL AND jsonb_array_length(images_enhanced) > 0) as rooms_with_enhanced_images,
    SUM(array_length(images, 1)) FILTER (WHERE images IS NOT NULL) as total_legacy_images,
    SUM(jsonb_array_length(images_enhanced)) FILTER (WHERE images_enhanced IS NOT NULL) as total_enhanced_images
FROM public.rooms;

-- Step 4: Show sample of converted data
SELECT 
    room_number,
    'Legacy format:' as format_type,
    array_length(images, 1) as image_count,
    images as sample_data
FROM public.rooms 
WHERE images IS NOT NULL AND array_length(images, 1) > 0
LIMIT 2

UNION ALL

SELECT 
    room_number,
    'Enhanced format:' as format_type,
    jsonb_array_length(images_enhanced) as image_count,
    images_enhanced as sample_data
FROM public.rooms 
WHERE images_enhanced IS NOT NULL AND jsonb_array_length(images_enhanced) > 0
LIMIT 2;

-- Step 5: Create helper functions for the new format

-- Function to get image by ID
CREATE OR REPLACE FUNCTION get_room_image_by_id(room_images JSONB, image_id TEXT)
RETURNS JSONB
LANGUAGE sql
IMMUTABLE
AS $$
    SELECT value
    FROM jsonb_array_elements(room_images) AS value
    WHERE value->>'id' = image_id
    LIMIT 1;
$$;

-- Function to get all image URLs (for backward compatibility)
CREATE OR REPLACE FUNCTION get_room_image_urls_enhanced(room_images JSONB)
RETURNS TEXT[]
LANGUAGE sql
IMMUTABLE
AS $$
    SELECT ARRAY(
        SELECT value->>'url'
        FROM jsonb_array_elements(room_images) AS value
    );
$$;

-- Function to count images
CREATE OR REPLACE FUNCTION count_room_images(room_images JSONB)
RETURNS INTEGER
LANGUAGE sql
IMMUTABLE
AS $$
    SELECT jsonb_array_length(room_images);
$$;

-- Step 6: Test the new functions
SELECT 
    room_number,
    count_room_images(images_enhanced) as image_count,
    get_room_image_urls_enhanced(images_enhanced) as image_urls,
    get_room_image_by_id(images_enhanced, (images_enhanced->0->>'id')) as first_image_details
FROM public.rooms 
WHERE images_enhanced IS NOT NULL AND jsonb_array_length(images_enhanced) > 0
LIMIT 3;

-- Step 7: Create a view that uses the enhanced format by default
CREATE OR REPLACE VIEW rooms_with_enhanced_images AS
SELECT 
    id,
    room_number,
    room_type,
    price_per_night,
    description,
    amenities,
    max_occupancy,
    COALESCE(images_enhanced, '[]'::jsonb) as images, -- Use enhanced images
    images as images_legacy, -- Keep legacy for reference
    status,
    created_at,
    updated_at
FROM public.rooms;

-- Grant permissions on the view
GRANT SELECT ON rooms_with_enhanced_images TO authenticated;
GRANT SELECT ON rooms_with_enhanced_images TO anon;

-- Step 8: Instructions for your application
SELECT '
🎉 ROOM IMAGES QUICK FIX COMPLETED!

✅ What was done:
- Added images_enhanced column with RoomImage objects containing unique IDs
- Converted all existing images to the new format
- Created helper functions for working with enhanced images
- Created a view that uses enhanced images by default

📋 Next steps for your application:

1. IMMEDIATE FIX (No code changes needed):
   Update your Supabase queries to use the new view:
   
   // Instead of: supabase.from("rooms")
   // Use: supabase.from("rooms_with_enhanced_images")
   
   This will automatically return images in the new format with unique IDs!

2. OPTIONAL (For better performance):
   Update your application code to use images_enhanced column directly:
   
   const { data } = await supabase
     .from("rooms")
     .select("*, images_enhanced as images")
   
3. VERIFICATION:
   Check that room images now have unique IDs in your app
   Each image should have: id, url, alt_text, upload_date, file_name

4. WHEN READY (Optional):
   You can replace the original images column with images_enhanced:
   
   -- Backup first!
   ALTER TABLE rooms RENAME COLUMN images TO images_backup;
   ALTER TABLE rooms RENAME COLUMN images_enhanced TO images;

🔧 Helper functions available:
- get_room_image_by_id(images, image_id) - Get specific image
- get_room_image_urls_enhanced(images) - Extract URLs only  
- count_room_images(images) - Count images

The missing image IDs issue is now FIXED! 🚀
' as instructions;
