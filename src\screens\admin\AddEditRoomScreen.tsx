import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Alert,
  Image,
  TouchableOpacity,
  TextInput,
  Switch,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import {
  Card,
  Button,
  Chip,
  Divider,
  Text as PaperText,
} from 'react-native-paper';
import { MaterialIcons } from '@expo/vector-icons';
import * as ImagePicker from 'expo-image-picker';
import { colors, spacing, typography } from '../../constants';
import { useRoomStore } from '../../store/roomStore';
import { RoomType, RoomStatus } from '../../types/database';
import { Room, RoomImage } from '../../types';
import { CustomButton } from '../../components/ui/CustomButton';
import { imageService } from '../../services/imageService';

interface AddEditRoomScreenProps {
  navigation: any;
  route: {
    params?: {
      room?: Room;
    };
  };
}

interface FormData {
  room_number: string;
  type: RoomType;
  price_per_night: string;
  max_occupancy: string;
  bed_type: string;
  size_sqm: string;
  description: string;
  is_available: boolean;
  status: RoomStatus;
  amenities: string[];
  images: RoomImage[];
}

interface FormErrors {
  room_number?: string;
  price_per_night?: string;
  max_occupancy?: string;
  size_sqm?: string;
  description?: string;
}

const roomTypes: { value: RoomType; label: string }[] = [
  { value: 'standard', label: 'Standard' },
  { value: 'deluxe', label: 'Deluxe' },
  { value: 'suite', label: 'Suite' },
  { value: 'presidential', label: 'Presidential' },
];

const availableAmenities = [
  'WiFi',
  'Air Conditioning',
  'TV',
  'Mini Bar',
  'Room Service',
  'Balcony',
  'Sea View',
  'City View',
  'Jacuzzi',
  'Safe',
  'Coffee Machine',
  'Kitchenette',
];

const bedTypes = [
  'Single Bed',
  'Double Bed',
  'Queen Bed',
  'King Bed',
  'Twin Beds',
  'Sofa Bed',
];

export const AddEditRoomScreen: React.FC<AddEditRoomScreenProps> = ({
  navigation,
  route,
}) => {
  const { room } = route.params || {};
  const { addRoom, updateRoom, loading } = useRoomStore();
  const isEdit = !!room;

  // Helper function to convert legacy images to RoomImage format
  const convertLegacyImages = (images: any[]): RoomImage[] => {
    if (!images || images.length === 0) return [];

    return images.map((image, index) => {
      if (typeof image === 'string') {
        // Legacy format - convert to RoomImage
        return {
          id: `legacy_${Date.now()}_${index}`,
          url: image,
          alt_text: `Room image ${index + 1}`,
          upload_date: new Date().toISOString(),
        };
      }
      // Already in RoomImage format
      return image;
    });
  };

  const [formData, setFormData] = useState<FormData>({
    room_number: room?.room_number || '',
    type: room?.room_type || 'standard',
    price_per_night: room?.price_per_night?.toString() || '',
    max_occupancy: room?.max_occupancy?.toString() || '',
    bed_type: room?.bed_type || 'Queen Bed',
    size_sqm: room?.size_sqm?.toString() || '',
    description: room?.description || '',
    is_available: room?.is_available ?? true,
    status: room?.status || 'available',
    amenities: room?.amenities || [],
    images: convertLegacyImages(room?.images || []),
  });

  const [errors, setErrors] = useState<FormErrors>({});
  const [imagePickerVisible, setImagePickerVisible] = useState(false);

  useEffect(() => {
    navigation.setOptions({
      title: isEdit ? 'Edit Room' : 'Add New Room',
    });
  }, [navigation, isEdit]);

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    if (!formData.room_number.trim()) {
      newErrors.room_number = 'Room number is required';
    }

    if (!formData.price_per_night.trim()) {
      newErrors.price_per_night = 'Price per night is required';
    } else if (isNaN(Number(formData.price_per_night)) || Number(formData.price_per_night) <= 0) {
      newErrors.price_per_night = 'Please enter a valid price';
    }

    if (!formData.max_occupancy.trim()) {
      newErrors.max_occupancy = 'Max occupancy is required';
    } else if (isNaN(Number(formData.max_occupancy)) || Number(formData.max_occupancy) <= 0) {
      newErrors.max_occupancy = 'Please enter a valid occupancy number';
    }

    if (!formData.size_sqm.trim()) {
      newErrors.size_sqm = 'Room size is required';
    } else if (isNaN(Number(formData.size_sqm)) || Number(formData.size_sqm) <= 0) {
      newErrors.size_sqm = 'Please enter a valid room size';
    }

    if (!formData.description.trim()) {
      newErrors.description = 'Description is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = async () => {
    if (!validateForm()) {
      Alert.alert('Validation Error', 'Please fix the errors in the form');
      return;
    }

    try {
      const roomData = {
        room_number: formData.room_number,
        type: formData.type,
        price_per_night: Number(formData.price_per_night),
        max_occupancy: Number(formData.max_occupancy),
        bed_type: formData.bed_type,
        size_sqm: Number(formData.size_sqm),
        description: formData.description,
        is_available: formData.is_available,
        status: formData.status,
        amenities: formData.amenities,
        images: formData.images,
      };

      if (isEdit && room) {
        await updateRoom(room.id, roomData);
      } else {
        await addRoom(roomData);
      }

      Alert.alert(
        'Success',
        `Room ${isEdit ? 'updated' : 'added'} successfully`,
        [{ text: 'OK', onPress: () => navigation.goBack() }]
      );
    } catch (error) {
      Alert.alert('Error', `Failed to ${isEdit ? 'update' : 'add'} room`);
    }
  };

  const pickImage = async () => {
    const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
    if (status !== 'granted') {
      Alert.alert('Permission needed', 'Please grant camera roll permissions to add images');
      return;
    }

    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [4, 3],
      quality: 0.8,
    });

    if (!result.canceled && result.assets[0]) {
      // Create a temporary RoomImage object for the new image
      const newImage: RoomImage = {
        id: `temp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        url: result.assets[0].uri,
        alt_text: `Room ${formData.room_number} image`,
        upload_date: new Date().toISOString(),
        file_name: `temp_${Date.now()}.jpg`,
        file_size: result.assets[0].fileSize,
      };

      setFormData(prev => ({
        ...prev,
        images: [...prev.images, newImage],
      }));
    }
  };

  const removeImage = (imageId: string) => {
    setFormData(prev => ({
      ...prev,
      images: prev.images.filter(image => image.id !== imageId),
    }));
  };

  const toggleAmenity = (amenity: string) => {
    setFormData(prev => ({
      ...prev,
      amenities: prev.amenities.includes(amenity)
        ? prev.amenities.filter(a => a !== amenity)
        : [...prev.amenities, amenity],
    }));
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView}>
        {/* Basic Information */}
        <Card style={styles.card}>
          <Card.Content>
            <PaperText variant="headlineSmall" style={styles.sectionTitle}>Basic Information</PaperText>
            
            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Room Number*</Text>
              <TextInput
                value={formData.room_number}
                onChangeText={(text) => setFormData(prev => ({ ...prev, room_number: text }))}
                style={[styles.textInput, errors.room_number && styles.textInputError]}
                placeholder="Enter room number"
              />
              {errors.room_number && (
                <Text style={styles.errorText}>{errors.room_number}</Text>
              )}
            </View>

            <Text style={styles.fieldLabel}>Room Type*</Text>
            <View style={styles.segmentedButtons}>
              {roomTypes.map(type => (
                <TouchableOpacity
                  key={type.value}
                  style={[
                    styles.segmentButton,
                    formData.type === type.value && styles.selectedSegmentButton
                  ]}
                  onPress={() => setFormData(prev => ({ ...prev, type: type.value }))}
                >
                  <Text style={[
                    styles.segmentButtonText,
                    formData.type === type.value && styles.selectedSegmentButtonText
                  ]}>
                    {type.label}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>

            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Price per Night (KSh)*</Text>
              <TextInput
                value={formData.price_per_night}
                onChangeText={(text) => setFormData(prev => ({ ...prev, price_per_night: text }))}
                keyboardType="numeric"
                style={[styles.textInput, errors.price_per_night && styles.textInputError]}
                placeholder="Enter price"
              />
              {errors.price_per_night && (
                <Text style={styles.errorText}>{errors.price_per_night}</Text>
              )}
            </View>

            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Max Occupancy*</Text>
              <TextInput
                value={formData.max_occupancy}
                onChangeText={(text) => setFormData(prev => ({ ...prev, max_occupancy: text }))}
                keyboardType="numeric"
                style={[styles.textInput, errors.max_occupancy && styles.textInputError]}
                placeholder="Enter max occupancy"
              />
              {errors.max_occupancy && (
                <Text style={styles.errorText}>{errors.max_occupancy}</Text>
              )}
            </View>

            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Room Size (sqm)*</Text>
              <TextInput
                value={formData.size_sqm}
                onChangeText={(text) => setFormData(prev => ({ ...prev, size_sqm: text }))}
                keyboardType="numeric"
                style={[styles.textInput, errors.size_sqm && styles.textInputError]}
                placeholder="Enter room size"
              />
              {errors.size_sqm && (
                <Text style={styles.errorText}>{errors.size_sqm}</Text>
              )}
            </View>
          </Card.Content>
        </Card>

        {/* Bed Type */}
        <Card style={styles.card}>
          <Card.Content>
            <PaperText variant="headlineSmall" style={styles.sectionTitle}>Bed Configuration</PaperText>
            
            <Text style={styles.fieldLabel}>Bed Type</Text>
            <ScrollView horizontal showsHorizontalScrollIndicator={false}>
              <View style={styles.chipContainer}>
                {bedTypes.map((bedType) => (
                  <Chip
                    key={bedType}
                    selected={formData.bed_type === bedType}
                    onPress={() => setFormData(prev => ({ ...prev, bed_type: bedType }))}
                    style={[
                      styles.chip,
                      formData.bed_type === bedType && styles.selectedChip
                    ]}
                    textStyle={[
                      styles.chipText,
                      formData.bed_type === bedType && styles.selectedChipText
                    ]}
                  >
                    {bedType}
                  </Chip>
                ))}
              </View>
            </ScrollView>
          </Card.Content>
        </Card>

        {/* Amenities */}
        <Card style={styles.card}>
          <Card.Content>
            <PaperText variant="headlineSmall" style={styles.sectionTitle}>Amenities</PaperText>
            
            <View style={styles.amenitiesContainer}>
              {availableAmenities.map((amenity) => (
                <Chip
                  key={amenity}
                  selected={formData.amenities.includes(amenity)}
                  onPress={() => toggleAmenity(amenity)}
                  style={[
                    styles.amenityChip,
                    formData.amenities.includes(amenity) && styles.selectedAmenityChip
                  ]}
                  textStyle={[
                    styles.amenityChipText,
                    formData.amenities.includes(amenity) && styles.selectedAmenityChipText
                  ]}
                >
                  {amenity}
                </Chip>
              ))}
            </View>
          </Card.Content>
        </Card>

        {/* Description */}
        <Card style={styles.card}>
          <Card.Content>
            <PaperText variant="headlineSmall" style={styles.sectionTitle}>Description</PaperText>
            
            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Room Description*</Text>
              <TextInput
                value={formData.description}
                onChangeText={(text) => setFormData(prev => ({ ...prev, description: text }))}
                multiline
                numberOfLines={4}
                style={[styles.textArea, errors.description && styles.textInputError]}
                placeholder="Enter room description"
              />
              {errors.description && (
                <Text style={styles.errorText}>{errors.description}</Text>
              )}
            </View>
          </Card.Content>
        </Card>

        {/* Images */}
        <Card style={styles.card}>
          <Card.Content>
            <PaperText variant="headlineSmall" style={styles.sectionTitle}>Room Images</PaperText>
            
            <ScrollView horizontal showsHorizontalScrollIndicator={false}>
              <View style={styles.imagesContainer}>
                {formData.images.map((image, index) => (
                  <View key={image.id} style={styles.imageContainer}>
                    <Image
                      source={{ uri: image.url }}
                      style={styles.roomImage}
                      accessibilityLabel={image.alt_text || `Room image ${index + 1}`}
                    />
                    <TouchableOpacity
                      style={styles.removeImageButton}
                      onPress={() => removeImage(image.id)}
                    >
                      <MaterialIcons name="close" size={20} color={colors.surface} />
                    </TouchableOpacity>
                  </View>
                ))}

                <TouchableOpacity style={styles.addImageButton} onPress={pickImage}>
                  <MaterialIcons name="add-a-photo" size={32} color={colors.primary} />
                  <Text style={styles.addImageText}>Add Photo</Text>
                </TouchableOpacity>
              </View>
            </ScrollView>
          </Card.Content>
        </Card>

        {/* Status */}
        <Card style={styles.card}>
          <Card.Content>
            <PaperText variant="headlineSmall" style={styles.sectionTitle}>Room Status</PaperText>
            
            <View style={styles.switchContainer}>
              <Text style={styles.switchLabel}>Available for Booking</Text>
              <Switch
                value={formData.is_available}
                onValueChange={(value) => setFormData(prev => ({ ...prev, is_available: value }))}
                trackColor={{ false: colors.outline, true: colors.primary }}
                thumbColor={formData.is_available ? colors.onPrimary : colors.surface}
              />
            </View>

            <Text style={styles.fieldLabel}>Current Status</Text>
            <View style={styles.segmentedButtons}>
              {[
                { value: 'available', label: 'Available' },
                { value: 'maintenance', label: 'Maintenance' },
                { value: 'cleaning', label: 'Cleaning' },
              ].map(status => (
                <TouchableOpacity
                  key={status.value}
                  style={[
                    styles.segmentButton,
                    formData.status === status.value && styles.selectedSegmentButton
                  ]}
                  onPress={() => setFormData(prev => ({ ...prev, status: status.value as RoomStatus }))}
                >
                  <Text style={[
                    styles.segmentButtonText,
                    formData.status === status.value && styles.selectedSegmentButtonText
                  ]}>
                    {status.label}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </Card.Content>
        </Card>

        {/* Action Buttons */}
        <View style={styles.actionsContainer}>
          <CustomButton
            title={isEdit ? 'Update Room' : 'Add Room'}
            onPress={handleSave}
            loading={loading}
            variant="primary"
            style={styles.saveButton}
          />
          
          <CustomButton
            title="Cancel"
            onPress={() => navigation.goBack()}
            variant="outline"
            style={styles.cancelButton}
          />
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  scrollView: {
    flex: 1,
  },
  card: {
    margin: spacing.lg,
    marginBottom: spacing.md,
  },
  sectionTitle: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.semibold as any,
    marginBottom: spacing.md,
    color: colors.primary,
  },
  input: {
    marginBottom: spacing.sm,
  },
  fieldLabel: {
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.medium as any,
    color: colors.onSurface,
    marginBottom: spacing.sm,
    marginTop: spacing.sm,
  },
  segmentedButtons: {
    flexDirection: 'row',
    marginBottom: spacing.md,
    borderRadius: 8,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: colors.outline || colors.border,
  },
  chipContainer: {
    flexDirection: 'row',
    gap: spacing.sm,
    paddingRight: spacing.lg,
  },
  chip: {
    marginRight: spacing.sm,
    backgroundColor: colors.surfaceVariant,
    borderWidth: 1,
    borderColor: colors.outline || colors.border,
  },
  selectedChip: {
    backgroundColor: colors.primary,
    borderColor: colors.primary,
  },
  chipText: {
    color: colors.onSurfaceVariant,
    fontSize: typography.sizes.sm,
  },
  selectedChipText: {
    color: colors.onPrimary || colors.surface,
    fontWeight: typography.weights.medium as any,
  },
  amenitiesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: spacing.sm,
  },
  amenityChip: {
    marginBottom: spacing.sm,
    backgroundColor: colors.surfaceVariant,
    borderWidth: 1,
    borderColor: colors.outline || colors.border,
  },
  selectedAmenityChip: {
    backgroundColor: colors.primary,
    borderColor: colors.primary,
  },
  amenityChipText: {
    color: colors.onSurfaceVariant,
    fontSize: typography.sizes.sm,
  },
  selectedAmenityChipText: {
    color: colors.onPrimary || colors.surface,
    fontWeight: typography.weights.medium as any,
  },
  imagesContainer: {
    flexDirection: 'row',
    gap: spacing.md,
    paddingRight: spacing.lg,
  },
  imageContainer: {
    position: 'relative',
  },
  roomImage: {
    width: 120,
    height: 90,
    borderRadius: 8,
  },
  removeImageButton: {
    position: 'absolute',
    top: -8,
    right: -8,
    backgroundColor: colors.error,
    borderRadius: 12,
    width: 24,
    height: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  addImageButton: {
    width: 120,
    height: 90,
    borderRadius: 8,
    borderWidth: 2,
    borderColor: colors.primary,
    borderStyle: 'dashed',
    alignItems: 'center',
    justifyContent: 'center',
  },
  addImageText: {
    fontSize: typography.sizes.sm,
    color: colors.primary,
    marginTop: spacing.xs,
  },
  switchContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  switchLabel: {
    fontSize: typography.sizes.md,
    color: colors.onSurface,
  },
  actionsContainer: {
    padding: spacing.lg,
    gap: spacing.md,
  },
  saveButton: {
    marginBottom: spacing.sm,
  },
  cancelButton: {},
  errorText: {
    color: colors.error,
    fontSize: typography.sizes.sm,
    marginTop: spacing.xs,
    marginBottom: spacing.sm,
  },
  segmentButton: {
    flex: 1,
    paddingVertical: spacing.md,
    paddingHorizontal: spacing.sm,
    backgroundColor: colors.surface,
    alignItems: 'center',
    justifyContent: 'center',
    borderRightWidth: 1,
    borderRightColor: colors.outline || colors.border,
  },
  selectedSegmentButton: {
    backgroundColor: colors.primary,
  },
  segmentButtonText: {
    fontSize: typography.sizes.sm,
    color: colors.onSurface,
    fontWeight: typography.weights.medium as any,
  },
  selectedSegmentButtonText: {
    color: colors.onPrimary || colors.surface,
    fontWeight: typography.weights.semibold as any,
  },
  inputContainer: {
    marginBottom: spacing.md,
  },
  inputLabel: {
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.medium as any,
    color: colors.onSurface,
    marginBottom: spacing.xs,
  },
  textInput: {
    borderWidth: 1,
    borderColor: colors.outline || colors.border,
    borderRadius: 8,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    fontSize: typography.sizes.md,
    color: colors.onSurface,
    backgroundColor: colors.surface,
    minHeight: 48,
  },
  textInputError: {
    borderColor: colors.error,
  },
  textArea: {
    borderWidth: 1,
    borderColor: colors.outline || colors.border,
    borderRadius: 8,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    fontSize: typography.sizes.md,
    color: colors.onSurface,
    backgroundColor: colors.surface,
    minHeight: 100,
    textAlignVertical: 'top',
  },
});
