-- ============================================================================
-- ADD MISSING RESERVATION COLUMNS
-- This script adds the missing columns that the frontend expects
-- ============================================================================

-- ============================================================================
-- STEP 1: Add missing columns to reservations table
-- ============================================================================

-- Add guest_name column if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'reservations' AND column_name = 'guest_name') THEN
        ALTER TABLE public.reservations ADD COLUMN guest_name TEXT;
        RAISE NOTICE 'Added guest_name column';
    ELSE
        RAISE NOTICE 'guest_name column already exists';
    END IF;
END $$;

-- Add guest_email column if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'reservations' AND column_name = 'guest_email') THEN
        ALTER TABLE public.reservations ADD COLUMN guest_email TEXT;
        RAISE NOTICE 'Added guest_email column';
    ELSE
        RAISE NOTICE 'guest_email column already exists';
    END IF;
END $$;

-- Add room_number column if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'reservations' AND column_name = 'room_number') THEN
        ALTER TABLE public.reservations ADD COLUMN room_number TEXT;
        RAISE NOTICE 'Added room_number column';
    ELSE
        RAISE NOTICE 'room_number column already exists';
    END IF;
END $$;

-- Add guests column if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'reservations' AND column_name = 'guests') THEN
        ALTER TABLE public.reservations ADD COLUMN guests INTEGER DEFAULT 1;
        RAISE NOTICE 'Added guests column';
    ELSE
        RAISE NOTICE 'guests column already exists';
    END IF;
END $$;

-- Add check_in column (legacy alias) if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'reservations' AND column_name = 'check_in') THEN
        ALTER TABLE public.reservations ADD COLUMN check_in DATE;
        RAISE NOTICE 'Added check_in column';
    ELSE
        RAISE NOTICE 'check_in column already exists';
    END IF;
END $$;

-- Add check_out column (legacy alias) if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'reservations' AND column_name = 'check_out') THEN
        ALTER TABLE public.reservations ADD COLUMN check_out DATE;
        RAISE NOTICE 'Added check_out column';
    ELSE
        RAISE NOTICE 'check_out column already exists';
    END IF;
END $$;

-- ============================================================================
-- STEP 2: Update existing reservations with missing data
-- ============================================================================

-- Update all reservations to populate the new columns
UPDATE public.reservations 
SET 
    guest_name = u.full_name,
    guest_email = u.email,
    room_number = r.room_number,
    guests = COALESCE(guests, 1),
    check_in = check_in_date,
    check_out = check_out_date
FROM public.users u, public.rooms r
WHERE 
    public.reservations.guest_id = u.id 
    AND public.reservations.room_id = r.id;

-- ============================================================================
-- STEP 3: Create/update trigger to keep columns in sync
-- ============================================================================

-- Drop existing trigger and function if they exist
DROP TRIGGER IF EXISTS trigger_sync_reservation_data ON public.reservations;
DROP FUNCTION IF EXISTS sync_reservation_data();

-- Create comprehensive function to sync all reservation data
CREATE OR REPLACE FUNCTION sync_reservation_data()
RETURNS TRIGGER AS $$
DECLARE
    guest_record RECORD;
    room_record RECORD;
BEGIN
    -- When a reservation is inserted or updated, populate all derived fields
    IF TG_OP = 'INSERT' OR TG_OP = 'UPDATE' THEN
        -- Get guest information
        SELECT full_name, email 
        INTO guest_record
        FROM public.users 
        WHERE id = NEW.guest_id;
        
        -- Get room information
        SELECT room_number 
        INTO room_record
        FROM public.rooms 
        WHERE id = NEW.room_id;
        
        -- Update the NEW record with the fetched data
        IF guest_record IS NOT NULL THEN
            NEW.guest_name := guest_record.full_name;
            NEW.guest_email := guest_record.email;
        END IF;
        
        IF room_record IS NOT NULL THEN
            NEW.room_number := room_record.room_number;
        END IF;
        
        -- Sync date columns
        NEW.check_in := NEW.check_in_date;
        NEW.check_out := NEW.check_out_date;
        
        -- Set default guests if not provided
        IF NEW.guests IS NULL THEN
            NEW.guests := 1;
        END IF;
        
        RETURN NEW;
    END IF;
    
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to auto-populate all reservation data
CREATE TRIGGER trigger_sync_reservation_data
    BEFORE INSERT OR UPDATE ON public.reservations
    FOR EACH ROW
    EXECUTE FUNCTION sync_reservation_data();

-- ============================================================================
-- STEP 4: Verify the fix
-- ============================================================================

-- Show updated reservations with all columns
SELECT 
    'Updated Reservations with All Columns' as info,
    r.id,
    r.guest_id,
    r.guest_name,
    r.guest_email,
    r.room_id,
    r.room_number,
    r.check_in_date,
    r.check_out_date,
    r.check_in,
    r.check_out,
    r.guests,
    r.status,
    r.payment_status,
    r.total_amount,
    r.created_at
FROM public.reservations r
ORDER BY r.created_at DESC;

-- Test the trigger by updating a reservation
UPDATE public.reservations 
SET updated_at = NOW()
WHERE id = (SELECT id FROM public.reservations LIMIT 1);

-- Show final state after trigger test
SELECT 
    'Final State After Trigger Test' as info,
    r.id,
    r.guest_name,
    r.guest_email,
    r.room_number,
    r.guests,
    r.status,
    r.total_amount
FROM public.reservations r
ORDER BY r.created_at DESC;

-- Instructions
DO $$
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '✅ MISSING RESERVATION COLUMNS ADDED!';
    RAISE NOTICE '';
    RAISE NOTICE '📋 WHAT WAS ADDED:';
    RAISE NOTICE '- guest_name, guest_email, room_number columns';
    RAISE NOTICE '- guests column for number of guests';
    RAISE NOTICE '- check_in, check_out legacy alias columns';
    RAISE NOTICE '- Comprehensive trigger to keep all data in sync';
    RAISE NOTICE '';
    RAISE NOTICE '🔄 NEXT STEPS:';
    RAISE NOTICE '1. Refresh the admin reservations screen in your app';
    RAISE NOTICE '2. All reservation data should now display correctly';
    RAISE NOTICE '3. Check the console logs for any remaining issues';
END;
$$;
