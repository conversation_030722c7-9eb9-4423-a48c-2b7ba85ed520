import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
} from 'react-native';
import { RefreshControl } from 'react-native-gesture-handler';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Card, Button, Text as PaperText } from 'react-native-paper';
import { Ionicons } from '@expo/vector-icons';
import { BarChart } from 'react-native-gifted-charts';
import { useAuthStore } from '../../store/authStore';
import { useRoomStore } from '../../store/roomStore';
import { useReservationStore } from '../../store/reservationStore';
import { usePermissions } from '../../hooks/usePermissions';
import { formatPrice } from '../../utils/currency';
import { 
  normalizeDate, 
  getTodayDateString, 
  isToday, 
  isCurrentMonth,
  isActiveCheckInStatus,
  isActiveCheckOutStatus 
} from '../../utils/dateUtils';
import { spacing, typography } from '../../constants';
import { useTheme } from '../../contexts/ThemeContext';

// Using a static width value since Dimensions is not available in this TypeScript version
const screenWidth = 360; // Standard mobile width

interface DashboardStats {
  totalRooms: number;
  availableRooms: number;
  occupiedRooms: number;
  totalReservations: number;
  todayCheckIns: number;
  todayCheckOuts: number;
  monthlyRevenue: number;
  occupancyRate: number;
}

interface RevenueChartDataItem {
  value: number;
  label: string;
  frontColor: string;
}

export const AdminDashboardScreen = ({ navigation }: any) => {
  const { colors } = useTheme();
  const { user } = useAuthStore();
  const { canManageRooms, canViewAllReservations } = usePermissions();
  const { rooms, fetchRooms } = useRoomStore();
  const { reservations, fetchAllReservations } = useReservationStore();
  const [refreshing, setRefreshing] = React.useState(false);
  const [stats, setStats] = React.useState<DashboardStats>({
    totalRooms: 0,
    availableRooms: 0,
    occupiedRooms: 0,
    totalReservations: 0,
    todayCheckIns: 0,
    todayCheckOuts: 0,
    monthlyRevenue: 0,
    occupancyRate: 0,
  });

  const [revenueData, setRevenueData] = React.useState<RevenueChartDataItem[]>([]);

  const loadDashboardData = async () => {
    try {
      await Promise.all([fetchRooms(), fetchAllReservations()]);
    } catch (error) {
      console.error('Error loading dashboard data:', error);
    }
  };

  React.useEffect(() => {
    loadDashboardData();
  }, []);

  React.useEffect(() => {
    if (rooms.length > 0 || reservations.length > 0) {
      calculateStats();
      generateRevenueChart();
    }
  }, [rooms, reservations]);

  const onRefresh = async () => {
    setRefreshing(true);
    await loadDashboardData();
    setRefreshing(false);
  };

  const calculateStats = () => {
    const availableRooms = rooms.filter(room => room.is_available).length;
    const occupiedRooms = rooms.length - availableRooms;

    // Count today's check-ins (confirmed or already checked-in reservations)
    const todayCheckIns = reservations.filter(r => 
      isToday(r.check_in_date) && isActiveCheckInStatus(r.status)
    ).length;

    // Count today's check-outs (checked-in or already checked-out reservations)
    const todayCheckOuts = reservations.filter(r => 
      isToday(r.check_out_date) && isActiveCheckOutStatus(r.status)
    ).length;

    // Calculate monthly revenue from paid or pending reservations
    const monthlyRevenue = reservations
      .filter(r => 
        isCurrentMonth(r.created_at) && 
        (r.payment_status === 'paid' || r.payment_status === 'pending')
      )
      .reduce((sum, r) => sum + (r.total_amount || 0), 0);

    // Debug logging (can be enabled for troubleshooting)
    if (process.env.NODE_ENV === 'development') {
      console.log('Dashboard Stats Debug:', {
        today: getTodayDateString(),
        totalReservations: reservations.length,
        todayCheckIns,
        todayCheckOuts,
        availableRooms,
        occupiedRooms,
        monthlyRevenue,
        sampleReservations: reservations.slice(0, 3).map(r => ({
          id: r.id,
          check_in_date: r.check_in_date,
          check_out_date: r.check_out_date,
          status: r.status,
          payment_status: r.payment_status
        }))
      });
    }

    const occupancyRate =
      rooms.length > 0 ? (occupiedRooms / rooms.length) * 100 : 0;

    setStats({
      totalRooms: rooms.length,
      availableRooms,
      occupiedRooms,
      totalReservations: reservations.length,
      todayCheckIns,
      todayCheckOuts,
      monthlyRevenue,
      occupancyRate,
    });
  };

  const generateRevenueChart = () => {
    const data: RevenueChartDataItem[] = [];
    const today = new Date();
    for (let i = 6; i >= 0; i--) {
      const date = new Date(today);
      date.setDate(today.getDate() - i);
      const dateStr = date.toISOString().split('T')[0];
      const dayRevenue = reservations
        .filter(r => r.created_at && r.created_at.startsWith(dateStr))
        .reduce((sum, r) => sum + (r.total_amount || 0), 0);

      data.push({
        value: dayRevenue,
        label: date.toLocaleDateString('en-US', { weekday: 'short' }),
        frontColor: i === 0 ? colors.primary : colors.textSecondary,
      });
    }
    setRevenueData(data);
  };

  const StatCard = ({
    title,
    value,
    icon,
    color,
    onPress,
  }: {
    title: string;
    value: string | number;
    icon: keyof typeof Ionicons.glyphMap;
    color: string;
    onPress?: () => void;
  }) => (
    <TouchableOpacity onPress={onPress} disabled={!onPress}>
      <Card style={[styles.statCard, { backgroundColor: colors.surface }]}>
        <Card.Content style={styles.statCardContent}>
          <Ionicons name={icon} size={32} color={color} />
          <Text style={[styles.statValue, { color: colors.onSurface }]}>
            {value}
          </Text>
          <Text style={[styles.statTitle, { color: colors.onSurfaceVariant }]}>
            {title}
          </Text>
        </Card.Content>
      </Card>
    </TouchableOpacity>
  );

  const memoizedRevenueChart = React.useMemo(
    () => (
      <BarChart
        data={revenueData}
        barWidth={22}
        spacing={25}
        roundedTop
        roundedBottom
        hideRules
        xAxisThickness={0}
        yAxisThickness={0}
        yAxisTextStyle={{ color: colors.onSurfaceVariant }}
        noOfSections={4}
        maxValue={
          Math.max(...revenueData.map(d => d.value)) > 0
            ? Math.max(...revenueData.map(d => d.value)) * 1.25
            : 1000
        }
        formatYLabel={value => formatPrice(Number(value))}
        width={screenWidth - 90}
        height={180}
      />
    ),
    [revenueData, colors]
  );

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <ScrollView
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollViewContent}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            tintColor={colors.primary}
          />
        }
      >
        <View style={styles.header}>
          <View>
            <Text style={[styles.headerGreeting, { color: colors.onSurfaceVariant }]}>
              Welcome back,
            </Text>
            <Text style={[styles.headerName, { color: colors.onSurface }]}>
              {user?.full_name || 'Admin'}
            </Text>
          </View>
          <TouchableOpacity onPress={() => navigation.navigate('AdminSettings')}>
            <Ionicons name="settings-outline" size={24} color={colors.onSurface} />
          </TouchableOpacity>
        </View>

        <View style={styles.statsGrid}>
          <StatCard
            title="Available Rooms"
            value={stats.availableRooms}
            icon="bed-outline"
            color="#4CAF50"
            onPress={() => navigation.navigate('RoomsManagement')}
          />
          <StatCard
            title="Occupied Rooms"
            value={stats.occupiedRooms}
            icon="people-outline"
            color="#FFC107"
            onPress={() => navigation.navigate('RoomsManagement')}
          />
          <StatCard
            title="Check-ins Today"
            value={stats.todayCheckIns}
            icon="log-in-outline"
            color="#2196F3"
            onPress={() => navigation.navigate('ReservationsManagement')}
          />
          <StatCard
            title="Check-outs Today"
            value={stats.todayCheckOuts}
            icon="log-out-outline"
            color="#F44336"
            onPress={() => navigation.navigate('ReservationsManagement')}
          />
        </View>

        <Card style={[styles.mainCard, { backgroundColor: colors.surface }]}>
          <Card.Content>
            <Text style={[styles.cardTitle, { color: colors.onSurface }]}>
              Weekly Revenue
            </Text>
            <Text style={{ color: colors.onSurfaceVariant, marginBottom: spacing.lg }}>
              Last 7 days performance
            </Text>
            {memoizedRevenueChart}
          </Card.Content>
        </Card>

        <Card style={[styles.mainCard, { backgroundColor: colors.surface }]}>
          <Card.Content>
            <Text style={[styles.cardTitle, { color: colors.onSurface }]}>
              Occupancy
            </Text>
            <View style={styles.occupancyContent}>
              <View>
                <Text style={[styles.occupancyValue, { color: colors.primary }]}>
                  {stats.occupancyRate.toFixed(1)}%
                </Text>
                <Text style={[styles.occupancyLabel, { color: colors.onSurfaceVariant }]}>
                  Current Rate
                </Text>
              </View>
              <View>
                <Text style={[styles.occupancyValue, { color: colors.onSurface }]}>
                  {stats.totalReservations}
                </Text>
                <Text style={[styles.occupancyLabel, { color: colors.onSurfaceVariant }]}>
                  Total Reservations
                </Text>
              </View>
            </View>
          </Card.Content>
        </Card>

        {canManageRooms && (
          <Button
            mode="contained"
            onPress={() => navigation.navigate('RoomsManagement', { screen: 'AddEditRoom' })}
            style={styles.fab}
            icon={({ size, color }) => (
              <Ionicons name="add-circle-outline" size={size} color={color} />
            )}
            // Remove the unsupported labelStyle property
            // The button text styling can be achieved through the children or custom component
          >
            <Text style={{fontSize: 16, fontWeight: '600'}}>New Room</Text>
          </Button>
        )}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollViewContent: {
    paddingBottom: 100,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingTop: spacing.lg,
    paddingBottom: spacing.md,
  },
  headerGreeting: {
    ...typography.body2,
  },
  headerName: {
    ...typography.h4,
    marginTop: -4,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.lg,
    marginTop: spacing.md,
  },
  statCard: {
    width: screenWidth / 2 - spacing.lg * 1.5,
    marginBottom: spacing.md,
    borderRadius: 16,
    elevation: 2,
  },
  statCardContent: {
    alignItems: 'center',
    padding: spacing.md,
  },
  statValue: {
    ...typography.h4,
    marginTop: spacing.sm,
  },
  statTitle: {
    ...typography.caption,
    marginTop: spacing.xs,
  },
  mainCard: {
    marginHorizontal: spacing.lg,
    marginTop: spacing.lg,
    borderRadius: 16,
    elevation: 2,
  },
  cardTitle: {
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.medium,
  },
  occupancyContent: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    paddingTop: spacing.lg,
  },
  occupancyValue: {
    ...typography.h4,
    textAlign: 'center',
  },
  occupancyLabel: {
    ...typography.body2,
    textAlign: 'center',
    marginTop: spacing.xs,
  },
  fab: {
    position: 'absolute',
    bottom: 16,
    right: 16,
    borderRadius: 28,
    height: 56,
    justifyContent: 'center',
  },
});
