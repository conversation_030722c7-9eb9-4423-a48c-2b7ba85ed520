-- Fix duplicate user entries and improve user creation process
-- Run this script to clean up any existing issues and prevent future duplicates

-- First, let's check for any duplicate users (this is just for information)
-- SELECT id, email, COUNT(*) as count 
-- FROM public.users 
-- GROUP BY id, email 
-- HAVING COUNT(*) > 1;

-- Remove any orphaned user profiles (users in public.users but not in auth.users)
DELETE FROM public.users 
WHERE id NOT IN (SELECT id FROM auth.users);

-- Ensure all auth users have profiles in public.users
-- This will create missing profiles for existing auth users
INSERT INTO public.users (id, email, full_name, phone, role)
SELECT 
    au.id,
    au.email,
    COALESCE(au.raw_user_meta_data->>'full_name', au.email) as full_name,
    au.raw_user_meta_data->>'phone' as phone,
    COALESCE(au.raw_user_meta_data->>'role', 'guest') as role
FROM auth.users au
WHERE au.id NOT IN (SELECT id FROM public.users)
ON CONFLICT (id) DO NOTHING;

-- Update any existing profiles with missing data from auth metadata
UPDATE public.users 
SET 
    full_name = COALESCE(
        NULLIF(public.users.full_name, ''), 
        auth.users.raw_user_meta_data->>'full_name',
        public.users.email
    ),
    phone = COALESCE(
        public.users.phone,
        auth.users.raw_user_meta_data->>'phone'
    ),
    role = COALESCE(
        public.users.role,
        CAST(COALESCE(auth.users.raw_user_meta_data->>'role', 'guest') AS user_role)
    )
FROM auth.users
WHERE public.users.id = auth.users.id
    AND (
        public.users.full_name = '' 
        OR public.users.full_name IS NULL
        OR public.users.phone IS NULL
    );

-- Ensure RLS policies are properly set up
-- Drop and recreate the insert policy to be more permissive during signup
DROP POLICY IF EXISTS "Enable insert for service role and authenticated users" ON public.users;

CREATE POLICY "Enable insert for service role and authenticated users" ON public.users
    FOR INSERT WITH CHECK (
        auth.role() = 'service_role' OR
        auth.role() = 'authenticated' OR
        auth.uid() = id OR
        auth.uid() IS NULL  -- Allow during signup process
    );

-- Add a policy to allow upserts during user creation
DROP POLICY IF EXISTS "Enable upsert for user creation" ON public.users;

CREATE POLICY "Enable upsert for user creation" ON public.users
    FOR ALL USING (
        auth.role() = 'service_role' OR
        auth.uid() = id OR
        (auth.role() = 'authenticated' AND auth.uid() = id)
    );

-- Create an index on email for better performance
CREATE INDEX IF NOT EXISTS idx_users_email ON public.users(email);

-- Create a function to safely create or update user profiles
CREATE OR REPLACE FUNCTION public.create_user_profile(
    user_id UUID,
    user_email TEXT,
    user_full_name TEXT DEFAULT '',
    user_phone TEXT DEFAULT NULL,
    user_role user_role DEFAULT 'guest'
)
RETURNS VOID AS $$
BEGIN
    INSERT INTO public.users (id, email, full_name, phone, role)
    VALUES (user_id, user_email, user_full_name, user_phone, user_role)
    ON CONFLICT (id) 
    DO UPDATE SET
        email = EXCLUDED.email,
        full_name = CASE 
            WHEN EXCLUDED.full_name != '' THEN EXCLUDED.full_name 
            ELSE public.users.full_name 
        END,
        phone = COALESCE(EXCLUDED.phone, public.users.phone),
        role = EXCLUDED.role,
        updated_at = NOW();
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission on the function
GRANT EXECUTE ON FUNCTION public.create_user_profile TO authenticated, service_role;

-- Create a trigger to automatically create user profiles (optional - can be disabled if manual creation is preferred)
-- This is commented out by default to avoid conflicts with manual profile creation
/*
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.users (id, email, full_name, phone, role)
    VALUES (
        NEW.id,
        NEW.email,
        COALESCE(NEW.raw_user_meta_data->>'full_name', NEW.email),
        NEW.raw_user_meta_data->>'phone',
        COALESCE(NEW.raw_user_meta_data->>'role', 'guest')::user_role
    )
    ON CONFLICT (id) DO NOTHING;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();
*/

-- Verify the setup
SELECT 
    'Auth users count' as metric, 
    COUNT(*)::text as value 
FROM auth.users
UNION ALL
SELECT 
    'Public users count' as metric, 
    COUNT(*)::text as value 
FROM public.users
UNION ALL
SELECT 
    'Users without profiles' as metric, 
    COUNT(*)::text as value 
FROM auth.users au 
WHERE au.id NOT IN (SELECT id FROM public.users)
UNION ALL
SELECT 
    'Orphaned profiles' as metric, 
    COUNT(*)::text as value 
FROM public.users pu 
WHERE pu.id NOT IN (SELECT id FROM auth.users);
