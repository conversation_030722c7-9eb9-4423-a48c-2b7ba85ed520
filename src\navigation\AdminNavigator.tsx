import React from 'react';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { MaterialIcons } from '@expo/vector-icons';
import { colors } from '../constants';

// Screen imports
import { AdminDashboardScreen } from '../screens/admin/AdminDashboardScreen';
import { AdminRoomsScreen } from '../screens/admin/AdminRoomsScreen';
import { AddEditRoomScreen } from '../screens/admin/AddEditRoomScreen';
import { AdminReservationsScreen } from '../screens/admin/AdminReservationsScreen';
import { AdminAnalyticsScreen } from '../screens/admin/AdminAnalyticsScreen';
import { AdminSettingsScreen } from '../screens/admin/AdminSettingsScreen';

const Tab = createBottomTabNavigator();
const Stack = createNativeStackNavigator();

// Dashboard Stack
function DashboardStack() {
  return (
    <Stack.Navigator>
      <Stack.Screen 
        name="DashboardMain" 
        component={AdminDashboardScreen} 
        options={{ title: 'Admin Dashboard' }}
      />
    </Stack.Navigator>
  );
}

// Rooms Management Stack
function RoomsManagementStack() {
  return (
    <Stack.Navigator>
      <Stack.Screen 
        name="RoomsManagementMain" 
        component={AdminRoomsScreen} 
        options={{ title: 'Manage Rooms' }}
      />
      <Stack.Screen 
        name="AddEditRoom" 
        component={AddEditRoomScreen}
        options={({ route }) => ({
          title: route.params?.room ? 'Edit Room' : 'Add Room'
        })}
      />
    </Stack.Navigator>
  );
}

// Reservations Management Stack
function ReservationsManagementStack() {
  return (
    <Stack.Navigator>
      <Stack.Screen 
        name="ReservationsManagementMain" 
        component={AdminReservationsScreen} 
        options={{ title: 'All Reservations' }}
      />
    </Stack.Navigator>
  );
}

// Analytics Stack
function AnalyticsStack() {
  return (
    <Stack.Navigator>
      <Stack.Screen 
        name="AnalyticsMain" 
        component={AdminAnalyticsScreen} 
        options={{ title: 'Analytics & Reports' }}
      />
    </Stack.Navigator>
  );
}

// Settings Stack
function SettingsStack() {
  return (
    <Stack.Navigator>
      <Stack.Screen 
        name="SettingsMain" 
        component={AdminSettingsScreen} 
        options={{ title: 'Settings' }}
      />
    </Stack.Navigator>
  );
}

export const AdminNavigator = () => {
  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused, color, size }) => {
          let iconName: any;

          if (route.name === 'Dashboard') {
            iconName = focused ? 'dashboard' : 'dashboard';
          } else if (route.name === 'RoomsManagement') {
            iconName = focused ? 'hotel' : 'hotel';
          } else if (route.name === 'ReservationsManagement') {
            iconName = focused ? 'event' : 'event';
          } else if (route.name === 'Analytics') {
            iconName = focused ? 'analytics' : 'analytics';
          } else if (route.name === 'Settings') {
            iconName = focused ? 'settings' : 'settings';
          } else {
            iconName = 'help';
          }

          return <MaterialIcons name={iconName} size={size} color={color} />;
        },
        tabBarActiveTintColor: colors.primary,
        tabBarInactiveTintColor: colors.onSurfaceVariant,
        headerShown: false,
        tabBarLabelStyle: { fontSize: 10 },
        tabBarStyle: {
          backgroundColor: colors.surface,
          borderTopColor: colors.outline,
        },
      })}
    >
      <Tab.Screen 
        name="Dashboard" 
        component={DashboardStack}
        options={{ tabBarLabel: 'Dashboard' }}
      />
      <Tab.Screen 
        name="RoomsManagement" 
        component={RoomsManagementStack}
        options={{ tabBarLabel: 'Rooms' }}
      />
      <Tab.Screen 
        name="ReservationsManagement" 
        component={ReservationsManagementStack}
        options={{ tabBarLabel: 'Bookings' }}
      />
      <Tab.Screen 
        name="Analytics" 
        component={AnalyticsStack}
        options={{ tabBarLabel: 'Analytics' }}
      />
      <Tab.Screen 
        name="Settings" 
        component={SettingsStack}
        options={{ tabBarLabel: 'Settings' }}
      />
    </Tab.Navigator>
  );
};
