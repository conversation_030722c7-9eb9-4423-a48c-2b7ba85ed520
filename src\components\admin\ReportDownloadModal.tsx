import React, { useState } from 'react';
import {
  View,
  StyleSheet,
  Alert,
  TouchableOpacity,
  ScrollView,
} from 'react-native';
import {
  Modal,
  Portal,
  Card,
  Title,
  Text,
  Button,
  Divider,
  ActivityIndicator,
} from 'react-native-paper';
import { MaterialIcons } from '@expo/vector-icons';
import { colors, spacing, typography } from '../../constants';
import { reportService, ReportData, ReportOptions } from '../../services/reportService';

interface ReportDownloadModalProps {
  visible: boolean;
  onDismiss: () => void;
  reportData: ReportData;
}

export const ReportDownloadModal: React.FC<ReportDownloadModalProps> = ({
  visible,
  onDismiss,
  reportData,
}) => {
  const [selectedFormat, setSelectedFormat] = useState<'csv' | 'pdf'>('csv');
  const [selectedType, setSelectedType] = useState<'summary' | 'detailed' | 'reservations' | 'rooms'>('summary');
  const [downloading, setDownloading] = useState(false);

  // Debug log when modal opens
  React.useEffect(() => {
    if (visible) {
      console.log('📊 Modal opened with data:', {
        totalRevenue: reportData?.totalRevenue,
        totalReservations: reportData?.totalReservations,
        period: reportData?.period,
        hasReservations: !!reportData?.reservations?.length,
        hasRooms: !!reportData?.rooms?.length,
      });
    }
  }, [visible, reportData]);

  const handleDownload = async () => {
    try {
      console.log('🔄 Starting download...', { selectedFormat, selectedType });
      setDownloading(true);

      const options: ReportOptions = {
        format: selectedFormat,
        type: selectedType,
      };

      console.log('📊 Report options:', options);
      console.log('📊 Report data preview:', {
        totalRevenue: reportData.totalRevenue,
        totalReservations: reportData.totalReservations,
        period: reportData.period,
        reservationsCount: reportData.reservations?.length || 0,
        roomsCount: reportData.rooms?.length || 0,
      });

      let fileUri: string;

      if (selectedFormat === 'csv') {
        console.log('📄 Generating CSV report...');
        fileUri = await reportService.generateCSVReport(reportData, options);
      } else {
        console.log('📄 Generating PDF report...');
        fileUri = await reportService.generatePDFReport(reportData, options);
      }

      console.log('✅ Report generated:', fileUri);

      // Share the file
      console.log('📤 Sharing report...');
      await reportService.shareReport(fileUri);

      Alert.alert(
        'Success',
        `${selectedType.charAt(0).toUpperCase() + selectedType.slice(1)} report generated and ready to share!`,
        [{ text: 'OK', onPress: onDismiss }]
      );

    } catch (error) {
      console.error('❌ Error generating report:', error);
      Alert.alert(
        'Error',
        `Failed to generate report: ${error.message || 'Unknown error'}`,
        [{ text: 'OK' }]
      );
    } finally {
      setDownloading(false);
    }
  };

  const formatOptions = [
    { value: 'csv', label: 'CSV (Excel Compatible)', icon: 'table-chart' },
    { value: 'pdf', label: 'PDF (Formatted Report)', icon: 'picture-as-pdf' },
  ];

  const typeOptions = [
    { 
      value: 'summary', 
      label: 'Summary Report', 
      description: 'Key metrics and overview',
      icon: 'summarize'
    },
    { 
      value: 'detailed', 
      label: 'Detailed Analytics', 
      description: 'Complete analytics with charts data',
      icon: 'analytics'
    },
    { 
      value: 'reservations', 
      label: 'Reservations Data', 
      description: 'All reservation records',
      icon: 'event'
    },
    { 
      value: 'rooms', 
      label: 'Rooms Data', 
      description: 'Room inventory and details',
      icon: 'hotel'
    },
  ];

  return (
    <Portal>
      <Modal
        visible={visible}
        onDismiss={onDismiss}
        contentContainerStyle={styles.modalContainer}
      >
        <Card style={styles.card}>
          <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
            <Card.Content>
            <View style={styles.header}>
              <MaterialIcons name="download" size={20} color={colors.primary} />
              <Title style={styles.title}>Download Report</Title>
            </View>

            <Text style={styles.subtitle}>
              Choose format and type
            </Text>

            <Divider style={styles.divider} />

            {/* Format Selection */}
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Report Format</Text>
              {formatOptions.map((option) => (
                <TouchableOpacity
                  key={option.value}
                  style={[
                    styles.optionButton,
                    selectedFormat === option.value && styles.selectedOption
                  ]}
                  onPress={() => setSelectedFormat(option.value as 'csv' | 'pdf')}
                >
                  <View style={styles.optionContent}>
                    <MaterialIcons
                      name={option.icon as any}
                      size={20}
                      color={selectedFormat === option.value ? colors.white : colors.primary}
                      style={styles.optionIcon}
                    />
                    <View style={styles.optionText}>
                      <Text style={[
                        styles.optionLabel,
                        selectedFormat === option.value && styles.selectedText
                      ]}>
                        {option.label}
                      </Text>
                    </View>
                  </View>
                  <View style={[
                    styles.radioCircle,
                    selectedFormat === option.value && styles.radioSelected
                  ]}>
                    {selectedFormat === option.value && (
                      <View style={styles.radioDot} />
                    )}
                  </View>
                </TouchableOpacity>
              ))}
            </View>

            <Divider style={styles.divider} />

            {/* Type Selection */}
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Report Type</Text>
              {typeOptions.map((option) => (
                <TouchableOpacity
                  key={option.value}
                  style={[
                    styles.optionButton,
                    selectedType === option.value && styles.selectedOption
                  ]}
                  onPress={() => setSelectedType(option.value as any)}
                >
                  <View style={styles.optionContent}>
                    <MaterialIcons
                      name={option.icon as any}
                      size={20}
                      color={selectedType === option.value ? colors.white : colors.primary}
                      style={styles.optionIcon}
                    />
                    <View style={styles.optionText}>
                      <Text style={[
                        styles.optionLabel,
                        selectedType === option.value && styles.selectedText
                      ]}>
                        {option.label}
                      </Text>
                      <Text style={[
                        styles.optionDescription,
                        selectedType === option.value && styles.selectedText
                      ]}>
                        {option.description}
                      </Text>
                    </View>
                  </View>
                  <View style={[
                    styles.radioCircle,
                    selectedType === option.value && styles.radioSelected
                  ]}>
                    {selectedType === option.value && (
                      <View style={styles.radioDot} />
                    )}
                  </View>
                </TouchableOpacity>
              ))}
            </View>

            <Divider style={styles.divider} />

            {/* Report Info */}
            <View style={styles.infoSection}>
              <Text style={styles.infoText}>
                📊 Period: {reportData.period.toUpperCase()}
              </Text>
              <Text style={styles.infoText}>
                📅 Generated: {new Date().toLocaleDateString()}
              </Text>
              <Text style={styles.infoText}>
                📈 {reportData.totalReservations} reservations included
              </Text>
            </View>
          </Card.Content>
          </ScrollView>

          <View style={styles.actions}>
            <View style={styles.buttonContainer}>
              <TouchableOpacity
                onPress={onDismiss}
                disabled={downloading}
                style={[styles.cancelButton, downloading && styles.disabledButton]}
              >
                <Text style={styles.cancelButtonText}>Cancel</Text>
              </TouchableOpacity>

              <TouchableOpacity
                onPress={handleDownload}
                disabled={downloading}
                style={[styles.downloadButton, downloading && styles.disabledButton]}
              >
                <View style={styles.downloadButtonContent}>
                  {downloading ? (
                    <View style={styles.loadingContainer}>
                      <ActivityIndicator size="small" color={colors.white} />
                      <Text style={styles.loadingText}>Generating...</Text>
                    </View>
                  ) : (
                    <>
                      <MaterialIcons name="download" size={16} color={colors.white} style={styles.downloadIcon} />
                      <Text style={styles.downloadButtonText}>Generate Report</Text>
                    </>
                  )}
                </View>
              </TouchableOpacity>
            </View>
          </View>
        </Card>
      </Modal>
    </Portal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    margin: spacing.md,
    marginVertical: '10%',
    maxHeight: '80%',
  },
  card: {
    backgroundColor: colors.surface,
    maxHeight: '100%',
  },
  scrollView: {
    flexGrow: 0,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  title: {
    marginLeft: spacing.sm,
    fontSize: typography.sizes.lg,
    color: colors.primary,
  },
  subtitle: {
    fontSize: typography.sizes.sm,
    color: colors.onSurfaceVariant,
    marginBottom: spacing.md,
  },
  divider: {
    marginVertical: spacing.sm,
  },
  section: {
    marginVertical: spacing.xs,
  },
  sectionTitle: {
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.semibold as any,
    color: colors.onSurface,
    marginBottom: spacing.sm,
  },
  optionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: spacing.sm,
    paddingHorizontal: spacing.sm,
    marginVertical: spacing.xs / 2,
    borderRadius: 6,
    borderWidth: 1,
    borderColor: colors.outline,
    backgroundColor: colors.surface,
  },
  selectedOption: {
    backgroundColor: colors.primary,
    borderColor: colors.primary,
  },
  optionContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  optionIcon: {
    marginRight: spacing.sm,
  },
  optionText: {
    flex: 1,
  },
  optionLabel: {
    fontSize: typography.sizes.md,
    color: colors.onSurface,
    fontWeight: typography.weights.medium as any,
  },
  optionDescription: {
    fontSize: typography.sizes.sm,
    color: colors.onSurfaceVariant,
    marginTop: 2,
  },
  selectedText: {
    color: colors.white,
  },
  radioCircle: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: colors.outline,
    alignItems: 'center',
    justifyContent: 'center',
  },
  radioSelected: {
    borderColor: colors.white,
  },
  radioDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: colors.white,
  },
  infoSection: {
    backgroundColor: colors.surfaceVariant,
    padding: spacing.sm,
    borderRadius: 6,
    marginTop: spacing.xs,
  },
  infoText: {
    fontSize: typography.sizes.sm,
    color: colors.onSurfaceVariant,
    marginBottom: 4,
  },
  actions: {
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.md,
    borderTopWidth: 1,
    borderTopColor: colors.outline,
    backgroundColor: colors.surface,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    width: '100%',
    gap: spacing.sm,
  },
  cancelButton: {
    flex: 1,
    paddingVertical: spacing.md,
    paddingHorizontal: spacing.lg,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: colors.outline,
    backgroundColor: colors.surface,
    alignItems: 'center',
    justifyContent: 'center',
  },
  cancelButtonText: {
    fontSize: typography.sizes.md,
    color: colors.onSurface,
    fontWeight: typography.weights.medium as any,
  },
  downloadButton: {
    flex: 1,
    paddingVertical: spacing.md,
    paddingHorizontal: spacing.lg,
    borderRadius: 8,
    backgroundColor: colors.primary,
    alignItems: 'center',
    justifyContent: 'center',
  },
  downloadButtonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  downloadButtonText: {
    fontSize: typography.sizes.md,
    color: colors.white,
    fontWeight: typography.weights.medium as any,
  },
  downloadIcon: {
    marginRight: spacing.xs,
  },
  disabledButton: {
    opacity: 0.6,
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  loadingText: {
    color: colors.white,
    marginLeft: spacing.xs,
    fontSize: typography.sizes.sm,
  },
});
