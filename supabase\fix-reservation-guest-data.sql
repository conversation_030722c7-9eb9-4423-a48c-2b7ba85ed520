-- ============================================================================
-- FIX RESERVATION GUEST DATA
-- This script fixes the missing guest_name, guest_email, and room_number data
-- ============================================================================

-- ============================================================================
-- STEP 1: Update existing reservations with missing guest data
-- ============================================================================

-- Update all reservations to populate guest_name, guest_email, and room_number
UPDATE public.reservations 
SET 
    guest_name = u.full_name,
    guest_email = u.email,
    room_number = r.room_number
FROM public.users u, public.rooms r
WHERE 
    public.reservations.guest_id = u.id 
    AND public.reservations.room_id = r.id
    AND (
        public.reservations.guest_name IS NULL 
        OR public.reservations.guest_email IS NULL 
        OR public.reservations.room_number IS NULL
    );

-- ============================================================================
-- STEP 2: Recreate the trigger function to ensure it works properly
-- ============================================================================

-- Drop existing trigger and function
DROP TRIGGER IF EXISTS trigger_sync_guest_data ON public.reservations;
DROP FUNCTION IF EXISTS sync_guest_data_in_reservations();

-- Create improved function to sync guest data
CREATE OR REPLACE FUNCTION sync_guest_data_in_reservations()
RETURNS TRIGGER AS $$
DECLARE
    guest_record RECORD;
    room_record RECORD;
BEGIN
    -- When a reservation is inserted or updated, populate guest info
    IF TG_OP = 'INSERT' OR TG_OP = 'UPDATE' THEN
        -- Get guest information
        SELECT full_name, email 
        INTO guest_record
        FROM public.users 
        WHERE id = NEW.guest_id;
        
        -- Get room information
        SELECT room_number 
        INTO room_record
        FROM public.rooms 
        WHERE id = NEW.room_id;
        
        -- Update the NEW record with the fetched data
        IF guest_record IS NOT NULL THEN
            NEW.guest_name := guest_record.full_name;
            NEW.guest_email := guest_record.email;
        END IF;
        
        IF room_record IS NOT NULL THEN
            NEW.room_number := room_record.room_number;
        END IF;
        
        RETURN NEW;
    END IF;
    
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to auto-populate guest data
CREATE TRIGGER trigger_sync_guest_data
    BEFORE INSERT OR UPDATE ON public.reservations
    FOR EACH ROW
    EXECUTE FUNCTION sync_guest_data_in_reservations();

-- ============================================================================
-- STEP 3: Verify the fix
-- ============================================================================

-- Show updated reservations
SELECT 
    'Updated Reservations' as info,
    r.id,
    r.guest_id,
    r.guest_name,
    r.guest_email,
    r.room_id,
    r.room_number,
    r.check_in_date,
    r.check_out_date,
    r.status,
    r.payment_status,
    r.total_amount,
    r.created_at
FROM public.reservations r
ORDER BY r.created_at DESC;

-- Test the trigger by updating a reservation
UPDATE public.reservations 
SET updated_at = NOW()
WHERE id = (SELECT id FROM public.reservations LIMIT 1);

-- Show final state after trigger test
SELECT 
    'Final State After Trigger Test' as info,
    r.id,
    r.guest_name,
    r.guest_email,
    r.room_number,
    r.status,
    r.total_amount
FROM public.reservations r
ORDER BY r.created_at DESC;

-- Instructions
DO $$
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '✅ RESERVATION GUEST DATA FIX COMPLETED!';
    RAISE NOTICE '';
    RAISE NOTICE '📋 WHAT WAS FIXED:';
    RAISE NOTICE '- Populated missing guest_name, guest_email, room_number columns';
    RAISE NOTICE '- Recreated the trigger to ensure future reservations work correctly';
    RAISE NOTICE '- Tested the trigger functionality';
    RAISE NOTICE '';
    RAISE NOTICE '🔄 NEXT STEPS:';
    RAISE NOTICE '1. Refresh the admin reservations screen in your app';
    RAISE NOTICE '2. Reservations should now display with proper guest information';
    RAISE NOTICE '3. Check the console logs for any remaining issues';
END;
$$;
