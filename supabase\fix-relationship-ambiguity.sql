-- FIX FOR "MORE THAN ONE RELATIONSHIP" ERROR BETWEEN RESERVATIONS AND PAYMENTS
-- Run this script in Supabase SQL Editor to resolve the relationship ambiguity

-- ============================================================================
-- PROBLEM: Two relationships exist between reservations and payments tables:
-- 1. reservations.payment_id → payments.id (redundant)
-- 2. payments.reservation_id → reservations.id (needed)
-- This causes Supabase to throw "more than one relationship" error
-- ============================================================================

-- SOLUTION: Remove the redundant relationship (payment_id from reservations)
-- Keep only: payments.reservation_id → reservations.id

-- Step 1: Remove the foreign key constraint
ALTER TABLE public.reservations DROP CONSTRAINT IF EXISTS fk_reservations_payment_id;

-- Step 2: Remove the redundant payment_id column from reservations table
ALTER TABLE public.reservations DROP COLUMN IF EXISTS payment_id;

-- Step 3: Verify the fix by checking table structure
SELECT 
    'Relationship ambiguity fixed! Only one relationship now exists: payments.reservation_id → reservations.id' as status;

-- Step 4: Show current table structure to confirm
SELECT 
    column_name, 
    data_type, 
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'reservations' 
    AND table_schema = 'public'
ORDER BY ordinal_position;
