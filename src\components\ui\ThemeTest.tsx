import React from 'react';
import { View, StyleSheet } from 'react-native';
import { Text, Card, Button } from 'react-native-paper';
import { useTheme } from '../../contexts/ThemeContext';
import { ThemeToggle } from './ThemeToggle';

export const ThemeTest: React.FC = () => {
  const { colors, isDark, themeMode } = useTheme();

  return (
    <Card style={[styles.container, { backgroundColor: colors.surface }]}>
      <Card.Content>
        <View style={styles.header}>
          <Text variant="titleLarge" style={{ color: colors.text }}>
            Theme System Test
          </Text>
          <ThemeToggle />
        </View>
        
        <View style={styles.info}>
          <Text style={{ color: colors.textSecondary }}>
            Current Mode: {themeMode}
          </Text>
          <Text style={{ color: colors.textSecondary }}>
            Is Dark: {isDark ? 'Yes' : 'No'}
          </Text>
        </View>

        <View style={styles.colorSamples}>
          <View style={[styles.colorBox, { backgroundColor: colors.primary }]}>
            <Text style={{ color: colors.white }}>Primary</Text>
          </View>
          <View style={[styles.colorBox, { backgroundColor: colors.accent }]}>
            <Text style={{ color: colors.white }}>Accent</Text>
          </View>
          <View style={[styles.colorBox, { backgroundColor: colors.surface, borderWidth: 1, borderColor: colors.border }]}>
            <Text style={{ color: colors.text }}>Surface</Text>
          </View>
        </View>

        <Text style={{ color: colors.text, marginTop: 16 }}>
          This text should change color based on the theme.
        </Text>
        
        <Text style={{ color: colors.textSecondary, marginTop: 8 }}>
          This is secondary text that should also adapt to the theme.
        </Text>
      </Card.Content>
    </Card>
  );
};

const styles = StyleSheet.create({
  container: {
    margin: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  info: {
    marginBottom: 16,
    gap: 4,
  },
  colorSamples: {
    flexDirection: 'row',
    gap: 8,
    marginBottom: 16,
  },
  colorBox: {
    flex: 1,
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
});
