# Fix for Duplicate Key Error in User Creation

## Problem Description

The error `"duplicate key value violates unique constraint \"users_pkey\""` occurs when trying to create a user profile that already exists in the `public.users` table. This typically happens due to:

1. **Race conditions** during user signup
2. **Multiple attempts** to create the same user profile
3. **Inconsistent state** between `auth.users` and `public.users` tables
4. **Failed cleanup** from previous signup attempts

## Root Cause Analysis

The issue was in the user creation flow where:

1. `supabase.auth.signUp()` creates a user in `auth.users`
2. Manual insertion into `public.users` table sometimes fails due to timing issues
3. The `ensureUserProfile()` function could also try to create the same profile
4. No proper handling of duplicate key constraints

## Implemented Fixes

### 1. Updated `authService.signUp()` in `src/services/supabase.ts`

**Changes:**
- Replaced `INSERT` with `UPSERT` operation
- Added `onConflict: 'id'` handling
- Better error handling for duplicate entries

```typescript
// Before: INSERT (could fail on duplicates)
const { error: profileError } = await supabase
  .from('users')
  .insert([{ id: authData.user.id, ... }]);

// After: UPSERT (handles duplicates gracefully)
const { error: profileError } = await supabase
  .from('users')
  .upsert([{ id: authData.user.id, ... }], {
    onConflict: 'id',
    ignoreDuplicates: false
  });
```

### 2. Improved `ensureUserProfile()` Function

**Changes:**
- Replaced check-then-insert pattern with direct upsert
- Added specific handling for duplicate key errors (code 23505)
- More robust error handling

```typescript
// Before: Check existence, then insert
const { data: existingProfile } = await supabase
  .from('users')
  .select('id')
  .eq('id', user.id)
  .single();

if (!existingProfile) {
  // Insert logic
}

// After: Direct upsert
const { error } = await supabase
  .from('users')
  .upsert([userData], {
    onConflict: 'id',
    ignoreDuplicates: false
  });
```

### 3. Enhanced Error Handling in `authStore.ts`

**Changes:**
- Added specific error messages for duplicate key constraints
- Better user feedback for different error scenarios
- Graceful handling of database errors

### 4. Database Cleanup Script (`supabase/fix-duplicate-users.sql`)

**Features:**
- Removes orphaned user profiles
- Creates missing profiles for existing auth users
- Updates RLS policies for better permission handling
- Adds utility function for safe user profile creation
- Provides verification queries

### 5. Added Utility Functions

**New Functions:**
- `createUserProfileSafe()` - Uses database function with fallback
- `testUserCreation()` - For testing the fixes
- `debugUserCreationError()` - For debugging issues

## How to Apply the Fixes

### Step 1: Run Database Cleanup
```sql
-- Execute the cleanup script in Supabase SQL Editor
-- File: supabase/fix-duplicate-users.sql
```

### Step 2: Verify Code Changes
The following files have been updated:
- `src/services/supabase.ts` - Core auth service fixes
- `src/store/authStore.ts` - Better error handling
- `src/utils/testUserCreation.ts` - Testing utilities

### Step 3: Test the Fixes
```typescript
import { testUserCreation } from './src/utils/testUserCreation';

// Run this to test user creation
await testUserCreation();
```

## Prevention Strategies

### 1. Always Use Upsert for User Profiles
```typescript
// Good: Use upsert
await supabase.from('users').upsert([userData], { onConflict: 'id' });

// Avoid: Direct insert without conflict handling
await supabase.from('users').insert([userData]);
```

### 2. Handle Specific Error Codes
```typescript
if (error.code === '23505') {
  // Handle duplicate key constraint
  console.log('User profile already exists');
}
```

### 3. Use Database Functions for Complex Operations
```sql
-- Create a database function for safe user creation
CREATE OR REPLACE FUNCTION create_user_profile(...)
RETURNS VOID AS $$
BEGIN
  INSERT INTO users (...) VALUES (...)
  ON CONFLICT (id) DO UPDATE SET ...;
END;
$$ LANGUAGE plpgsql;
```

## Testing Checklist

- [ ] New user signup works without errors
- [ ] Duplicate signup attempts are handled gracefully
- [ ] Existing users can still sign in
- [ ] Profile data is correctly created/updated
- [ ] Error messages are user-friendly
- [ ] Database constraints are respected

## Monitoring

Watch for these error patterns:
- `duplicate key value violates unique constraint`
- `User already registered`
- Profile creation failures
- Auth state inconsistencies

## Rollback Plan

If issues occur:
1. Revert code changes in `src/services/supabase.ts` and `src/store/authStore.ts`
2. Run the original schema without the new policies
3. Re-enable any disabled triggers if they were working before

## Additional Notes

- The fixes maintain backward compatibility
- No data loss should occur during the update
- Users can continue using the app during the fix deployment
- The cleanup script is idempotent and safe to run multiple times
