-- ============================================================================
-- DEBUG ADMIN RESERVATIONS ISSUE
-- This script helps diagnose why admin users can't see reservations
-- ============================================================================

-- ============================================================================
-- STEP 1: Check current database state
-- ============================================================================

-- Check if there are any reservations in the database
SELECT 
    'Total Reservations in Database' as info,
    COUNT(*) as count
FROM public.reservations;

-- Show all reservations with guest info
SELECT 
    'All Reservations' as info,
    r.id,
    r.guest_id,
    r.guest_name,
    r.guest_email,
    r.room_id,
    r.room_number,
    r.check_in_date,
    r.check_out_date,
    r.status,
    r.payment_status,
    r.total_amount,
    r.created_at
FROM public.reservations r
ORDER BY r.created_at DESC
LIMIT 10;

-- Check users and their roles
SELECT 
    'User Roles' as info,
    u.email,
    u.role,
    u.full_name,
    u.created_at
FROM public.users u
ORDER BY u.created_at DESC;

-- ============================================================================
-- STEP 2: Check RLS policies
-- ============================================================================

-- Show current RLS policies for reservations table
SELECT 
    'Current RLS Policies for Reservations' as info,
    schemaname,
    tablename,
    policyname,
    permissive,
    roles,
    cmd,
    qual,
    with_check
FROM pg_policies 
WHERE tablename = 'reservations';

-- ============================================================================
-- STEP 3: Test admin access to reservations
-- ============================================================================

-- This will be run as the current authenticated user
-- If you're logged in as admin, this should show all reservations
SELECT 
    'Test Admin Access' as info,
    COUNT(*) as accessible_reservations
FROM public.reservations;

-- ============================================================================
-- STEP 4: Fix RLS policies if needed
-- ============================================================================

-- Drop existing problematic policies
DROP POLICY IF EXISTS "Staff can view all reservations" ON public.reservations;
DROP POLICY IF EXISTS "Staff can manage all reservations" ON public.reservations;
DROP POLICY IF EXISTS "reservations_select_all_authenticated" ON public.reservations;
DROP POLICY IF EXISTS "reservations_all_authenticated" ON public.reservations;

-- Create new, working policies for admin access
-- Allow authenticated users to view all reservations (this covers admin/receptionist)
CREATE POLICY "authenticated_users_can_view_all_reservations" ON public.reservations
    FOR SELECT USING (auth.role() = 'authenticated');

-- Allow authenticated users to manage all reservations (for admin/receptionist operations)
CREATE POLICY "authenticated_users_can_manage_all_reservations" ON public.reservations
    FOR ALL USING (auth.role() = 'authenticated');

-- ============================================================================
-- STEP 5: Verify the fix
-- ============================================================================

-- Test the new policies
SELECT 
    'After Policy Fix - Test Access' as info,
    COUNT(*) as accessible_reservations
FROM public.reservations;

-- Show updated policies
SELECT 
    'Updated RLS Policies' as info,
    policyname,
    cmd,
    qual
FROM pg_policies 
WHERE tablename = 'reservations'
ORDER BY policyname;

-- ============================================================================
-- STEP 6: Create sample data if database is empty
-- ============================================================================

-- Check if we need sample data
DO $$
DECLARE
    reservation_count INTEGER;
    sample_user_id UUID;
    sample_room_id UUID;
BEGIN
    -- Count existing reservations
    SELECT COUNT(*) INTO reservation_count FROM public.reservations;
    
    IF reservation_count = 0 THEN
        RAISE NOTICE 'No reservations found. Creating sample data...';
        
        -- Get a user ID (preferably admin)
        SELECT id INTO sample_user_id 
        FROM public.users 
        WHERE role = 'admin' OR email LIKE '%admin%'
        LIMIT 1;
        
        -- If no admin, get any user
        IF sample_user_id IS NULL THEN
            SELECT id INTO sample_user_id 
            FROM public.users 
            LIMIT 1;
        END IF;
        
        -- Get a room ID
        SELECT id INTO sample_room_id 
        FROM public.rooms 
        LIMIT 1;
        
        -- Create sample reservations if we have both user and room
        IF sample_user_id IS NOT NULL AND sample_room_id IS NOT NULL THEN
            INSERT INTO public.reservations (
                guest_id,
                room_id,
                check_in_date,
                check_out_date,
                total_amount,
                status,
                payment_status,
                special_requests
            ) VALUES 
            (
                sample_user_id,
                sample_room_id,
                CURRENT_DATE + INTERVAL '1 day',
                CURRENT_DATE + INTERVAL '3 days',
                15000.00,
                'confirmed',
                'paid',
                'Sample reservation for testing'
            ),
            (
                sample_user_id,
                sample_room_id,
                CURRENT_DATE + INTERVAL '5 days',
                CURRENT_DATE + INTERVAL '7 days',
                12000.00,
                'pending',
                'pending',
                'Another sample reservation'
            );
            
            RAISE NOTICE 'Sample reservations created successfully!';
        ELSE
            RAISE NOTICE 'Cannot create sample data - missing users or rooms';
        END IF;
    ELSE
        RAISE NOTICE 'Found % existing reservations', reservation_count;
    END IF;
END;
$$;

-- ============================================================================
-- STEP 7: Final verification
-- ============================================================================

-- Show final state
SELECT 
    'Final State - All Reservations' as info,
    r.id,
    r.guest_name,
    r.guest_email,
    r.room_number,
    r.status,
    r.total_amount,
    r.created_at
FROM public.reservations r
ORDER BY r.created_at DESC;

-- Instructions
DO $$
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '🔧 ADMIN RESERVATIONS DEBUG COMPLETED!';
    RAISE NOTICE '';
    RAISE NOTICE '📋 NEXT STEPS:';
    RAISE NOTICE '1. 🔄 Refresh the admin reservations screen';
    RAISE NOTICE '2. 🔍 Check the console logs for detailed debugging info';
    RAISE NOTICE '3. ✅ Reservations should now be visible to admin users';
    RAISE NOTICE '';
    RAISE NOTICE '🐛 If still not working:';
    RAISE NOTICE '- Check that you are logged in as admin user';
    RAISE NOTICE '- Verify the role sync script was run successfully';
    RAISE NOTICE '- Look at the console logs for specific error messages';
END;
$$;
