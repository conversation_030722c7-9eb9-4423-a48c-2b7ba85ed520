import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ImageBackground,
  SafeAreaView,
  StatusBar,
} from 'react-native';
import CustomButton from '../../components/ui/CustomButton';
import { COLORS, TYPOGRAPHY, SPACING } from '../../constants';

interface WelcomeScreenProps {
  navigation: any;
}

export default function WelcomeScreen({ navigation }: WelcomeScreenProps) {
  return (
    <ImageBackground
      source={{
        uri: 'https://images.unsplash.com/photo-1571896349842-33c89424de2d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80'
      }}
      style={styles.background}
    >
      <StatusBar barStyle="light-content" />
      <View style={styles.overlay}>
        <SafeAreaView style={styles.container}>
          <View style={styles.content}>
            <View style={styles.header}>
              <Text style={styles.title}>Sunset View Hotel</Text>
              <Text style={styles.subtitle}>
                Experience luxury and comfort in the heart of paradise
              </Text>
            </View>

            <View style={styles.footer}>
              <Text style={styles.welcomeText}>
                Welcome! Ready to book your perfect stay?
              </Text>

              <View style={styles.buttonContainer}>
                <CustomButton
                  title="Sign In"
                  onPress={() => navigation.navigate('Login')}
                  variant="primary"
                  size="large"
                  style={styles.button}
                />

                <CustomButton
                  title="Create Account"
                  onPress={() => navigation.navigate('Register')}
                  variant="outline"
                  size="large"
                  style={[styles.button, styles.outlineButton]}
                  textStyle={styles.outlineButtonText}
                />
              </View>

              <Text style={styles.footerText}>
                Book rooms • Manage reservations • Secure payments
              </Text>
            </View>
          </View>
        </SafeAreaView>
      </View>
    </ImageBackground>
  );
}

const styles = StyleSheet.create({
  background: {
    flex: 1,
  },
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    justifyContent: 'space-between',
    paddingHorizontal: SPACING.lg,
    paddingTop: SPACING.xxl,
    paddingBottom: SPACING.xl,
  },
  header: {
    alignItems: 'center',
    marginTop: SPACING.xxl,
  },
  title: {
    ...TYPOGRAPHY.h1,
    fontSize: 36,
    color: '#fff',
    textAlign: 'center',
    marginBottom: SPACING.md,
    fontWeight: '700',
    textShadowColor: 'rgba(0, 0, 0, 0.5)',
    textShadowOffset: { width: 0, height: 2 },
    textShadowRadius: 4,
  },
  subtitle: {
    ...TYPOGRAPHY.body1,
    color: '#fff',
    textAlign: 'center',
    opacity: 0.9,
    lineHeight: 24,
    textShadowColor: 'rgba(0, 0, 0, 0.5)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
  footer: {
    alignItems: 'center',
  },
  welcomeText: {
    ...TYPOGRAPHY.h4,
    color: '#fff',
    textAlign: 'center',
    marginBottom: SPACING.xl,
    textShadowColor: 'rgba(0, 0, 0, 0.5)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
  buttonContainer: {
    width: '100%',
    paddingHorizontal: SPACING.md,
  },
  button: {
    marginBottom: SPACING.md,
    minHeight: 50,
  },
  outlineButton: {
    borderColor: '#fff',
    borderWidth: 2,
  },
  outlineButtonText: {
    color: '#fff',
  },
  footerText: {
    ...TYPOGRAPHY.body2,
    color: '#fff',
    textAlign: 'center',
    opacity: 0.8,
    marginTop: SPACING.lg,
    textShadowColor: 'rgba(0, 0, 0, 0.5)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
});
