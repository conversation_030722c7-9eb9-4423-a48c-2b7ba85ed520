import { useMemo } from 'react';
import { useAuthStore } from '../store/authStore';
import { permissionsService, type PermissionCheck } from '../services/permissionsService';

/**
 * Hook to access user permissions throughout the app
 * This provides a React-friendly way to check permissions
 */
export const usePermissions = () => {
  const { user } = useAuthStore();

  // Memoize permissions to avoid unnecessary recalculations
  const permissions = useMemo((): PermissionCheck => {
    if (!user) {
      // Return guest permissions if no user
      return {
        canViewAllUsers: false,
        canManageUsers: false,
        canManageRooms: false,
        canViewAllReservations: false,
        canManageReservations: false,
        canViewAllPayments: false,
        canManagePayments: false,
        canUploadImages: false,
      };
    }

    return permissionsService.getPermissions();
  }, [user?.role, user?.id]);

  // Individual permission checks for convenience
  const canViewAllUsers = useMemo(() => permissions.canViewAllUsers, [permissions]);
  const canManageUsers = useMemo(() => permissions.canManageUsers, [permissions]);
  const canManageRooms = useMemo(() => permissions.canManageRooms, [permissions]);
  const canViewAllReservations = useMemo(() => permissions.canViewAllReservations, [permissions]);
  const canManageReservations = useMemo(() => permissions.canManageReservations, [permissions]);
  const canViewAllPayments = useMemo(() => permissions.canViewAllPayments, [permissions]);
  const canManagePayments = useMemo(() => permissions.canManagePayments, [permissions]);
  const canUploadImages = useMemo(() => permissions.canUploadImages, [permissions]);

  // Helper functions for specific access checks
  const canAccessReservation = (reservationGuestId: string): boolean => {
    return permissionsService.canAccessReservation(reservationGuestId);
  };

  const canAccessPayment = (paymentGuestId: string): boolean => {
    return permissionsService.canAccessPayment(paymentGuestId);
  };

  // Role-based checks for convenience
  const isAdmin = user?.role === 'admin';
  const isReceptionist = user?.role === 'receptionist';
  const isStaff = isAdmin || isReceptionist;
  const isGuest = user?.role === 'guest' || !user;

  return {
    // Full permissions object
    permissions,
    
    // Individual permission flags
    canViewAllUsers,
    canManageUsers,
    canManageRooms,
    canViewAllReservations,
    canManageReservations,
    canViewAllPayments,
    canManagePayments,
    canUploadImages,
    
    // Helper functions
    canAccessReservation,
    canAccessPayment,
    
    // Role checks
    isAdmin,
    isReceptionist,
    isStaff,
    isGuest,
    
    // User info
    user,
    userRole: user?.role || 'guest',
    userId: user?.id || null,
  };
};
