// Custom type declarations for React Native
import 'react-native';

declare module 'react-native' {
  export interface StatusBarStatic {
    currentHeight?: number;
    setBarStyle: (style: 'default' | 'light-content' | 'dark-content', animated?: boolean) => void;
    setBackgroundColor: (color: string, animated?: boolean) => void;
    setHidden: (hidden: boolean, animation?: 'fade' | 'slide') => void;
  }
  
  export interface PlatformStatic {
    OS: 'ios' | 'android' | 'web' | 'windows' | 'macos';
    Version: number;
    select<T>(specifics: { ios?: T; android?: T; web?: T; windows?: T; macos?: T; default?: T }): T;
  }
  
  export const StatusBar: StatusBarStatic;
  export const Platform: PlatformStatic;
}
