// Navigation types for React Navigation
import type { Room, Reservation, Payment } from './database';

// We'll define our own versions of the navigation types
// This avoids issues with mismatched versions from React Navigation

// Type for composite screen props
type CompositeScreenProps<T, S> = T & S;

// Type for navigator screen parameters
type NavigatorScreenParams<T> = {
  [K in keyof T]?: object | undefined;
};

// Define screen props types
type StackScreenProps<T, K extends keyof T> = {
  navigation: {
    navigate: (screen: string, params?: object) => void;
    goBack: () => void;
    push: (screen: string, params?: object) => void;
    pop: (count?: number) => void;
    popToTop: () => void;
    setParams: (params: Partial<T[K]>) => void;
    addListener: (event: string, callback: () => void) => () => void;
    dispatch: (action: object) => void;
  };
  route: {
    key: string;
    name: K;
    params: T[K];
  };
};

// Define bottom tab screen props type
type BottomTabScreenProps<T, K extends keyof T> = StackScreenProps<T, K>;

// Root Navigator
export type RootStackParamList = {
  Loading: undefined;
  Auth: NavigatorScreenParams<AuthStackParamList>;
  Guest: NavigatorScreenParams<GuestTabParamList>;
  Admin: NavigatorScreenParams<AdminTabParamList>;
};

// Auth Navigator
export type AuthStackParamList = {
  Welcome: undefined;
  Login: undefined;
  Register: undefined;
  ForgotPassword: undefined;
};

// Guest Tab Navigator
export type GuestTabParamList = {
  Home: undefined;
  Rooms: undefined;
  Reservations: undefined;
  Profile: undefined;
};

// Guest Stack Navigator (for screens within tabs)
export type GuestStackParamList = {
  HomeMain: undefined;
  RoomsMain: undefined;
  ReservationsMain: undefined;
  ProfileMain: undefined;
  RoomDetails: {
    roomId: string;
    room?: Room;
  };
  Booking: {
    room: Room;
    checkIn: string;
    checkOut: string;
    guests: number;
  };
  Payment: {
    reservation: Partial<Reservation>;
    totalAmount: number;
  };
  Receipt: {
    reservationId: string;
    paymentId?: string;
  };
  ReservationDetails: {
    reservationId: string;
    reservation?: Reservation;
  };
};

// Admin Tab Navigator
export type AdminTabParamList = {
  Dashboard: undefined;
  Rooms: undefined;
  Reservations: undefined;
  Analytics: undefined;
  Settings: undefined;
};

// Admin Stack Navigator
export type AdminStackParamList = {
  AdminDashboard: undefined;
  AdminRooms: undefined;
  AddEditRoom: {
    roomId?: string;
    room?: Room;
    mode: 'add' | 'edit';
  };
  AdminReservations: undefined;
  ReservationDetails: {
    reservationId: string;
    reservation?: Reservation;
  };
  AdminAnalytics: undefined;
  AdminSettings: undefined;
};

// Screen Props Types
export type RootStackScreenProps<T extends keyof RootStackParamList> = StackScreenProps<
  RootStackParamList,
  T
>;

export type AuthStackScreenProps<T extends keyof AuthStackParamList> = StackScreenProps<
  AuthStackParamList,
  T
>;

export type GuestTabScreenProps<T extends keyof GuestTabParamList> = CompositeScreenProps<
  BottomTabScreenProps<GuestTabParamList, T>,
  RootStackScreenProps<keyof RootStackParamList>
>;

export type GuestStackScreenProps<T extends keyof GuestStackParamList> = StackScreenProps<
  GuestStackParamList,
  T
>;

export type AdminTabScreenProps<T extends keyof AdminTabParamList> = CompositeScreenProps<
  BottomTabScreenProps<AdminTabParamList, T>,
  RootStackScreenProps<keyof RootStackParamList>
>;

export type AdminStackScreenProps<T extends keyof AdminStackParamList> = StackScreenProps<
  AdminStackParamList,
  T
>;

// Combined types for screens that can be in both tab and stack navigators
export type HomeScreenProps = GuestTabScreenProps<'Home'>;
export type RoomsScreenProps = GuestTabScreenProps<'Rooms'>;
export type ReservationsScreenProps = GuestTabScreenProps<'Reservations'>;
export type ProfileScreenProps = GuestTabScreenProps<'Profile'>;

export type RoomDetailsScreenProps = GuestStackScreenProps<'RoomDetails'>;
export type BookingScreenProps = GuestStackScreenProps<'Booking'>;
export type PaymentScreenProps = GuestStackScreenProps<'Payment'>;

export type AdminDashboardScreenProps = AdminTabScreenProps<'Dashboard'>;
export type AdminRoomsScreenProps = AdminTabScreenProps<'Rooms'>;
export type AdminReservationsScreenProps = AdminTabScreenProps<'Reservations'>;
export type AdminAnalyticsScreenProps = AdminTabScreenProps<'Analytics'>;
export type AdminSettingsScreenProps = AdminTabScreenProps<'Settings'>;

export type AddEditRoomScreenProps = AdminStackScreenProps<'AddEditRoom'>;
export type ReservationDetailsScreenProps = AdminStackScreenProps<'ReservationDetails'>;

// Auth screen props
export type WelcomeScreenProps = AuthStackScreenProps<'Welcome'>;
export type LoginScreenProps = AuthStackScreenProps<'Login'>;
export type RegisterScreenProps = AuthStackScreenProps<'Register'>;
export type ForgotPasswordScreenProps = AuthStackScreenProps<'ForgotPassword'>;

// Navigation helpers
declare global {
  namespace ReactNavigation {
    interface RootParamList extends RootStackParamList {}
  }
}

// Custom hook types for navigation
export interface UseNavigationOptions {
  resetOnBlur?: boolean;
  gestureEnabled?: boolean;
  animationEnabled?: boolean;
}

// Deep linking types
export type DeepLinkParams = {
  screen: string;
  params?: Record<string, any>;
};

export type LinkingConfig = {
  prefixes: string[];
  config: {
    screens: Record<string, string | { path: string; exact?: boolean }>;
  };
};

// Notification navigation types
export interface NotificationNavigationData {
  screen: keyof RootStackParamList;
  params?: Record<string, any>;
  tab?: string;
}

// Screen options types
export interface ScreenOptions {
  title?: string;
  headerShown?: boolean;
  headerBackTitleVisible?: boolean;
  headerStyle?: Record<string, any>;
  headerTitleStyle?: Record<string, any>;
  headerTintColor?: string;
  gestureEnabled?: boolean;
  animationEnabled?: boolean;
}

// Tab bar options
export interface TabBarOptions {
  tabBarLabel?: string;
  tabBarIcon?: ({ focused, color, size }: { focused: boolean; color: string; size: number }) => React.ReactNode;
  tabBarBadge?: string | number;
  tabBarVisible?: boolean;
  tabBarStyle?: Record<string, any>;
}

// Form navigation data
export interface BookingFormData {
  room: Room;
  checkInDate: string;
  checkOutDate: string;
  guests: number;
  specialRequests?: string;
}

export interface PaymentFormData {
  reservation: Partial<Reservation>;
  paymentMethod: 'card' | 'bank_transfer' | 'ussd';
  totalAmount: number;
}

// Search and filter navigation
export interface RoomSearchParams {
  checkIn?: string;
  checkOut?: string;
  guests?: number;
  roomType?: string;
  minPrice?: number;
  maxPrice?: number;
  amenities?: string[];
}

export interface ReservationFilterParams {
  status?: string;
  startDate?: string;
  endDate?: string;
  guestName?: string;
  roomNumber?: string;
}
