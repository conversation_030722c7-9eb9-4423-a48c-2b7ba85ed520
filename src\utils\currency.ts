/**
 * Currency utility functions for Kenyan Shilling (KES)
 */

export const CURRENCY_CONFIG = {
  code: 'KES',
  symbol: 'KSh',
  locale: 'en-KE',
  name: 'Kenyan Shilling',
  // Paystack uses cents for KES (1 KES = 100 cents)
  centsFactor: 100,
};

/**
 * Format price in Kenyan Shillings
 * @param price - Price in KES
 * @param options - Formatting options
 * @returns Formatted price string
 */
export const formatPrice = (
  price: number,
  options: {
    showSymbol?: boolean;
    minimumFractionDigits?: number;
    maximumFractionDigits?: number;
  } = {}
): string => {
  const {
    showSymbol = true,
    minimumFractionDigits = 0,
    maximumFractionDigits = 0,
  } = options;

  if (showSymbol) {
    return new Intl.NumberFormat(CURRENCY_CONFIG.locale, {
      style: 'currency',
      currency: CURRENCY_CONFIG.code,
      minimumFractionDigits,
      maximumFractionDigits,
    }).format(price);
  } else {
    return new Intl.NumberFormat(CURRENCY_CONFIG.locale, {
      minimumFractionDigits,
      maximumFractionDigits,
    }).format(price);
  }
};

/**
 * Format price with custom symbol (KSh)
 * @param price - Price in KES
 * @param options - Formatting options
 * @returns Formatted price string with KSh symbol
 */
export const formatPriceWithSymbol = (
  price: number,
  options: {
    minimumFractionDigits?: number;
    maximumFractionDigits?: number;
  } = {}
): string => {
  const {
    minimumFractionDigits = 0,
    maximumFractionDigits = 0,
  } = options;

  const formattedNumber = new Intl.NumberFormat(CURRENCY_CONFIG.locale, {
    minimumFractionDigits,
    maximumFractionDigits,
  }).format(price);

  return `${CURRENCY_CONFIG.symbol} ${formattedNumber}`;
};

/**
 * Convert KES to cents for Paystack
 * @param amount - Amount in KES
 * @returns Amount in cents
 */
export const toCents = (amount: number): number => {
  return Math.round(amount * CURRENCY_CONFIG.centsFactor);
};

/**
 * Convert cents to KES from Paystack
 * @param cents - Amount in cents
 * @returns Amount in KES
 */
export const fromCents = (cents: number): number => {
  return cents / CURRENCY_CONFIG.centsFactor;
};

/**
 * Validate if amount is a valid currency value
 * @param amount - Amount to validate
 * @returns True if valid, false otherwise
 */
export const isValidAmount = (amount: number): boolean => {
  return !isNaN(amount) && amount >= 0 && isFinite(amount);
};

/**
 * Parse string to number for currency calculations
 * @param value - String value to parse
 * @returns Parsed number or 0 if invalid
 */
export const parseAmount = (value: string): number => {
  const parsed = parseFloat(value.replace(/[^\d.-]/g, ''));
  return isValidAmount(parsed) ? parsed : 0;
};

/**
 * Calculate total with tax (if applicable)
 * @param subtotal - Subtotal amount
 * @param taxRate - Tax rate (e.g., 0.16 for 16% VAT)
 * @returns Total amount including tax
 */
export const calculateTotal = (subtotal: number, taxRate: number = 0): number => {
  return subtotal * (1 + taxRate);
};

/**
 * Format price range
 * @param minPrice - Minimum price
 * @param maxPrice - Maximum price
 * @returns Formatted price range string
 */
export const formatPriceRange = (minPrice: number, maxPrice: number): string => {
  if (minPrice === maxPrice) {
    return formatPrice(minPrice);
  }
  return `${formatPrice(minPrice)} - ${formatPrice(maxPrice)}`;
};
