import React, { useEffect, useState } from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { MaterialIcons } from '@expo/vector-icons';
import { useAuthStore } from '../store/authStore';
import { usePermissions } from '../hooks/usePermissions';
import { colors } from '../constants';

// Screen imports
import LoadingScreen from '../screens/LoadingScreen';
import AuthNavigator from './AuthNavigator';
import GuestNavigator from './GuestNavigator';
import { AdminNavigator } from './AdminNavigator';

const Stack = createNativeStackNavigator();
const Tab = createBottomTabNavigator();

// Main Tab Navigator for authenticated users
function MainTabNavigator() {
  const { isStaff } = usePermissions();

  if (isStaff) {
    return <AdminNavigator />;
  }

  return <GuestNavigator />;
}

// Root Navigator
export const AppNavigator = () => {
  const { user, session, loading, initialize } = useAuthStore();
  const [isInitializing, setIsInitializing] = useState(true);

  useEffect(() => {
    const initializeAuth = async () => {
      await initialize();
      setIsInitializing(false);
    };
    initializeAuth();
  }, []);

  if (isInitializing || loading) {
    return <LoadingScreen />;
  }

  return (
    <NavigationContainer>
      <Stack.Navigator screenOptions={{ headerShown: false }}>
        {user && session ? (
          <Stack.Screen name="Main" component={MainTabNavigator} />
        ) : (
          <Stack.Screen name="Auth" component={AuthNavigator} />
        )}
      </Stack.Navigator>
    </NavigationContainer>
  );
};
