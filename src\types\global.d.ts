// Global type declarations

// Node.js global variables
declare var process: {
  env: {
    [key: string]: string | undefined;
    EXPO_PUBLIC_SUPABASE_URL?: string;
    EXPO_PUBLIC_SUPABASE_ANON_KEY?: string;
    EXPO_PUBLIC_PAYSTACK_PUBLIC_KEY?: string;
  };
};

// Jest globals for testing
declare global {
  var __DEV__: boolean;
  var testNotifications: any;

  // Jest testing framework globals
  var describe: (name: string, fn: () => void) => void;
  var it: (name: string, fn: () => void) => void;
  var expect: (value: any) => any;
  var beforeEach: (fn: () => void) => void;
  var afterEach: (fn: () => void) => void;
  var beforeAll: (fn: () => void) => void;
  var afterAll: (fn: () => void) => void;
}

// Global type augmentations

// Declare UI and navigation libraries
declare module 'react-native' {
  export const View: any;
  export const Text: any;
  export const StyleSheet: any;
  export const ScrollView: any;
  export const Alert: any;
  export const Image: any;
  export const TouchableOpacity: any;
  export const TextInput: any;
  export const Switch: any;
  export const FlatList: any;
  export const RefreshControl: any;
  export const Dimensions: any;
  export const ActivityIndicator: any;
  export const SafeAreaView: any;
  export const KeyboardAvoidingView: any;
  export const Platform: any;
  export const ImageBackground: any;
  export const StatusBar: any;
  export const Animated: any;
  export interface TextInputProps {
    value?: string;
    onChangeText?: (text: string) => void;
    placeholder?: string;
    style?: any;
    [key: string]: any;
  }
  export interface ViewStyle {
    [key: string]: any;
  }
  export interface TextStyle {
    [key: string]: any;
  }
  export interface ViewProps {
    style?: ViewStyle;
    [key: string]: any;
  }
  export * from 'react-native';
}
declare module 'react-native-paper' {
  export const Text: any;
  export const Card: any;
  export const Button: any;
  export const Avatar: any;
  export const Chip: any;
  export const Divider: any;
  export const IconButton: any;
  export const PaperProvider: any;
  export const Surface: any;
  export * from 'react-native-paper';
}
declare module '@react-navigation/native' {
  export const useNavigation: any;
  export const useRoute: any;
  export const useFocusEffect: any;
  export const NavigationContainer: any;
  export interface NavigationProp<T = any> {
    navigate: (name: string, params?: any) => void;
    goBack: () => void;
    [key: string]: any;
  }
  export * from '@react-navigation/native';
}
declare module '@react-navigation/native-stack' {
  export const createNativeStackNavigator: any;
  export * from '@react-navigation/native-stack';
}
declare module '@react-navigation/bottom-tabs' {
  export const createBottomTabNavigator: any;
  export * from '@react-navigation/bottom-tabs';
}
declare module '@react-navigation/stack' {
  export const createStackNavigator: any;
  export * from '@react-navigation/stack';
}

// Declare Expo modules
declare module '@expo/vector-icons' {
  export const MaterialIcons: any;
  export const Ionicons: any;
  export * from '@expo/vector-icons';
}
declare module 'expo-image-picker' {
  export const MediaTypeOptions: any;
  export const CameraOptions: any;
  export const ImagePickerOptions: any;
  export const Asset: any;
  export const requestCameraPermissionsAsync: any;
  export const requestMediaLibraryPermissionsAsync: any;
  export const launchCameraAsync: any;
  export const launchImageLibraryAsync: any;
  export * from 'expo-image-picker';
}
declare module 'expo-image-manipulator' {
  export const Action: any;
  export const SaveFormat: any;
  export const manipulateAsync: any;
  export * from 'expo-image-manipulator';
}
declare module 'expo-device';
declare module 'expo-notifications';
declare module 'expo-file-system';
declare module 'expo-sharing';
declare module 'react-native-html-to-pdf';

// Declare other modules
declare module 'react-native-super-grid';
declare module 'react-native-modal-datetime-picker';
declare module '@react-native-community/datetimepicker';
declare module 'react-native-toast-message';
declare module 'react-native-chart-kit';
declare module 'react-native-paystack-webview' {
  export interface PaystackProps {
    paystackKey: string;
    amount: number;
    billingEmail: string;
    billingName?: string;
    billingMobile?: string;
    currency?: string;
    reference?: string;
    channels?: string[];
    metadata?: any;
    onCancel: () => void;
    onSuccess: (response: any) => void;
    autoStart?: boolean;
  }

  export const Paystack: React.ComponentType<PaystackProps>;
}
declare module 'expo-status-bar' {
  export const StatusBar: any;
  export * from 'expo-status-bar';
}
declare module 'react-native-safe-area-context' {
  export const SafeAreaProvider: any;
  export const SafeAreaView: any;
  export const useSafeAreaInsets: any;
  export * from 'react-native-safe-area-context';
}
declare module '@supabase/supabase-js' {
  export const createClient: any;
  export interface RealtimeChannel {
    [key: string]: any;
  }
  export interface PostgrestError {
    message: string;
    details?: string;
    hint?: string;
    code?: string;
  }
  export interface AuthError {
    message: string;
    status?: number;
  }
  export * from '@supabase/supabase-js';
}
declare module 'zustand' {
  export const create: any;
  export * from 'zustand';
}

declare module 'zustand/middleware' {
  export const persist: any;
  export const createJSONStorage: any;
  export * from 'zustand/middleware';
}

declare module 'react-native-pager-view' {
  export default class PagerView extends React.Component<any> {
    setPage(index: number): void;
  }
  export interface NativeProps {
    children?: React.ReactNode;
    style?: any;
    initialPage?: number;
    onPageSelected?: (e: any) => void;
    ref?: React.RefObject<PagerView>;
  }
}

// For image imports with require()
declare module '*.png' {
  const content: any;
  export default content;
}
declare module '*.jpg' {
  const content: any;
  export default content;
}
declare module '*.jpeg' {
  const content: any;
  export default content;
}
declare module '*.gif' {
  const content: any;
  export default content;
}
declare module '*.svg' {
  const content: any;
  export default content;
}

// Add any other modules that need declarations
