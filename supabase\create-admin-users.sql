-- ============================================================================
-- CREATE ADMIN USERS FOR SUNSET VIEW HOTEL
-- This script creates admin and receptionist users for the hotel management system
-- ============================================================================

-- Note: This script creates users directly in the auth.users table
-- You'll need to run this with service role permissions

-- ============================================================================
-- STEP 1: Create Admin User
-- ============================================================================

-- Insert admin user into auth.users table
-- Replace the email and password with your desired admin credentials
INSERT INTO auth.users (
    instance_id,
    id,
    aud,
    role,
    email,
    encrypted_password,
    email_confirmed_at,
    raw_app_meta_data,
    raw_user_meta_data,
    created_at,
    updated_at,
    confirmation_token,
    email_change,
    email_change_token_new,
    recovery_token
) VALUES (
    '00000000-0000-0000-0000-000000000000',
    gen_random_uuid(),
    'authenticated',
    'authenticated',
    '<EMAIL>',
    crypt('AdminPassword123!', gen_salt('bf')), -- Change this password!
    NOW(),
    '{"provider": "email", "providers": ["email"]}',
    '{"full_name": "Hotel Administrator", "phone": "+************", "role": "admin"}',
    NOW(),
    NOW(),
    '',
    '',
    '',
    ''
) ON CONFLICT (email) DO NOTHING;

-- ============================================================================
-- STEP 2: Create Receptionist User
-- ============================================================================

INSERT INTO auth.users (
    instance_id,
    id,
    aud,
    role,
    email,
    encrypted_password,
    email_confirmed_at,
    raw_app_meta_data,
    raw_user_meta_data,
    created_at,
    updated_at,
    confirmation_token,
    email_change,
    email_change_token_new,
    recovery_token
) VALUES (
    '00000000-0000-0000-0000-000000000000',
    gen_random_uuid(),
    'authenticated',
    'authenticated',
    '<EMAIL>',
    crypt('ReceptionPassword123!', gen_salt('bf')), -- Change this password!
    NOW(),
    '{"provider": "email", "providers": ["email"]}',
    '{"full_name": "Hotel Receptionist", "phone": "+************", "role": "receptionist"}',
    NOW(),
    NOW(),
    '',
    '',
    '',
    ''
) ON CONFLICT (email) DO NOTHING;

-- ============================================================================
-- STEP 3: Create corresponding profiles in public.users table
-- ============================================================================

-- Create admin profile
INSERT INTO public.users (id, email, full_name, phone, role)
SELECT 
    au.id,
    au.email,
    au.raw_user_meta_data->>'full_name',
    au.raw_user_meta_data->>'phone',
    (au.raw_user_meta_data->>'role')::user_role
FROM auth.users au
WHERE au.email = '<EMAIL>'
ON CONFLICT (id) DO UPDATE SET
    full_name = EXCLUDED.full_name,
    phone = EXCLUDED.phone,
    role = EXCLUDED.role;

-- Create receptionist profile
INSERT INTO public.users (id, email, full_name, phone, role)
SELECT 
    au.id,
    au.email,
    au.raw_user_meta_data->>'full_name',
    au.raw_user_meta_data->>'phone',
    (au.raw_user_meta_data->>'role')::user_role
FROM auth.users au
WHERE au.email = '<EMAIL>'
ON CONFLICT (id) DO UPDATE SET
    full_name = EXCLUDED.full_name,
    phone = EXCLUDED.phone,
    role = EXCLUDED.role;

-- ============================================================================
-- STEP 4: Verify the users were created
-- ============================================================================

-- Show created admin users
SELECT 
    'Admin Users Created' as status,
    u.email,
    u.full_name,
    u.role,
    u.created_at
FROM public.users u
WHERE u.role IN ('admin', 'receptionist')
ORDER BY u.role, u.email;

-- ============================================================================
-- STEP 5: Instructions
-- ============================================================================

DO $$
BEGIN
    RAISE NOTICE '🎉 ADMIN USERS CREATED SUCCESSFULLY!';
    RAISE NOTICE '';
    RAISE NOTICE '👤 Admin Login Credentials:';
    RAISE NOTICE '   Email: <EMAIL>';
    RAISE NOTICE '   Password: AdminPassword123!';
    RAISE NOTICE '';
    RAISE NOTICE '👤 Receptionist Login Credentials:';
    RAISE NOTICE '   Email: <EMAIL>';
    RAISE NOTICE '   Password: ReceptionPassword123!';
    RAISE NOTICE '';
    RAISE NOTICE '🔐 IMPORTANT SECURITY NOTES:';
    RAISE NOTICE '1. Change these default passwords immediately after first login!';
    RAISE NOTICE '2. Use strong, unique passwords for production';
    RAISE NOTICE '3. Consider enabling 2FA for admin accounts';
    RAISE NOTICE '';
    RAISE NOTICE '📱 How to login as admin:';
    RAISE NOTICE '1. Open your React Native app';
    RAISE NOTICE '2. Use the regular login screen';
    RAISE NOTICE '3. Enter admin credentials above';
    RAISE NOTICE '4. You will automatically be redirected to admin dashboard';
    RAISE NOTICE '';
    RAISE NOTICE '✅ The app will detect the admin role and show the admin interface!';
END;
$$;
