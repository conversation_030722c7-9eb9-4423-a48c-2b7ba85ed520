-- ============================================================================
-- FINAL FIX: Handle both check_in/check_out AND check_in_date/check_out_date
-- This creates a trigger that automatically maps the fields
-- ============================================================================

-- Create a function to handle date field mapping
CREATE OR REPLACE FUNCTION handle_reservation_date_fields()
RETURNS TRIGGER AS $$
BEGIN
    -- If check_in is provided but check_in_date is null, copy it
    IF NEW.check_in IS NOT NULL AND NEW.check_in_date IS NULL THEN
        NEW.check_in_date = NEW.check_in;
    END IF;
    
    -- If check_out is provided but check_out_date is null, copy it
    IF NEW.check_out IS NOT NULL AND NEW.check_out_date IS NULL THEN
        NEW.check_out_date = NEW.check_out;
    END IF;
    
    -- If check_in_date is provided but check_in is null, copy it
    IF NEW.check_in_date IS NOT NULL AND NEW.check_in IS NULL THEN
        NEW.check_in = NEW.check_in_date;
    END IF;
    
    -- If check_out_date is provided but check_out is null, copy it
    IF NEW.check_out_date IS NOT NULL AND NEW.check_out IS NULL THEN
        NEW.check_out = NEW.check_out_date;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create the trigger
DROP TRIGGER IF EXISTS trigger_handle_date_fields ON public.reservations;
CREATE TRIGGER trigger_handle_date_fields
    BEFORE INSERT OR UPDATE ON public.reservations
    FOR EACH ROW
    EXECUTE FUNCTION handle_reservation_date_fields();

-- Done!
SELECT '✅ FIXED! Date field mapping is now automatic!' as status;
