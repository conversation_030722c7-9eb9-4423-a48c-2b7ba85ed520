import React from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  Alert,
  Text as RNText,
} from 'react-native';
import * as Sharing from 'expo-sharing';
import { <PERSON>ton, Card, Chip, Divider, IconButton, Surface, Text } from 'react-native-paper';

import { SafeAreaView } from 'react-native-safe-area-context';
import { useRoute, useNavigation } from '@react-navigation/native';
import { MaterialIcons } from '@expo/vector-icons';

import { useReservationStore } from '../../store/reservationStore';
import { useAuthStore } from '../../store/authStore';
import { Colors, colors, spacing, typography } from '../../constants';
import { ReceiptModal } from '../../components/guest/ReceiptModal';
import { ReceiptDownloadButton } from '../../components/guest/ReceiptDownloadButton';
import { formatPrice } from '../../utils/currency';
import type { Reservation, ReservationStatus } from '../../types';
import type { GuestStackScreenProps } from '../../types/navigation';

type ReservationDetailsScreenProps = GuestStackScreenProps<'ReservationDetails'>;

export const ReservationDetailsScreen = () => {
  const route = useRoute();
  const navigation = useNavigation();
  const { reservationId, reservation: routeReservation } = route.params;
  
  const { user } = useAuthStore();
  const { 
    selectedReservation, 
    cancelReservation, 
    loading 
  } = useReservationStore();
  
  const [actionLoading, setActionLoading] = React.useState(false);
  const [receiptModalVisible, setReceiptModalVisible] = React.useState(false);

  const reservation = selectedReservation || routeReservation;

  React.useEffect(() => {
    if (!reservation && reservationId) {
      // Fetch reservation details if not provided
      // This would be implemented in the store
    }
  }, [reservationId, reservation]);

  const handleCancelReservation = () => {
    Alert.alert(
      'Cancel Reservation',
      'Are you sure you want to cancel this reservation? This action cannot be undone.',
      [
        { text: 'No', style: 'cancel' },
        {
          text: 'Yes, Cancel',
          style: 'destructive',
          onPress: async () => {
            if (!reservation) return;
            
            setActionLoading(true);
            const result = await cancelReservation(reservation.id);
            setActionLoading(false);

            if (result.success) {
              Alert.alert('Success', 'Your reservation has been cancelled successfully.', [
                { text: 'OK', onPress: () => navigation.goBack() }
              ]);
            } else {
              Alert.alert('Error', result.error || 'Failed to cancel reservation');
            }
          }
        }
      ]
    );
  };

  const handleShareReservation = async () => {
    if (!reservation) return;

    try {
      const message = `Reservation Details
Hotel: Sunset View Hotel
Reservation ID: ${reservation.id}
Check-in: ${new Date(reservation.check_in_date).toLocaleDateString()}
Check-out: ${new Date(reservation.check_out_date).toLocaleDateString()}
Room: ${reservation.room?.room_number || 'N/A'}
Total: ${formatPrice(reservation.total_amount)}
Status: ${reservation.status}`;

      // Check if sharing is available on the device
      const isAvailable = await Sharing.isAvailableAsync();
      if (isAvailable) {
        // Create a temporary file for sharing
        // For simplicity, we're using Alert here since direct text sharing
        // requires a file in expo-sharing
        Alert.alert('Share', 'Sharing reservation details...');
        // In a real app, you would create a file with the message and share it
      } else {
        Alert.alert('Sharing not available', 'Sharing is not available on this device');
      }
    } catch (error) {
      console.error('Error sharing reservation:', error);
    }
  };

  const getStatusColor = (status: ReservationStatus) => {
    switch (status) {
      case 'confirmed':
        return colors.success;
      case 'pending':
        return colors.warning;
      case 'checked_in':
        return colors.info;
      case 'checked_out':
        return colors.onSurfaceVariant;
      case 'cancelled':
        return colors.error;
      default:
        return colors.onSurfaceVariant;
    }
  };

  const getStatusText = (status: ReservationStatus) => {
    switch (status) {
      case 'confirmed':
        return 'Confirmed';
      case 'pending':
        return 'Pending';
      case 'checked_in':
        return 'Checked In';
      case 'checked_out':
        return 'Checked Out';
      case 'cancelled':
        return 'Cancelled';
      default:
        return status;
    }
  };

  const getPaymentStatusText = (paymentStatus: string) => {
    switch (paymentStatus) {
      case 'paid':
        return 'Paid';
      case 'pending':
        return 'Pending';
      case 'failed':
        return 'Failed';
      case 'refunded':
        return 'Refunded';
      default:
        return paymentStatus?.charAt(0).toUpperCase() + paymentStatus?.slice(1) || 'Unknown';
    }
  };

  const getPaymentStatusColor = (paymentStatus: string) => {
    switch (paymentStatus) {
      case 'paid':
        return colors.success;
      case 'pending':
        return colors.warning;
      case 'failed':
        return colors.error;
      case 'refunded':
        return colors.info;
      default:
        return colors.onSurfaceVariant;
    }
  };

  const getStatusTextColor = (status: ReservationStatus | string) => {
    switch (status) {
      case 'confirmed':
        return '#FFFFFF'; // White text on primary
      case 'pending':
        return '#000000'; // Black text on yellow/orange
      case 'checked_in':
        return '#FFFFFF'; // White text on blue
      case 'checked_out':
        return '#FFFFFF'; // White text on gray
      case 'cancelled':
        return '#FFFFFF'; // White text on red
      case 'paid':
        return '#FFFFFF'; // White text on green
      case 'failed':
        return '#FFFFFF'; // White text on red
      case 'refunded':
        return '#FFFFFF'; // White text on blue
      default:
        return '#FFFFFF'; // White text default
    }
  };

  const shouldShowReceipt = () => {
    if (!reservation) return false;

    // Show receipt if payment exists (paid, pending, or failed) OR if reservation is confirmed
    return (
      reservation.payment_status === 'paid' ||
      reservation.payment_status === 'pending' ||
      reservation.payment_status === 'failed' ||
      (reservation.status === 'confirmed' && reservation.payment) ||
      (reservation.payment && (reservation.payment.status === 'paid' || reservation.payment.status === 'pending'))
    );
  };

  const canCancelReservation = () => {
    if (!reservation) return false;

    const canCancel = reservation.status === 'pending' || reservation.status === 'confirmed';
    const checkInDate = new Date(reservation.check_in_date);
    const now = new Date();
    const hoursDiff = (checkInDate.getTime() - now.getTime()) / (1000 * 60 * 60);

    return canCancel && hoursDiff > 24; // Can cancel up to 24 hours before check-in
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <RNText style={{ textAlign: 'center', fontSize: 16, color: colors.primary }}>Loading...</RNText>
          <Text style={styles.loadingText}>Loading reservation details...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (!reservation) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.errorContainer}>
          <MaterialIcons name="error-outline" size={64} color={colors.error} />
          <Text style={styles.errorText}>Reservation not found</Text>
          <Button mode="contained" onPress={() => navigation.goBack()}>
            Go Back
          </Button>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Header */}
        <Surface style={styles.header}>
          <View style={styles.headerContent}>
            <View>
              <Text style={styles.reservationId}>#{reservation.id.slice(-8).toUpperCase()}</Text>
              <Chip
                style={[styles.statusChip, { backgroundColor: getStatusColor(reservation.status) }]}
                textStyle={{ color: getStatusTextColor(reservation.status) }}
              >
                {getStatusText(reservation.status)}
              </Chip>
            </View>
            <IconButton
              icon="share"
              size={24}
              iconColor={colors.primary}
              onPress={handleShareReservation}
            />
          </View>
        </Surface>

        {/* Dates */}
        <Card style={styles.card}>
          <Card.Content>
            <Text style={styles.sectionTitle}>Stay Dates</Text>
            <View style={styles.dateContainer}>
              <View style={styles.dateItem}>
                <MaterialIcons name="login" size={24} color={colors.primary} />
                <View style={styles.dateInfo}>
                  <Text style={styles.dateLabel}>Check-in</Text>
                  <Text style={styles.dateValue}>
                    {new Date(reservation.check_in_date).toLocaleDateString('en-US', {
                      weekday: 'short',
                      month: 'short',
                      day: 'numeric',
                      year: 'numeric'
                    })}
                  </Text>
                </View>
              </View>
              
              <View style={styles.dateItem}>
                <MaterialIcons name="logout" size={24} color={colors.primary} />
                <View style={styles.dateInfo}>
                  <Text style={styles.dateLabel}>Check-out</Text>
                  <Text style={styles.dateValue}>
                    {new Date(reservation.check_out_date).toLocaleDateString('en-US', {
                      weekday: 'short',
                      month: 'short',
                      day: 'numeric',
                      year: 'numeric'
                    })}
                  </Text>
                </View>
              </View>
            </View>
            
            <Divider style={styles.divider} />
            
            <View style={styles.nightsContainer}>
              <Text style={styles.nightsText}>
                {Math.ceil(
                  (new Date(reservation.check_out_date).getTime() - 
                   new Date(reservation.check_in_date).getTime()) / 
                  (1000 * 60 * 60 * 24)
                )} nights
              </Text>
            </View>
          </Card.Content>
        </Card>

        {/* Room Details */}
        <Card style={styles.card}>
          <Card.Content>
            <Text style={styles.sectionTitle}>Room Details</Text>
            <View style={styles.roomInfo}>
              <MaterialIcons name="hotel" size={24} color={colors.primary} />
              <View style={styles.roomDetails}>
                <Text style={styles.roomNumber}>
                  Room {reservation.room?.room_number || 'TBA'}
                </Text>
                <Text style={styles.roomType}>
                  {reservation.room?.room_type?.charAt(0).toUpperCase() + 
                   reservation.room?.room_type?.slice(1) || 'Standard'} Room
                </Text>
                <Text style={styles.roomCapacity}>
                  Up to {reservation.room?.max_occupancy || 2} guests
                </Text>
              </View>
            </View>
          </Card.Content>
        </Card>

        {/* Special Requests */}
        {reservation.special_requests && (
          <Card style={styles.card}>
            <Card.Content>
              <Text style={styles.sectionTitle}>Special Requests</Text>
              <Text style={styles.specialRequests}>{reservation.special_requests}</Text>
            </Card.Content>
          </Card>
        )}

        {/* Payment Information */}
        <Card style={styles.card}>
          <Card.Content>
            <Text style={styles.sectionTitle}>Payment Information</Text>
            <View style={styles.paymentInfo}>
              <View style={styles.paymentRow}>
                <Text style={styles.paymentLabel}>Total Amount</Text>
                <Text style={styles.paymentValue}>{formatPrice(reservation.total_amount)}</Text>
              </View>
              <View style={styles.paymentRow}>
                <Text style={styles.paymentLabel}>Payment Status</Text>
                <View style={[
                  styles.paymentStatusBadge,
                  { backgroundColor: getPaymentStatusColor(reservation.payment_status) }
                ]}>
                  <Text style={[
                    styles.paymentStatusText,
                    { color: getStatusTextColor(reservation.payment_status) }
                  ]}>
                    {getPaymentStatusText(reservation.payment_status)}
                  </Text>
                </View>
              </View>
              <View style={styles.paymentRow}>
                <Text style={styles.paymentLabel}>Payment Method</Text>
                <Text style={styles.paymentValue}>
                  {reservation.payment?.payment_method?.replace('_', ' ').toUpperCase() || 'Not specified'}
                </Text>
              </View>

              {/* Receipt Actions */}
              {shouldShowReceipt() && (
                <View style={styles.receiptActions}>
                  <Button
                    mode="outlined"
                    icon="receipt"
                    onPress={() => setReceiptModalVisible(true)}
                    style={[styles.receiptButton, styles.buttonContent]}
                  >
                    View Receipt
                  </Button>
                  <ReceiptDownloadButton
                    reservationId={reservation.id}
                    variant="button"
                    mode="contained"
                    style={styles.receiptButton}
                  />
                </View>
              )}
            </View>
          </Card.Content>
        </Card>

        {/* Contact Information */}
        <Card style={styles.card}>
          <Card.Content>
            <Text style={styles.sectionTitle}>Need Help?</Text>
            <View style={styles.contactContainer}>
              <Button
                mode="outlined"
                icon="phone"
                onPress={() => {/* Handle call */}}
                style={styles.contactButton}
              >
                Call Hotel
              </Button>
              <Button
                mode="outlined"
                icon="email"
                onPress={() => {/* Handle email */}}
                style={styles.contactButton}
              >
                Send Email
              </Button>
            </View>
          </Card.Content>
        </Card>
      </ScrollView>

      {/* Action Buttons */}
      {canCancelReservation() && (
        <Surface style={styles.actionContainer}>
          <Button
            mode="contained"
            icon="cancel"
            onPress={handleCancelReservation}
            loading={actionLoading}
            style={[styles.cancelButton, { backgroundColor: colors.error }]}
          >
            Cancel Reservation
          </Button>
        </Surface>
      )}

      {/* Receipt Modal */}
      <ReceiptModal
        visible={receiptModalVisible}
        onDismiss={() => setReceiptModalVisible(false)}
        reservationId={reservation.id}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  scrollView: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: spacing.xl,
  },
  loadingText: {
    fontSize: typography.sizes.md,
    color: colors.onSurfaceVariant,
    marginTop: spacing.md,
    textAlign: 'center',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: spacing.xl,
  },
  errorText: {
    fontSize: typography.sizes.lg,
    color: colors.error,
    marginVertical: spacing.lg,
    textAlign: 'center',
  },
  header: {
    margin: spacing.lg,
    marginBottom: spacing.md,
    elevation: 2,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: spacing.lg,
  },
  reservationId: {
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.bold as any,
    color: colors.onSurface,
    marginBottom: spacing.sm,
  },
  statusChip: {
    alignSelf: 'flex-start',
  },
  card: {
    margin: spacing.lg,
    marginBottom: spacing.md,
  },
  sectionTitle: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.semibold as any,
    color: colors.onSurface,
    marginBottom: spacing.md,
  },
  dateContainer: {
    gap: spacing.lg,
  },
  dateItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.md,
  },
  dateInfo: {
    flex: 1,
  },
  dateLabel: {
    fontSize: typography.sizes.sm,
    color: colors.onSurfaceVariant,
    marginBottom: spacing.xs,
  },
  dateValue: {
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.medium as any,
    color: colors.onSurface,
  },
  divider: {
    marginVertical: spacing.lg,
  },
  nightsContainer: {
    alignItems: 'center',
  },
  nightsText: {
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.medium as any,
    color: colors.primary,
  },
  roomInfo: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: spacing.md,
  },
  roomDetails: {
    flex: 1,
  },
  roomNumber: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.semibold as any,
    color: colors.onSurface,
    marginBottom: spacing.xs,
  },
  roomType: {
    fontSize: typography.sizes.md,
    color: colors.onSurfaceVariant,
    marginBottom: spacing.xs,
  },
  roomCapacity: {
    fontSize: typography.sizes.sm,
    color: colors.onSurfaceVariant,
  },
  specialRequests: {
    fontSize: typography.sizes.md,
    color: colors.onSurface,
    lineHeight: 24,
  },
  paymentInfo: {
    gap: spacing.md,
  },
  paymentRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  paymentLabel: {
    fontSize: typography.sizes.md,
    color: colors.onSurfaceVariant,
  },
  paymentValue: {
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.semibold as any,
    color: colors.onSurface,
  },
  paymentStatusBadge: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    alignSelf: 'flex-start',
    minWidth: 80,
    alignItems: 'center',
    justifyContent: 'center',
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 1,
  },
  paymentStatusText: {
    fontSize: 14,
    fontWeight: '700',
    textAlign: 'center',
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  receiptActions: {
    flexDirection: 'row',
    gap: spacing.md,
    marginTop: spacing.md,
    paddingHorizontal: spacing.xs,
    alignItems: 'stretch',
  },
  receiptButton: {
    flex: 1,
    minHeight: 48,
    minWidth: 120, // Ensure minimum width
  },
  buttonContent: {
    height: 48,
    justifyContent: 'center',
  },
  contactContainer: {
    flexDirection: 'row',
    gap: spacing.md,
  },
  contactButton: {
    flex: 1,
  },
  actionContainer: {
    padding: spacing.lg,
    elevation: 8,
  },
  cancelButton: {
    width: '100%',
  },
});
