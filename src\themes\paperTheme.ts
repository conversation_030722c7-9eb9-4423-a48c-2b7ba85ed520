import { lightColors, darkColors } from '../constants';

// Define our own theme interface based on react-native-paper's structure
interface CustomTheme {
  dark: boolean;
  version: number;
  isV3: boolean;
  colors: {
    primary: string;
    primaryContainer: string;
    secondary: string;
    secondaryContainer: string;
    tertiary: string;
    tertiaryContainer: string;
    surface: string;
    surfaceVariant: string;
    background: string;
    error: string;
    errorContainer: string;
    onPrimary: string;
    onPrimaryContainer: string;
    onSecondary: string;
    onSecondaryContainer: string;
    onTertiary: string;
    onTertiaryContainer: string;
    onSurface: string;
    onSurfaceVariant: string;
    onBackground: string;
    onError: string;
    onErrorContainer: string;
    outline: string;
    outlineVariant: string;
    inverseSurface: string;
    inverseOnSurface: string;
    inversePrimary: string;
    shadow: string;
    scrim: string;
    backdrop: string;
    surfaceDisabled: string;
    onSurfaceDisabled: string;
    elevation: {
      level0: string;
      level1: string;
      level2: string;
      level3: string;
      level4: string;
      level5: string;
    };
  };
}

// Define custom themes using the Theme type structure
// Using direct color definitions to avoid import issues

// Light theme for React Native Paper
export const lightPaperTheme: CustomTheme = {
  dark: false,
  version: 3,
  isV3: true,
  colors: {
    primary: lightColors.primary,
    primaryContainer: lightColors.primaryLight,
    secondary: lightColors.accent,
    secondaryContainer: lightColors.accentLight,
    tertiary: lightColors.info,
    tertiaryContainer: lightColors.surfaceVariant,
    surface: lightColors.surface,
    surfaceVariant: lightColors.surfaceVariant,
    background: lightColors.background,
    error: lightColors.error,
    errorContainer: '#FFEBEE',
    onPrimary: lightColors.onPrimary,
    onPrimaryContainer: lightColors.primaryDark,
    onSecondary: lightColors.onPrimary,
    onSecondaryContainer: lightColors.accentDark,
    onTertiary: lightColors.onPrimary,
    onTertiaryContainer: lightColors.textPrimary,
    onSurface: lightColors.onSurface,
    onSurfaceVariant: lightColors.onSurfaceVariant,
    onBackground: lightColors.text,
    onError: lightColors.onPrimary,
    onErrorContainer: '#B71C1C',
    outline: lightColors.outline,
    outlineVariant: lightColors.divider,
    inverseSurface: lightColors.textPrimary,
    inverseOnSurface: lightColors.surface,
    inversePrimary: lightColors.primaryLight,
    shadow: '#000000',
    scrim: '#000000',
    backdrop: 'rgba(0, 0, 0, 0.4)',
    surfaceDisabled: lightColors.disabled,
    onSurfaceDisabled: lightColors.disabled,
    elevation: {
      level0: 'transparent',
      level1: lightColors.surface,
      level2: '#F8F9FA',
      level3: '#F1F3F4',
      level4: '#EBEDF0',
      level5: '#E4E7EB',
    },
  },
};

// Dark theme for React Native Paper
export const darkPaperTheme: CustomTheme = {
  dark: true,
  version: 3,
  isV3: true,
  colors: {
    primary: darkColors.primary,
    primaryContainer: darkColors.primaryDark,
    secondary: darkColors.accent,
    secondaryContainer: darkColors.accentDark,
    tertiary: darkColors.info,
    tertiaryContainer: darkColors.surfaceVariant,
    surface: darkColors.surface,
    surfaceVariant: darkColors.surfaceVariant,
    background: darkColors.background,
    error: darkColors.error,
    errorContainer: '#5F1A1A',
    onPrimary: darkColors.onPrimary,
    onPrimaryContainer: darkColors.primaryLight,
    onSecondary: darkColors.onPrimary,
    onSecondaryContainer: darkColors.accentLight,
    onTertiary: darkColors.onPrimary,
    onTertiaryContainer: darkColors.textPrimary,
    onSurface: darkColors.text,
    onSurfaceVariant: darkColors.textSecondary,
    onBackground: darkColors.text,
    onError: darkColors.onPrimary,
    onErrorContainer: '#FFCDD2',
    outline: darkColors.border,
    outlineVariant: darkColors.divider,
    inverseSurface: darkColors.textPrimary,
    inverseOnSurface: darkColors.surface,
    inversePrimary: darkColors.primaryDark,
    shadow: '#000000',
    scrim: '#000000',
    backdrop: 'rgba(0, 0, 0, 0.6)',
    surfaceDisabled: darkColors.disabled,
    onSurfaceDisabled: darkColors.disabled,
    elevation: {
      level0: 'transparent',
      level1: darkColors.surface,
      level2: '#0F0F0F',
      level3: '#1A1A1A',
      level4: '#1F1F1F',
      level5: '#242424',
    },
  },
};

// Helper function to get the appropriate theme
export const getPaperTheme = (isDark: boolean): CustomTheme => {
  return isDark ? darkPaperTheme : lightPaperTheme;
};
