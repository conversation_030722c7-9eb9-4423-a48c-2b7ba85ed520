import { supabase } from './supabase';
import type { UserRole } from '../types';

export interface PermissionCheck {
  canViewAllUsers: boolean;
  canManageUsers: boolean;
  canManageRooms: boolean;
  canViewAllReservations: boolean;
  canManageReservations: boolean;
  canViewAllPayments: boolean;
  canManagePayments: boolean;
  canUploadImages: boolean;
}

export class PermissionsService {
  private static instance: PermissionsService;
  private userRole: UserRole | null = null;
  private userId: string | null = null;

  private constructor() {}

  static getInstance(): PermissionsService {
    if (!PermissionsService.instance) {
      PermissionsService.instance = new PermissionsService();
    }
    return PermissionsService.instance;
  }

  // Initialize with current user data
  initialize(userId: string, userRole: UserRole) {
    this.userId = userId;
    this.userRole = userRole;
  }

  // Clear permissions on logout
  clear() {
    this.userId = null;
    this.userRole = null;
  }

  // Get current user's permissions
  getPermissions(): PermissionCheck {
    if (!this.userRole) {
      return this.getGuestPermissions();
    }

    switch (this.userRole) {
      case 'admin':
        return this.getAdminPermissions();
      case 'receptionist':
        return this.getReceptionistPermissions();
      case 'guest':
      default:
        return this.getGuestPermissions();
    }
  }

  private getAdminPermissions(): PermissionCheck {
    return {
      canViewAllUsers: true,
      canManageUsers: true,
      canManageRooms: true,
      canViewAllReservations: true,
      canManageReservations: true,
      canViewAllPayments: true,
      canManagePayments: true,
      canUploadImages: true,
    };
  }

  private getReceptionistPermissions(): PermissionCheck {
    return {
      canViewAllUsers: true,
      canManageUsers: false,
      canManageRooms: false,
      canViewAllReservations: true,
      canManageReservations: true,
      canViewAllPayments: true,
      canManagePayments: false,
      canUploadImages: false,
    };
  }

  private getGuestPermissions(): PermissionCheck {
    return {
      canViewAllUsers: false,
      canManageUsers: false,
      canManageRooms: false,
      canViewAllReservations: false,
      canManageReservations: false,
      canViewAllPayments: false,
      canManagePayments: false,
      canUploadImages: false,
    };
  }

  // Specific permission checks
  canViewAllUsers(): boolean {
    return this.getPermissions().canViewAllUsers;
  }

  canManageUsers(): boolean {
    return this.getPermissions().canManageUsers;
  }

  canManageRooms(): boolean {
    return this.getPermissions().canManageRooms;
  }

  canViewAllReservations(): boolean {
    return this.getPermissions().canViewAllReservations;
  }

  canManageReservations(): boolean {
    return this.getPermissions().canManageReservations;
  }

  canViewAllPayments(): boolean {
    return this.getPermissions().canViewAllPayments;
  }

  canManagePayments(): boolean {
    return this.getPermissions().canManagePayments;
  }

  canUploadImages(): boolean {
    return this.getPermissions().canUploadImages;
  }

  // Check if user can access a specific reservation
  canAccessReservation(reservationGuestId: string): boolean {
    // Users can always access their own reservations
    if (this.userId === reservationGuestId) {
      return true;
    }
    // Staff can access all reservations
    return this.canViewAllReservations();
  }

  // Check if user can access a specific payment
  canAccessPayment(paymentGuestId: string): boolean {
    // Users can always access their own payments
    if (this.userId === paymentGuestId) {
      return true;
    }
    // Staff can access all payments
    return this.canViewAllPayments();
  }

  // Get user role safely from database (bypasses RLS issues)
  async getUserRoleFromDatabase(userId: string): Promise<UserRole | null> {
    try {
      // Use service role client to bypass RLS
      const { data, error } = await supabase
        .from('users')
        .select('role')
        .eq('id', userId)
        .single();

      if (error) {
        console.error('Error fetching user role:', error);
        return null;
      }

      return data?.role || 'guest';
    } catch (error) {
      console.error('Error in getUserRoleFromDatabase:', error);
      return null;
    }
  }

  // Refresh user role from database
  async refreshUserRole(): Promise<void> {
    if (!this.userId) return;

    const role = await this.getUserRoleFromDatabase(this.userId);
    if (role) {
      this.userRole = role;
    }
  }
}

// Export singleton instance
export const permissionsService = PermissionsService.getInstance();
