/**
 * Test suite for Room Image Migration functionality
 * Verifies that the migration from string arrays to RoomImage objects works correctly
 */

import RoomImageMigrationService from '../services/roomImageMigrationService';
import { RoomImage } from '../types';

describe('RoomImageMigrationService', () => {
  describe('convertLegacyImages', () => {
    it('should convert string array to RoomImage array', () => {
      const legacyImages = [
        'https://example.com/image1.jpg',
        'https://example.com/image2.jpg'
      ];

      const result = RoomImageMigrationService.convertLegacyImages(legacyImages);

      expect(result).toHaveLength(2);
      expect(result[0]).toHaveProperty('id');
      expect(result[0]).toHaveProperty('url', 'https://example.com/image1.jpg');
      expect(result[0]).toHaveProperty('alt_text', 'Room image 1');
      expect(result[0]).toHaveProperty('upload_date');
      expect(result[1]).toHaveProperty('url', 'https://example.com/image2.jpg');
      expect(result[1]).toHaveProperty('alt_text', 'Room image 2');
    });

    it('should handle empty array', () => {
      const result = RoomImageMigrationService.convertLegacyImages([]);
      expect(result).toEqual([]);
    });

    it('should handle null/undefined input', () => {
      expect(RoomImageMigrationService.convertLegacyImages(null as any)).toEqual([]);
      expect(RoomImageMigrationService.convertLegacyImages(undefined as any)).toEqual([]);
    });
  });

  describe('extractImageUrls', () => {
    it('should extract URLs from RoomImage array', () => {
      const roomImages: RoomImage[] = [
        {
          id: 'img1',
          url: 'https://example.com/image1.jpg',
          alt_text: 'Image 1'
        },
        {
          id: 'img2',
          url: 'https://example.com/image2.jpg',
          alt_text: 'Image 2'
        }
      ];

      const result = RoomImageMigrationService.extractImageUrls(roomImages);

      expect(result).toEqual([
        'https://example.com/image1.jpg',
        'https://example.com/image2.jpg'
      ]);
    });

    it('should handle empty array', () => {
      const result = RoomImageMigrationService.extractImageUrls([]);
      expect(result).toEqual([]);
    });
  });

  describe('normalizeRoomImages', () => {
    it('should handle mixed legacy and new format', () => {
      const mixedImages = [
        'https://example.com/legacy.jpg', // Legacy string
        {
          id: 'img1',
          url: 'https://example.com/new.jpg',
          alt_text: 'New format'
        }
      ];

      const result = RoomImageMigrationService.normalizeRoomImages(mixedImages);

      expect(result).toHaveLength(2);
      expect(result[0]).toHaveProperty('id');
      expect(result[0]).toHaveProperty('url', 'https://example.com/legacy.jpg');
      expect(result[1]).toHaveProperty('id', 'img1');
      expect(result[1]).toHaveProperty('url', 'https://example.com/new.jpg');
    });

    it('should handle pure legacy format', () => {
      const legacyImages = [
        'https://example.com/image1.jpg',
        'https://example.com/image2.jpg'
      ];

      const result = RoomImageMigrationService.normalizeRoomImages(legacyImages);

      expect(result).toHaveLength(2);
      result.forEach((image, index) => {
        expect(image).toHaveProperty('id');
        expect(image).toHaveProperty('url', legacyImages[index]);
        expect(image).toHaveProperty('alt_text', `Room image ${index + 1}`);
      });
    });

    it('should handle pure new format', () => {
      const newImages: RoomImage[] = [
        {
          id: 'img1',
          url: 'https://example.com/image1.jpg',
          alt_text: 'Image 1'
        }
      ];

      const result = RoomImageMigrationService.normalizeRoomImages(newImages);

      expect(result).toEqual(newImages);
    });
  });

  describe('isLegacyFormat', () => {
    it('should identify legacy format', () => {
      const legacyImages = ['url1', 'url2'];
      expect(RoomImageMigrationService.isLegacyFormat(legacyImages)).toBe(true);
    });

    it('should identify new format', () => {
      const newImages = [
        { id: 'img1', url: 'url1', alt_text: 'Alt 1' }
      ];
      expect(RoomImageMigrationService.isLegacyFormat(newImages)).toBe(false);
    });

    it('should handle empty array', () => {
      expect(RoomImageMigrationService.isLegacyFormat([])).toBe(false);
    });
  });

  describe('isNewFormat', () => {
    it('should identify new format', () => {
      const newImages = [
        { id: 'img1', url: 'url1', alt_text: 'Alt 1' }
      ];
      expect(RoomImageMigrationService.isNewFormat(newImages)).toBe(true);
    });

    it('should identify legacy format', () => {
      const legacyImages = ['url1', 'url2'];
      expect(RoomImageMigrationService.isNewFormat(legacyImages)).toBe(false);
    });

    it('should handle empty array as new format', () => {
      expect(RoomImageMigrationService.isNewFormat([])).toBe(true);
    });
  });

  describe('validateRoomImage', () => {
    it('should validate correct RoomImage', () => {
      const validImage = {
        id: 'img1',
        url: 'https://example.com/image.jpg',
        alt_text: 'Valid image'
      };
      expect(RoomImageMigrationService.validateRoomImage(validImage)).toBe(true);
    });

    it('should reject invalid RoomImage', () => {
      const invalidImages = [
        { url: 'missing-id' },
        { id: 'missing-url' },
        { id: '', url: 'empty-id' },
        { id: 'valid', url: '' },
        null,
        undefined,
        'string'
      ];

      invalidImages.forEach(image => {
        expect(RoomImageMigrationService.validateRoomImage(image)).toBe(false);
      });
    });
  });

  describe('generateImageId', () => {
    it('should generate unique IDs', () => {
      const id1 = RoomImageMigrationService.generateImageId();
      const id2 = RoomImageMigrationService.generateImageId();
      
      expect(id1).not.toBe(id2);
      expect(id1).toMatch(/^img_\d+_[a-z0-9]+$/);
      expect(id2).toMatch(/^img_\d+_[a-z0-9]+$/);
    });

    it('should use custom prefix', () => {
      const id = RoomImageMigrationService.generateImageId('custom');
      expect(id).toMatch(/^custom_\d+_[a-z0-9]+$/);
    });
  });

  describe('createRoomImageFromUrl', () => {
    it('should create RoomImage from URL', () => {
      const url = 'https://example.com/image.jpg';
      const altText = 'Test image';
      const fileName = 'image.jpg';
      const fileSize = 1024;

      const result = RoomImageMigrationService.createRoomImageFromUrl(
        url, altText, fileName, fileSize
      );

      expect(result).toHaveProperty('id');
      expect(result).toHaveProperty('url', url);
      expect(result).toHaveProperty('alt_text', altText);
      expect(result).toHaveProperty('upload_date');
      expect(result).toHaveProperty('file_name', fileName);
      expect(result).toHaveProperty('file_size', fileSize);
    });

    it('should use default alt text', () => {
      const result = RoomImageMigrationService.createRoomImageFromUrl(
        'https://example.com/image.jpg'
      );

      expect(result).toHaveProperty('alt_text', 'Room image');
    });
  });

  describe('removeDuplicateImages', () => {
    it('should remove duplicate URLs', () => {
      const imagesWithDuplicates: RoomImage[] = [
        { id: 'img1', url: 'https://example.com/image1.jpg', alt_text: 'Image 1' },
        { id: 'img2', url: 'https://example.com/image2.jpg', alt_text: 'Image 2' },
        { id: 'img3', url: 'https://example.com/image1.jpg', alt_text: 'Duplicate' }
      ];

      const result = RoomImageMigrationService.removeDuplicateImages(imagesWithDuplicates);

      expect(result).toHaveLength(2);
      expect(result[0]).toHaveProperty('id', 'img1');
      expect(result[1]).toHaveProperty('id', 'img2');
    });
  });

  describe('getImageStats', () => {
    it('should calculate image statistics', () => {
      const images: RoomImage[] = [
        {
          id: 'img1',
          url: 'url1',
          alt_text: 'Alt 1',
          file_size: 1000
        },
        {
          id: 'img2',
          url: 'url2',
          alt_text: '',
          file_size: 2000
        },
        {
          id: 'img3',
          url: 'url3'
          // No alt_text, no file_size
        }
      ];

      const stats = RoomImageMigrationService.getImageStats(images);

      expect(stats.total).toBe(3);
      expect(stats.withAltText).toBe(1); // Only first image has non-empty alt_text
      expect(stats.withFileSize).toBe(2);
      expect(stats.totalSize).toBe(3000);
      expect(stats.averageSize).toBe(1500);
    });
  });
});
