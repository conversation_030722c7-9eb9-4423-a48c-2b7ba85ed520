-- ============================================================================
-- UPGRADE EXISTING USER TO ADMIN
-- This script upgrades an existing guest user to admin role
-- Use this if you already have a user account and want to make it admin
-- ============================================================================

-- ============================================================================
-- STEP 1: Find your user (replace email with your actual email)
-- ============================================================================

-- First, let's see what users exist
SELECT 
    'Current Users' as info,
    email,
    full_name,
    role,
    created_at
FROM public.users
ORDER BY created_at DESC;

-- ============================================================================
-- STEP 2: Upgrade user to admin role
-- ============================================================================

-- Replace '<EMAIL>' with the email of the user you want to make admin
DO $$
DECLARE
    target_email TEXT := '<EMAIL>'; -- CHANGE THIS EMAIL!
    user_id UUID;
    current_metadata JSONB;
BEGIN
    -- Get the user ID
    SELECT id INTO user_id
    FROM public.users
    WHERE email = target_email;

    IF user_id IS NULL THEN
        RAISE NOTICE '❌ User with email % not found!', target_email;
        RAISE NOTICE '💡 Available users:';
        FOR user_id IN
            SELECT id FROM public.users ORDER BY created_at DESC LIMIT 5
        LOOP
            RAISE NOTICE '   - %', (SELECT email FROM public.users WHERE id = user_id);
        END LOOP;
        RETURN;
    END IF;

    -- Get current metadata
    SELECT raw_user_meta_data INTO current_metadata
    FROM auth.users
    WHERE email = target_email;

    -- Update the user role in public.users table
    UPDATE public.users
    SET role = 'admin', updated_at = NOW()
    WHERE email = target_email;

    -- Update the user metadata in auth.users table (THIS IS CRUCIAL!)
    UPDATE auth.users
    SET
        raw_user_meta_data = jsonb_set(
            COALESCE(current_metadata, '{}'::jsonb),
            '{role}',
            '"admin"'
        ),
        updated_at = NOW()
    WHERE email = target_email;

    RAISE NOTICE '✅ User % has been upgraded to admin!', target_email;
    RAISE NOTICE '🔄 IMPORTANT: The user MUST log out and log back in for changes to take effect!';
    RAISE NOTICE '📱 Or force refresh the JWT token in the app.';
END;
$$;

-- ============================================================================
-- STEP 3: Verify the upgrade
-- ============================================================================

-- Show the updated user
SELECT 
    'Updated User' as status,
    email,
    full_name,
    role,
    updated_at
FROM public.users
WHERE role = 'admin'
ORDER BY updated_at DESC;

-- ============================================================================
-- STEP 4: Instructions
-- ============================================================================

DO $$
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '📋 NEXT STEPS:';
    RAISE NOTICE '1. Make sure you changed the target_email variable above';
    RAISE NOTICE '2. Run this script again if you used the default email';
    RAISE NOTICE '3. The user must log out and log back in';
    RAISE NOTICE '4. They will then see the admin dashboard';
    RAISE NOTICE '';
    RAISE NOTICE '🔧 Alternative: Create new admin user';
    RAISE NOTICE 'If you prefer to create a fresh admin account, run:';
    RAISE NOTICE 'supabase/create-admin-users.sql';
END;
$$;
