import * as Print from 'expo-print';
import * as Sharing from 'expo-sharing';
import * as FileSystem from 'expo-file-system';
import { supabase } from './supabase';
import { formatPrice } from '../utils/currency';
import { APP_CONSTANTS } from '../constants';
import type { Reservation, Payment } from '../types';

export interface ReceiptData {
  reservation: Reservation & {
    room: {
      room_number: string;
      room_type: string;
      price_per_night: number;
    };
    guest: {
      full_name: string;
      email: string;
      phone?: string;
    };
  };
  payment: Payment;
  hotelInfo: {
    name: string;
    address: string;
    phone: string;
    email: string;
  };
  receiptNumber: string;
  nights: number;
  subtotal: number;
  taxes: number;
  total: number;
}

class ReceiptService {
  private hotelInfo = {
    name: APP_CONSTANTS.APP_NAME,
    address: 'Diani Beach Road, Mombasa, Kenya',
    phone: APP_CONSTANTS.SUPPORT_PHONE,
    email: APP_CONSTANTS.SUPPORT_EMAIL,
    website: 'www.sunsetviewhotel.com',
    tagline: 'Luxury • Comfort • Excellence',
  };

  // Fetch payment and reservation data for receipt
  async getReceiptData(reservationId: string, paymentId?: string): Promise<ReceiptData | null> {
    try {
      console.log('📄 Fetching receipt data for reservation:', reservationId);

      // Fetch reservation with room and guest details
      const { data: reservation, error: reservationError } = await supabase
        .from('reservations')
        .select(`
          *,
          room:rooms(room_number, room_type, price_per_night),
          guest:users!guest_id(full_name, email, phone)
        `)
        .eq('id', reservationId)
        .single();

      if (reservationError || !reservation) {
        console.error('❌ Error fetching reservation:', reservationError);
        return null;
      }

      // Fetch payment data
      let paymentQuery = supabase
        .from('payments')
        .select('*')
        .eq('reservation_id', reservationId);

      if (paymentId) {
        paymentQuery = paymentQuery.eq('id', paymentId);
      } else {
        paymentQuery = paymentQuery.eq('status', 'paid');
      }

      const { data: payments, error: paymentError } = await paymentQuery;

      if (paymentError || !payments || payments.length === 0) {
        console.error('❌ Error fetching payment:', paymentError);
        return null;
      }

      const payment = payments[0]; // Get the first paid payment

      // Calculate receipt details
      const checkIn = new Date(reservation.check_in_date);
      const checkOut = new Date(reservation.check_out_date);
      const nights = Math.ceil((checkOut.getTime() - checkIn.getTime()) / (1000 * 60 * 60 * 24));
      
      const subtotal = reservation.total_amount;
      const taxes = subtotal * 0.16; // 16% VAT in Kenya
      const total = subtotal;

      const receiptNumber = `RCP-${payment.paystack_reference || payment.id.slice(-8).toUpperCase()}`;

      return {
        reservation: reservation as any,
        payment,
        hotelInfo: this.hotelInfo,
        receiptNumber,
        nights,
        subtotal,
        taxes,
        total,
      };
    } catch (error) {
      console.error('❌ Error getting receipt data:', error);
      return null;
    }
  }

  // Generate HTML receipt template with enhanced design
  private generateReceiptHTML(data: ReceiptData): string {
    const { reservation, payment, hotelInfo, receiptNumber, nights, subtotal, taxes, total } = data;

    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Receipt - ${receiptNumber}</title>
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <style>
          * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
          }
          body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            color: #1A1A1A;
            line-height: 1.6;
            background: #f8f9fa;
          }
          .receipt-container {
            max-width: 650px;
            margin: 0 auto;
            background: white;
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
          }
          .header {
            background: linear-gradient(135deg, #2E8B57, #228B22);
            color: white;
            padding: 40px 30px;
            position: relative;
          }
          .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="1.5" fill="rgba(255,255,255,0.1)"/><circle cx="40" cy="80" r="1" fill="rgba(255,255,255,0.1)"/></svg>');
            opacity: 0.3;
          }
          .header-content {
            position: relative;
            z-index: 1;
          }
          .logo-section {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 20px;
          }
          .hotel-name {
            font-size: 32px;
            font-weight: bold;
            margin-bottom: 8px;
            text-align: center;
            letter-spacing: -0.5px;
          }
          .hotel-tagline {
            font-size: 14px;
            opacity: 0.9;
            text-align: center;
            font-style: italic;
            margin-bottom: 20px;
          }
          .hotel-contact {
            display: flex;
            justify-content: center;
            gap: 20px;
            flex-wrap: wrap;
            font-size: 12px;
            opacity: 0.9;
          }
          .contact-item {
            display: flex;
            align-items: center;
            gap: 5px;
          }
          .receipt-info {
            background: linear-gradient(135deg, #f8f9fa, #ffffff);
            padding: 30px;
            border-bottom: 3px solid #2E8B57;
            position: relative;
          }
          .receipt-badge {
            background: white;
            border: 2px solid #2E8B57;
            border-radius: 12px;
            padding: 15px;
            text-align: center;
            margin-bottom: 20px;
            box-shadow: 0 4px 12px rgba(46, 139, 87, 0.1);
          }
          .receipt-number {
            font-size: 24px;
            font-weight: bold;
            color: #2E8B57;
            margin-bottom: 5px;
            letter-spacing: 1px;
          }
          .receipt-label {
            font-size: 12px;
            color: #2E8B57;
            font-weight: bold;
            letter-spacing: 2px;
          }
          .content {
            padding: 40px 30px;
          }
          .section {
            margin-bottom: 35px;
            background: #fafafa;
            border-radius: 12px;
            padding: 25px;
            border-left: 4px solid #2E8B57;
          }
          .section-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
          }
          .section-icon {
            width: 24px;
            height: 24px;
            background: #2E8B57;
            border-radius: 50%;
            margin-right: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
          }
          .section-title {
            font-size: 18px;
            font-weight: bold;
            color: #2E8B57;
            margin: 0;
          }
          .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 15px;
          }
          .info-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #e9ecef;
          }
          .info-row:last-child {
            border-bottom: none;
          }
          .info-label {
            font-weight: 600;
            color: #6B7280;
            font-size: 14px;
          }
          .info-value {
            font-weight: bold;
            color: #1A1A1A;
            font-size: 14px;
            text-align: right;
          }
          .guest-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            border: 1px solid #e9ecef;
            margin-bottom: 15px;
          }
          .guest-name {
            font-size: 18px;
            font-weight: bold;
            color: #1A1A1A;
            margin-bottom: 10px;
          }
          .guest-contact {
            font-size: 14px;
            color: #6B7280;
            margin-bottom: 5px;
          }
          .room-card {
            background: linear-gradient(135deg, #2E8B57, #228B22);
            color: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            text-align: center;
          }
          .room-number {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 5px;
          }
          .room-type {
            font-size: 14px;
            opacity: 0.9;
            text-transform: capitalize;
          }
          .dates-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 20px;
          }
          .date-card {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
          }
          .date-label {
            font-size: 12px;
            color: #6B7280;
            margin-bottom: 5px;
            text-transform: uppercase;
            font-weight: bold;
          }
          .date-value {
            font-size: 14px;
            font-weight: bold;
            color: #1A1A1A;
          }
          .nights-badge {
            background: #FFD700;
            color: #1A1A1A;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: bold;
            text-align: center;
            margin: 20px auto;
            display: inline-block;
          }
          .payment-summary {
            background: linear-gradient(135deg, #f8f9fa, #ffffff);
            border: 2px solid #2E8B57;
            border-radius: 12px;
            padding: 25px;
            margin-top: 20px;
          }
          .payment-breakdown {
            margin-bottom: 20px;
          }
          .total-container {
            background: #2E8B57;
            color: white;
            border-radius: 8px;
            padding: 15px;
            margin-top: 15px;
          }
          .total-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 20px;
            font-weight: bold;
          }
          .payment-method-badge {
            background: #e9ecef;
            border-radius: 6px;
            padding: 8px 12px;
            text-align: center;
            margin-top: 15px;
            font-size: 12px;
            font-weight: bold;
            color: #2E8B57;
          }
          .status-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 20px;
          }
          .status-item {
            text-align: center;
          }
          .status-label {
            font-size: 12px;
            color: #6B7280;
            margin-bottom: 8px;
            text-transform: uppercase;
            font-weight: bold;
          }
          .status-badge {
            display: inline-block;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
            letter-spacing: 0.5px;
          }
          .status-paid {
            background: #10B981;
            color: white;
          }
          .status-confirmed {
            background: #2E8B57;
            color: white;
          }
          .status-pending {
            background: #F59E0B;
            color: white;
          }
          .footer {
            background: linear-gradient(135deg, #2E8B57, #228B22);
            color: white;
            padding: 30px;
            text-align: center;
            border-top: 3px solid #FFD700;
          }
          .footer-content {
            max-width: 400px;
            margin: 0 auto;
          }
          .thank-you-title {
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 10px;
          }
          .thank-you-message {
            font-size: 14px;
            opacity: 0.9;
            line-height: 1.6;
            margin-bottom: 15px;
          }
          .support-info {
            font-size: 12px;
            opacity: 0.8;
          }
          .qr-placeholder {
            width: 80px;
            height: 80px;
            background: white;
            border-radius: 8px;
            margin: 20px auto;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #2E8B57;
            font-weight: bold;
            font-size: 10px;
          }
        </style>
      </head>
      <body>
        <div class="receipt-container">
          <!-- Enhanced Header -->
          <div class="header">
            <div class="header-content">
              <div class="logo-section">
                <!-- Logo placeholder - in a real app, you'd include the actual logo -->
                <div style="width: 60px; height: 60px; background: rgba(255,255,255,0.2); border-radius: 50%; margin-bottom: 15px; display: flex; align-items: center; justify-content: center; font-size: 24px; font-weight: bold;">🏨</div>
              </div>
              <div class="hotel-name">${hotelInfo.name}</div>
              <div class="hotel-tagline">${hotelInfo.tagline}</div>
              <div class="hotel-contact">
                <div class="contact-item">📞 ${hotelInfo.phone}</div>
                <div class="contact-item">✉️ ${hotelInfo.email}</div>
                <div class="contact-item">📍 ${hotelInfo.address}</div>
              </div>
            </div>
          </div>

          <!-- Enhanced Receipt Info -->
          <div class="receipt-info">
            <div class="receipt-badge">
              <div class="receipt-label">OFFICIAL RECEIPT</div>
              <div class="receipt-number">${receiptNumber}</div>
            </div>
            <div class="info-grid">
              <div class="info-row">
                <span class="info-label">Date Issued</span>
                <span class="info-value">${new Date(payment.paid_at || payment.created_at).toLocaleDateString('en-US', {
                  weekday: 'short',
                  year: 'numeric',
                  month: 'short',
                  day: 'numeric'
                })}</span>
              </div>
              <div class="info-row">
                <span class="info-label">Time Issued</span>
                <span class="info-value">${new Date(payment.paid_at || payment.created_at).toLocaleTimeString('en-US', {
                  hour: '2-digit',
                  minute: '2-digit'
                })}</span>
              </div>
              <div class="info-row">
                <span class="info-label">Payment Method</span>
                <span class="info-value">${payment.payment_method.replace('_', ' ').toUpperCase()}</span>
              </div>
              <div class="info-row">
                <span class="info-label">Transaction ID</span>
                <span class="info-value">${payment.paystack_reference || payment.id.slice(-8)}</span>
              </div>
            </div>
          </div>

          <!-- Enhanced Content -->
          <div class="content">
            <!-- Guest Information -->
            <div class="section">
              <div class="section-header">
                <div class="section-icon">👤</div>
                <div class="section-title">Guest Information</div>
              </div>
              <div class="guest-card">
                <div class="guest-name">${reservation.guest.full_name}</div>
                <div class="guest-contact">✉️ ${reservation.guest.email}</div>
                ${reservation.guest.phone ? `<div class="guest-contact">📞 ${reservation.guest.phone}</div>` : ''}
              </div>
            </div>

            <!-- Reservation Details -->
            <div class="section">
              <div class="section-header">
                <div class="section-icon">🏨</div>
                <div class="section-title">Reservation Details</div>
              </div>

              <!-- Reservation ID -->
              <div class="info-row">
                <span class="info-label">Reservation ID</span>
                <span class="info-value">#${reservation.id.slice(-8).toUpperCase()}</span>
              </div>

              <!-- Room Information -->
              <div class="room-card">
                <div class="room-number">Room ${reservation.room.room_number}</div>
                <div class="room-type">${reservation.room.room_type} Room</div>
              </div>

              <!-- Stay Dates -->
              <div class="dates-container">
                <div class="date-card">
                  <div class="date-label">Check-in</div>
                  <div class="date-value">${new Date(reservation.check_in_date).toLocaleDateString('en-US', {
                    weekday: 'short',
                    month: 'short',
                    day: 'numeric',
                    year: 'numeric'
                  })}</div>
                </div>
                <div class="date-card">
                  <div class="date-label">Check-out</div>
                  <div class="date-value">${new Date(reservation.check_out_date).toLocaleDateString('en-US', {
                    weekday: 'short',
                    month: 'short',
                    day: 'numeric',
                    year: 'numeric'
                  })}</div>
                </div>
              </div>

              <!-- Nights Badge -->
              <div class="nights-badge">
                ${nights} night${nights > 1 ? 's' : ''}
              </div>

              <!-- Status Information -->
              <div class="status-container">
                <div class="status-item">
                  <div class="status-label">Reservation Status</div>
                  <div class="status-badge status-confirmed">${reservation.status}</div>
                </div>
                <div class="status-item">
                  <div class="status-label">Payment Status</div>
                  <div class="status-badge status-paid">${payment.status}</div>
                </div>
              </div>
            </div>

            <!-- Enhanced Payment Summary -->
            <div class="payment-summary">
              <div class="section-header">
                <div class="section-icon">💳</div>
                <div class="section-title">Payment Summary</div>
              </div>

              <div class="payment-breakdown">
                <div class="info-row">
                  <span class="info-label">Room Rate (${nights} night${nights > 1 ? 's' : ''})</span>
                  <span class="info-value">${formatPrice(subtotal)}</span>
                </div>
                <div class="info-row">
                  <span class="info-label">Service Charge</span>
                  <span class="info-value">Included</span>
                </div>
                <div class="info-row">
                  <span class="info-label">VAT (16%)</span>
                  <span class="info-value">Included</span>
                </div>
              </div>

              <div class="total-container">
                <div class="total-row">
                  <span>Total Paid</span>
                  <span>${formatPrice(total)}</span>
                </div>
              </div>

              <div class="payment-method-badge">
                Paid via ${payment.payment_method.replace('_', ' ').toUpperCase()}
              </div>
            </div>
          </div>

          <!-- Enhanced Footer -->
          <div class="footer">
            <div class="footer-content">
              <div class="thank-you-title">Thank You!</div>
              <div class="thank-you-message">
                We appreciate your business and hope you enjoy your stay at ${hotelInfo.name}.
                This is an official receipt for your hotel reservation.
              </div>
              <div class="qr-placeholder">
                QR CODE<br>📱
              </div>
              <div class="support-info">
                For support: ${hotelInfo.email} | ${hotelInfo.phone}<br>
                Visit us: ${hotelInfo.website}
              </div>
            </div>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  // Generate PDF receipt
  async generatePDFReceipt(reservationId: string, paymentId?: string): Promise<string | null> {
    try {
      console.log('📄 Generating PDF receipt...');
      
      const receiptData = await this.getReceiptData(reservationId, paymentId);
      if (!receiptData) {
        throw new Error('Failed to fetch receipt data');
      }

      const htmlContent = this.generateReceiptHTML(receiptData);
      const timestamp = new Date().toISOString().split('T')[0];
      const fileName = `receipt_${receiptData.receiptNumber}_${timestamp}.pdf`;

      // Generate PDF
      const { uri } = await Print.printToFileAsync({
        html: htmlContent,
        base64: false,
      });

      // Move to permanent location
      const finalUri = `${FileSystem.documentDirectory}${fileName}`;
      await FileSystem.moveAsync({
        from: uri,
        to: finalUri,
      });

      console.log('✅ PDF receipt generated:', finalUri);
      return finalUri;
    } catch (error) {
      console.error('❌ Error generating PDF receipt:', error);
      return null;
    }
  }

  // Share receipt
  async shareReceipt(fileUri: string): Promise<void> {
    try {
      const isAvailable = await Sharing.isAvailableAsync();
      if (isAvailable) {
        await Sharing.shareAsync(fileUri, {
          mimeType: 'application/pdf',
          dialogTitle: 'Share Receipt',
        });
      } else {
        throw new Error('Sharing is not available on this device');
      }
    } catch (error) {
      console.error('❌ Error sharing receipt:', error);
      throw error;
    }
  }

  // Generate and share receipt in one step
  async generateAndShareReceipt(reservationId: string, paymentId?: string): Promise<void> {
    try {
      const fileUri = await this.generatePDFReceipt(reservationId, paymentId);
      if (fileUri) {
        await this.shareReceipt(fileUri);
      } else {
        throw new Error('Failed to generate receipt');
      }
    } catch (error) {
      console.error('❌ Error generating and sharing receipt:', error);
      throw error;
    }
  }
}

export const receiptService = new ReceiptService();
