import { Alert } from 'react-native';
import { PostgrestError, AuthError } from '@supabase/supabase-js';

export interface AppError {
  message: string;
  code?: string;
  details?: any;
  timestamp: Date;
  severity: 'low' | 'medium' | 'high' | 'critical';
}

export class ErrorHandler {
  private static errors: AppError[] = [];
  private static maxErrors = 100;

  // Log error to internal store
  static logError(error: AppError): void {
    this.errors.unshift(error);
    if (this.errors.length > this.maxErrors) {
      this.errors = this.errors.slice(0, this.maxErrors);
    }
    
    // Log to console in development
    if (__DEV__) {
      console.error('App Error:', error);
    }
  }

  // Create standardized error
  static createError(
    message: string,
    code?: string,
    details?: any,
    severity: AppError['severity'] = 'medium'
  ): AppError {
    return {
      message,
      code,
      details,
      timestamp: new Date(),
      severity,
    };
  }

  // Handle Supabase errors
  static handleSupabaseError(error: PostgrestError | AuthError | Error): AppError {
    let message = 'An unexpected error occurred';
    let code = 'UNKNOWN_ERROR';
    let severity: AppError['severity'] = 'medium';

    if ('code' in error && error.code) {
      code = error.code;
      
      // Handle specific Supabase error codes
      switch (error.code) {
        case 'PGRST116':
          message = 'The requested resource was not found';
          severity = 'low';
          break;
        case 'PGRST204':
          message = 'No data found';
          severity = 'low';
          break;
        case '23505':
          message = 'This record already exists';
          severity = 'medium';
          break;
        case '23503':
          message = 'Cannot delete this record as it is referenced by other data';
          severity = 'medium';
          break;
        case '42501':
          message = 'You do not have permission to perform this action';
          severity = 'high';
          break;
        default:
          message = error.message || message;
      }
    } else if ('message' in error) {
      message = error.message;
    }

    const appError = this.createError(message, code, error, severity);
    this.logError(appError);
    return appError;
  }

  // Handle authentication errors
  static handleAuthError(error: AuthError | Error): AppError {
    let message = 'Authentication failed';
    let code = 'AUTH_ERROR';
    let severity: AppError['severity'] = 'medium';

    if ('message' in error) {
      const errorMessage = error.message.toLowerCase();
      
      if (errorMessage.includes('invalid login credentials')) {
        message = 'Invalid email or password';
        code = 'INVALID_CREDENTIALS';
      } else if (errorMessage.includes('email not confirmed')) {
        message = 'Please check your email and click the confirmation link';
        code = 'EMAIL_NOT_CONFIRMED';
      } else if (errorMessage.includes('user not found')) {
        message = 'No account found with this email address';
        code = 'USER_NOT_FOUND';
      } else if (errorMessage.includes('password')) {
        message = 'Password requirements not met';
        code = 'WEAK_PASSWORD';
      } else if (errorMessage.includes('email')) {
        message = 'Invalid email address format';
        code = 'INVALID_EMAIL';
      } else if (errorMessage.includes('network')) {
        message = 'Network connection failed. Please try again';
        code = 'NETWORK_ERROR';
        severity = 'high';
      } else {
        message = error.message;
      }
    }

    const appError = this.createError(message, code, error, severity);
    this.logError(appError);
    return appError;
  }

  // Handle network errors
  static handleNetworkError(error: Error): AppError {
    const message = 'Network connection failed. Please check your internet connection';
    const appError = this.createError(message, 'NETWORK_ERROR', error, 'high');
    this.logError(appError);
    return appError;
  }

  // Handle payment errors
  static handlePaymentError(error: any): AppError {
    let message = 'Payment processing failed';
    let code = 'PAYMENT_ERROR';
    let severity: AppError['severity'] = 'high';

    if (error?.message) {
      const errorMessage = error.message.toLowerCase();
      
      if (errorMessage.includes('insufficient funds')) {
        message = 'Insufficient funds in your account';
        code = 'INSUFFICIENT_FUNDS';
      } else if (errorMessage.includes('declined')) {
        message = 'Your payment was declined. Please try a different card';
        code = 'PAYMENT_DECLINED';
      } else if (errorMessage.includes('expired')) {
        message = 'Your card has expired';
        code = 'CARD_EXPIRED';
      } else if (errorMessage.includes('invalid card')) {
        message = 'Invalid card details';
        code = 'INVALID_CARD';
      } else if (errorMessage.includes('timeout')) {
        message = 'Payment timed out. Please try again';
        code = 'PAYMENT_TIMEOUT';
      } else {
        message = error.message;
      }
    }

    const appError = this.createError(message, code, error, severity);
    this.logError(appError);
    return appError;
  }

  // Handle validation errors
  static handleValidationError(field: string, message: string): AppError {
    const appError = this.createError(
      `${field}: ${message}`,
      'VALIDATION_ERROR',
      { field, message },
      'low'
    );
    this.logError(appError);
    return appError;
  }

  // Show error alert to user
  static showErrorAlert(error: AppError, onRetry?: () => void): void {
    const buttons: any[] = [{ text: 'OK', style: 'default' }];
    
    if (onRetry && error.severity !== 'critical') {
      buttons.unshift({ text: 'Retry', onPress: onRetry });
    }

    Alert.alert(
      'Error',
      error.message,
      buttons,
      { cancelable: true }
    );
  }

  // Show success message
  static showSuccessAlert(message: string, onOk?: () => void): void {
    Alert.alert(
      'Success',
      message,
      [{ text: 'OK', onPress: onOk }],
      { cancelable: true }
    );
  }

  // Show confirmation dialog
  static showConfirmDialog(
    title: string,
    message: string,
    onConfirm: () => void,
    onCancel?: () => void
  ): void {
    Alert.alert(
      title,
      message,
      [
        { text: 'Cancel', style: 'cancel', onPress: onCancel },
        { text: 'Confirm', style: 'destructive', onPress: onConfirm },
      ],
      { cancelable: true }
    );
  }

  // Get error history
  static getErrorHistory(): AppError[] {
    return [...this.errors];
  }

  // Clear error history
  static clearErrorHistory(): void {
    this.errors = [];
  }

  // Check if error is recoverable
  static isRecoverableError(error: AppError): boolean {
    const unrecoverableCodes = [
      'INVALID_CREDENTIALS',
      'USER_NOT_FOUND',
      'VALIDATION_ERROR',
      'PERMISSION_DENIED',
    ];
    
    return !unrecoverableCodes.includes(error.code || '');
  }

  // Format error for display
  static formatErrorForDisplay(error: AppError): string {
    const timestamp = error.timestamp.toLocaleString();
    return `[${timestamp}] ${error.message}${error.code ? ` (${error.code})` : ''}`;
  }

  // Hotel-specific error handlers
  static handleBookingError(error: any): AppError {
    let message = 'Booking failed';
    let code = 'BOOKING_ERROR';

    if (error?.message) {
      const errorMessage = error.message.toLowerCase();
      
      if (errorMessage.includes('room not available')) {
        message = 'This room is no longer available for the selected dates';
        code = 'ROOM_UNAVAILABLE';
      } else if (errorMessage.includes('invalid dates')) {
        message = 'Invalid check-in or check-out dates';
        code = 'INVALID_DATES';
      } else if (errorMessage.includes('maximum occupancy')) {
        message = 'Number of guests exceeds room capacity';
        code = 'EXCEEDS_CAPACITY';
      } else if (errorMessage.includes('minimum stay')) {
        message = 'Booking does not meet minimum stay requirements';
        code = 'MINIMUM_STAY_ERROR';
      }
    }

    const appError = this.createError(message, code, error, 'medium');
    this.logError(appError);
    return appError;
  }

  static handleRoomError(error: any): AppError {
    let message = 'Room operation failed';
    let code = 'ROOM_ERROR';

    if (error?.message) {
      const errorMessage = error.message.toLowerCase();
      
      if (errorMessage.includes('room number exists')) {
        message = 'A room with this number already exists';
        code = 'DUPLICATE_ROOM_NUMBER';
      } else if (errorMessage.includes('cannot delete')) {
        message = 'Cannot delete room with active bookings';
        code = 'ROOM_HAS_BOOKINGS';
      }
    }

    const appError = this.createError(message, code, error, 'medium');
    this.logError(appError);
    return appError;
  }

  // Retry mechanism
  static async retryOperation<T>(
    operation: () => Promise<T>,
    maxRetries: number = 3,
    delay: number = 1000
  ): Promise<T> {
    let lastError: any;
    
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error;
        
        if (attempt === maxRetries) {
          throw error;
        }
        
        // Exponential backoff
        await new Promise(resolve => setTimeout(resolve, delay * Math.pow(2, attempt - 1)));
      }
    }
    
    throw lastError;
  }
}

// Error boundary helper
export const withErrorHandling = <T extends any[], R>(
  fn: (...args: T) => Promise<R>
) => {
  return async (...args: T): Promise<{ data: R | null; error: AppError | null }> => {
    try {
      const data = await fn(...args);
      return { data, error: null };
    } catch (error) {
      const appError = ErrorHandler.handleSupabaseError(error as Error);
      return { data: null, error: appError };
    }
  };
};

// Validation helpers
export const validateEmail = (email: string): { valid: boolean; error?: string } => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  
  if (!email) {
    return { valid: false, error: 'Email is required' };
  }
  
  if (!emailRegex.test(email)) {
    return { valid: false, error: 'Please enter a valid email address' };
  }
  
  return { valid: true };
};

export const validatePassword = (password: string): { valid: boolean; error?: string } => {
  if (!password) {
    return { valid: false, error: 'Password is required' };
  }
  
  if (password.length < 8) {
    return { valid: false, error: 'Password must be at least 8 characters long' };
  }
  
  if (!/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(password)) {
    return { valid: false, error: 'Password must contain at least one uppercase letter, one lowercase letter, and one number' };
  }
  
  return { valid: true };
};

export const validatePhoneNumber = (phone: string): { valid: boolean; error?: string } => {
  const phoneRegex = /^\+?[\d\s\-\(\)]{10,}$/;
  
  if (!phone) {
    return { valid: true }; // Phone is optional
  }
  
  if (!phoneRegex.test(phone)) {
    return { valid: false, error: 'Please enter a valid phone number' };
  }
  
  return { valid: true };
};

export const validateBookingDates = (
  checkIn: string,
  checkOut: string
): { valid: boolean; error?: string } => {
  const checkInDate = new Date(checkIn);
  const checkOutDate = new Date(checkOut);
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  
  if (checkInDate < today) {
    return { valid: false, error: 'Check-in date cannot be in the past' };
  }
  
  if (checkOutDate <= checkInDate) {
    return { valid: false, error: 'Check-out date must be after check-in date' };
  }
  
  // Maximum booking period (1 year)
  const maxDate = new Date(checkInDate);
  maxDate.setFullYear(maxDate.getFullYear() + 1);
  
  if (checkOutDate > maxDate) {
    return { valid: false, error: 'Booking period cannot exceed 1 year' };
  }
  
  return { valid: true };
};
