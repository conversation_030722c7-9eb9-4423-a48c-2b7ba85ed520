-- Migration script to convert room images from string arrays to RoomImage objects
-- This script updates existing rooms to use the new image format with IDs

-- Create a function to convert legacy image arrays to the new format
CREATE OR REPLACE FUNCTION migrate_room_images()
RETURNS void
LANGUAGE plpgsql
AS $$
DECLARE
    room_record RECORD;
    image_url TEXT;
    new_images JSONB := '[]'::jsonb;
    image_index INTEGER;
    image_id TEXT;
    image_object JSONB;
BEGIN
    -- Loop through all rooms that have images
    FOR room_record IN 
        SELECT id, room_number, images 
        FROM public.rooms 
        WHERE images IS NOT NULL AND array_length(images, 1) > 0
    LOOP
        -- Reset for each room
        new_images := '[]'::jsonb;
        image_index := 1;
        
        -- Convert each image URL to a RoomImage object
        FOREACH image_url IN ARRAY room_record.images
        LOOP
            -- Generate a unique ID for the image
            image_id := 'img_' || extract(epoch from now())::bigint || '_' || image_index || '_' || substr(md5(random()::text), 1, 8);
            
            -- Create the image object
            image_object := jsonb_build_object(
                'id', image_id,
                'url', image_url,
                'alt_text', 'Room ' || room_record.room_number || ' image ' || image_index,
                'upload_date', now()::text
            );
            
            -- Add to the new images array
            new_images := new_images || image_object;
            
            image_index := image_index + 1;
        END LOOP;
        
        -- Update the room with the new image format
        -- First, we need to change the column type to JSONB to store objects
        -- This will be done in a separate step
        
        RAISE NOTICE 'Converted % images for room %', array_length(room_record.images, 1), room_record.room_number;
    END LOOP;
    
    RAISE NOTICE 'Migration completed successfully';
END;
$$;

-- Step 1: Add a new temporary column for the new image format
ALTER TABLE public.rooms ADD COLUMN IF NOT EXISTS images_new JSONB DEFAULT '[]'::jsonb;

-- Step 2: Migrate existing data
DO $$
DECLARE
    room_record RECORD;
    image_url TEXT;
    new_images JSONB := '[]'::jsonb;
    image_index INTEGER;
    image_id TEXT;
    image_object JSONB;
BEGIN
    -- Loop through all rooms that have images
    FOR room_record IN 
        SELECT id, room_number, images 
        FROM public.rooms 
        WHERE images IS NOT NULL AND array_length(images, 1) > 0
    LOOP
        -- Reset for each room
        new_images := '[]'::jsonb;
        image_index := 1;
        
        -- Convert each image URL to a RoomImage object
        FOREACH image_url IN ARRAY room_record.images
        LOOP
            -- Generate a unique ID for the image
            image_id := 'img_' || extract(epoch from now())::bigint || '_' || image_index || '_' || substr(md5(random()::text), 1, 8);
            
            -- Create the image object
            image_object := jsonb_build_object(
                'id', image_id,
                'url', image_url,
                'alt_text', 'Room ' || room_record.room_number || ' image ' || image_index,
                'upload_date', now()::text
            );
            
            -- Add to the new images array
            new_images := new_images || image_object;
            
            image_index := image_index + 1;
        END LOOP;
        
        -- Update the room with the new image format
        UPDATE public.rooms 
        SET images_new = new_images 
        WHERE id = room_record.id;
        
        RAISE NOTICE 'Converted % images for room %', array_length(room_record.images, 1), room_record.room_number;
    END LOOP;
    
    RAISE NOTICE 'Migration completed successfully';
END;
$$;

-- Step 3: Drop the old column and rename the new one
-- WARNING: This will permanently remove the old image format
-- Make sure to backup your data before running this step

-- Uncomment the following lines when you're ready to complete the migration:

-- ALTER TABLE public.rooms DROP COLUMN images;
-- ALTER TABLE public.rooms RENAME COLUMN images_new TO images;

-- Step 4: Update the column comment for documentation
-- COMMENT ON COLUMN public.rooms.images IS 'Room images stored as JSONB array of objects with id, url, alt_text, and upload_date';

-- Verification query to check the migration
-- SELECT 
--     room_number,
--     jsonb_array_length(images_new) as image_count,
--     images_new
-- FROM public.rooms 
-- WHERE images_new IS NOT NULL AND jsonb_array_length(images_new) > 0
-- ORDER BY room_number;

SELECT 'Room images migration script created. Review and execute steps carefully.' as status;
