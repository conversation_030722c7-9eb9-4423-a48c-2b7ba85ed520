// Type fixes and augmentations for better compatibility

// Additional type declarations for third-party packages without proper types
declare module 'react-native-super-grid' {
  export const FlatGrid: any;
}

declare module 'react-native-gifted-charts' {
  export const BarChart: any;
  export const LineChart: any;
  export const PieChart: any;
}

declare module 'react-native-chart-kit' {
  export const LineChart: any;
  export const BarChart: any;
  export const PieChart: any;
  export const ProgressChart: any;
  export const ContributionGraph: any;
  export const StackedBarChart: any;
}

declare module 'react-native-html-to-pdf' {
  export interface Options {
    html: string;
    fileName: string;
    base64?: boolean;
    [key: string]: any;
  }
  
  export interface Result {
    filePath: string;
    base64?: string;
  }
  
  export default function RNHTMLtoPDF(options: Options): Promise<Result>;
}
