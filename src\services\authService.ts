import { supabase } from './supabase';
import { authService as baseAuthService } from './supabase';

// Extended auth service with additional methods
export const authService = {
  ...baseAuthService,
  
  // Get user profile from the users table
  async getUserProfile(userId: string) {
    const { data, error } = await supabase
      .from('users')
      .select('*')
      .eq('id', userId)
      .single();
    return { data, error };
  },

  // Update user data
  async updateUser({ data }: { data: Record<string, any> }) {
    const { data: updateData, error } = await supabase.auth.updateUser({
      data
    });
    return { data: updateData, error };
  },

  // Ensure user profile exists in the users table
  async ensureUserProfile(user: any) {
    // Check if user profile exists
    const { data: existingUser } = await this.getUserProfile(user.id);
    
    if (!existingUser) {
      // Create user profile if it doesn't exist
      const userData = {
        id: user.id,
        email: user.email,
        full_name: user.user_metadata?.full_name || '',
        phone: user.user_metadata?.phone || null,
        role: user.user_metadata?.role || 'guest',
      };
      
      const { error } = await supabase
        .from('users')
        .insert([userData]);
        
      if (error) {
        console.error('Error creating user profile:', error);
      }
    }
  }
}; 