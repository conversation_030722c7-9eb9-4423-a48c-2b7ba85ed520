import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { Card } from 'react-native-paper';
import { useAuthStore } from '../../store/authStore';
import { usePermissions } from '../../hooks/usePermissions';
import { colors, spacing, typography } from '../../constants';

interface RoleDebuggerProps {
  visible?: boolean;
}

export const RoleDebugger: React.FC<RoleDebuggerProps> = ({ visible = true }) => {
  const { user, session } = useAuthStore();
  const { isAdmin, isReceptionist, isStaff, isGuest, userRole } = usePermissions();

  if (!visible || !user) return null;

  const handleRefreshRole = async () => {
    // Force re-initialize auth store
    const { initialize } = useAuthStore.getState();
    await initialize();
  };

  return (
    <Card style={styles.container}>
      <Card.Content>
        <Text style={styles.title}>🔍 Role Debug Info</Text>
        
        <View style={styles.infoRow}>
          <Text style={styles.label}>Email:</Text>
          <Text style={styles.value}>{user.email}</Text>
        </View>
        
        <View style={styles.infoRow}>
          <Text style={styles.label}>User Role:</Text>
          <Text style={[styles.value, styles.roleValue]}>{user.role}</Text>
        </View>
        
        <View style={styles.infoRow}>
          <Text style={styles.label}>Permission Role:</Text>
          <Text style={[styles.value, styles.roleValue]}>{userRole}</Text>
        </View>
        
        <View style={styles.infoRow}>
          <Text style={styles.label}>Is Admin:</Text>
          <Text style={[styles.value, isAdmin ? styles.success : styles.error]}>
            {isAdmin ? '✅ YES' : '❌ NO'}
          </Text>
        </View>
        
        <View style={styles.infoRow}>
          <Text style={styles.label}>Is Staff:</Text>
          <Text style={[styles.value, isStaff ? styles.success : styles.error]}>
            {isStaff ? '✅ YES' : '❌ NO'}
          </Text>
        </View>
        
        <View style={styles.infoRow}>
          <Text style={styles.label}>Is Guest:</Text>
          <Text style={[styles.value, isGuest ? styles.success : styles.error]}>
            {isGuest ? '✅ YES' : '❌ NO'}
          </Text>
        </View>
        
        <View style={styles.infoRow}>
          <Text style={styles.label}>JWT Metadata:</Text>
          <Text style={styles.value}>
            {session?.user?.user_metadata?.role || 'none'}
          </Text>
        </View>
        
        <TouchableOpacity style={styles.refreshButton} onPress={handleRefreshRole}>
          <Text style={styles.refreshButtonText}>🔄 Refresh Role</Text>
        </TouchableOpacity>
        
        <Text style={styles.instructions}>
          If role shows 'guest' but you're admin:
          {'\n'}1. Run fix-admin-role-sync.sql
          {'\n'}2. Log out completely
          {'\n'}3. Log back in
        </Text>
      </Card.Content>
    </Card>
  );
};

const styles = StyleSheet.create({
  container: {
    margin: spacing.md,
    backgroundColor: colors.surface,
  },
  title: {
    fontSize: typography.sizes.lg,
    fontWeight: 'bold',
    color: colors.primary,
    marginBottom: spacing.md,
    textAlign: 'center',
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: spacing.xs,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  label: {
    fontSize: typography.sizes.sm,
    fontWeight: '600',
    color: colors.textSecondary,
    flex: 1,
  },
  value: {
    fontSize: typography.sizes.sm,
    color: colors.text,
    flex: 1,
    textAlign: 'right',
  },
  roleValue: {
    fontWeight: 'bold',
    fontSize: typography.sizes.md,
  },
  success: {
    color: colors.success,
    fontWeight: 'bold',
  },
  error: {
    color: colors.error,
    fontWeight: 'bold',
  },
  refreshButton: {
    backgroundColor: colors.primary,
    padding: spacing.sm,
    borderRadius: 8,
    marginTop: spacing.md,
    alignItems: 'center',
  },
  refreshButtonText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: typography.sizes.sm,
  },
  instructions: {
    fontSize: typography.sizes.xs,
    color: colors.textSecondary,
    marginTop: spacing.sm,
    fontStyle: 'italic',
    textAlign: 'center',
  },
});
