import React from 'react';
import {
  View,
  StyleSheet,
  Image,
  Dimensions,
} from 'react-native';
import {
  Text,
  Surface,
  Divider,
} from 'react-native-paper';
import { MaterialIcons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { colors, spacing, typography, APP_CONSTANTS } from '../../constants';
import { formatPrice } from '../../utils/currency';
import type { ReceiptData } from '../../services/receiptService';

const { width: screenWidth } = Dimensions.get('window');

interface PrintableReceiptProps {
  receiptData: ReceiptData;
  style?: any;
}

export const PrintableReceipt: React.FC<PrintableReceiptProps> = ({
  receiptData,
  style,
}) => {
  const { reservation, payment, receiptNumber, nights } = receiptData;

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'paid':
        return colors.success;
      case 'confirmed':
        return colors.primary;
      case 'pending':
        return colors.warning;
      case 'checked_in':
        return colors.info;
      case 'checked_out':
        return colors.onSurfaceVariant;
      case 'cancelled':
        return colors.error;
      case 'failed':
        return colors.error;
      case 'refunded':
        return colors.info;
      default:
        return colors.outline;
    }
  };

  const getStatusTextColor = (status: string) => {
    switch (status) {
      case 'pending':
        return '#000000'; // Black text on yellow/orange
      default:
        return '#FFFFFF'; // White text default
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'paid':
        return 'Paid';
      case 'confirmed':
        return 'Confirmed';
      case 'pending':
        return 'Pending';
      case 'checked_in':
        return 'Checked In';
      case 'checked_out':
        return 'Checked Out';
      case 'cancelled':
        return 'Cancelled';
      case 'failed':
        return 'Failed';
      case 'refunded':
        return 'Refunded';
      default:
        return status?.charAt(0).toUpperCase() + status?.slice(1) || 'Unknown';
    }
  };

  return (
    <Surface style={[styles.container, style]}>
      {/* Header with Logo and Branding */}
      <LinearGradient
        colors={[colors.primary, colors.primaryDark]}
        style={styles.header}
      >
        <View style={styles.headerContent}>
          {/* Hotel Logo */}
          <View style={styles.logoContainer}>
            <Image
              source={require('../../../assets/icon.png')}
              style={styles.logo}
              resizeMode="contain"
            />
            <View style={styles.hotelInfo}>
              <Text style={styles.hotelName}>{APP_CONSTANTS.APP_NAME}</Text>
              <Text style={styles.hotelTagline}>Luxury • Comfort • Excellence</Text>
            </View>
          </View>
          
          {/* Receipt Badge */}
          <Surface style={styles.receiptBadge}>
            <MaterialIcons name="receipt-long" size={16} color={colors.primary} />
            <Text style={styles.receiptLabel}>RECEIPT</Text>
            <Text style={styles.receiptNumber}>#{receiptNumber}</Text>
          </Surface>
        </View>
        
        {/* Contact Information */}
        <View style={styles.contactInfo}>
          <View style={styles.contactRow}>
            <MaterialIcons name="phone" size={12} color={colors.white} />
            <Text style={styles.contactText}>{APP_CONSTANTS.SUPPORT_PHONE}</Text>
          </View>
          <View style={styles.contactRow}>
            <MaterialIcons name="email" size={12} color={colors.white} />
            <Text style={styles.contactText}>{APP_CONSTANTS.SUPPORT_EMAIL}</Text>
          </View>
          <View style={styles.contactRow}>
            <MaterialIcons name="location-on" size={12} color={colors.white} />
            <Text style={styles.contactText}>Mombasa, Kenya</Text>
          </View>
        </View>
      </LinearGradient>

      {/* Content */}
      <View style={styles.content}>
        {/* Receipt Information */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Receipt Information</Text>
          <View style={styles.infoGrid}>
            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>Date Issued:</Text>
              <Text style={styles.infoValue}>
                {new Date(payment.paid_at || payment.created_at).toLocaleDateString('en-US', {
                  year: 'numeric',
                  month: 'short',
                  day: 'numeric'
                })}
              </Text>
            </View>
            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>Payment Method:</Text>
              <Text style={styles.infoValue}>
                {payment.payment_method.replace('_', ' ').toUpperCase()}
              </Text>
            </View>
            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>Transaction ID:</Text>
              <Text style={styles.infoValue}>
                {payment.paystack_reference || payment.id.slice(-8)}
              </Text>
            </View>
          </View>
        </View>

        <Divider style={styles.divider} />

        {/* Guest Information */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Guest Information</Text>
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>Name:</Text>
            <Text style={styles.infoValue}>{reservation.guest.full_name}</Text>
          </View>
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>Email:</Text>
            <Text style={styles.infoValue}>{reservation.guest.email}</Text>
          </View>
          {reservation.guest.phone && (
            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>Phone:</Text>
              <Text style={styles.infoValue}>{reservation.guest.phone}</Text>
            </View>
          )}
        </View>

        <Divider style={styles.divider} />

        {/* Reservation Details */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Reservation Details</Text>
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>Reservation ID:</Text>
            <Text style={styles.infoValue}>
              #{reservation.id.slice(-8).toUpperCase()}
            </Text>
          </View>
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>Room:</Text>
            <Text style={styles.infoValue}>
              Room {reservation.room.room_number} ({reservation.room.room_type})
            </Text>
          </View>
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>Check-in:</Text>
            <Text style={styles.infoValue}>
              {new Date(reservation.check_in_date).toLocaleDateString()}
            </Text>
          </View>
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>Check-out:</Text>
            <Text style={styles.infoValue}>
              {new Date(reservation.check_out_date).toLocaleDateString()}
            </Text>
          </View>
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>Nights:</Text>
            <Text style={styles.infoValue}>
              {nights} night{nights > 1 ? 's' : ''}
            </Text>
          </View>
        </View>

        <Divider style={styles.divider} />

        {/* Payment Summary */}
        <Surface style={styles.paymentSummary}>
          <Text style={styles.sectionTitle}>Payment Summary</Text>
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>
              Room Rate ({nights} night{nights > 1 ? 's' : ''}):
            </Text>
            <Text style={styles.infoValue}>{formatPrice(reservation.total_amount)}</Text>
          </View>
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>Service Charge:</Text>
            <Text style={styles.infoValue}>Included</Text>
          </View>
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>VAT (16%):</Text>
            <Text style={styles.infoValue}>Included</Text>
          </View>
          <Divider style={styles.summaryDivider} />
          <View style={[styles.infoRow, styles.totalRow]}>
            <Text style={styles.totalLabel}>Total Paid:</Text>
            <Text style={styles.totalValue}>{formatPrice(payment.amount)}</Text>
          </View>
        </Surface>

        {/* Status Information */}
        <View style={styles.statusContainer}>
          <View style={styles.statusItem}>
            <Text style={styles.statusLabel}>Reservation Status</Text>
            <View style={[
              styles.statusBadge,
              { backgroundColor: getStatusColor(reservation.status) }
            ]}>
              <Text style={[
                styles.statusText,
                { color: getStatusTextColor(reservation.status) }
              ]}>
                {getStatusText(reservation.status)}
              </Text>
            </View>
          </View>
          
          <View style={styles.statusItem}>
            <Text style={styles.statusLabel}>Payment Status</Text>
            <View style={[
              styles.statusBadge,
              { backgroundColor: getStatusColor(payment.status) }
            ]}>
              <Text style={[
                styles.statusText,
                { color: getStatusTextColor(payment.status) }
              ]}>
                {getStatusText(payment.status)}
              </Text>
            </View>
          </View>
        </View>

        {/* Footer */}
        <View style={styles.footer}>
          <Text style={styles.thankYouText}>Thank You for Choosing {APP_CONSTANTS.APP_NAME}!</Text>
          <Text style={styles.supportText}>
            For support, contact us at {APP_CONSTANTS.SUPPORT_EMAIL}
          </Text>
        </View>
      </View>
    </Surface>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.white,
    borderRadius: 8,
    overflow: 'hidden',
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
  },
  header: {
    paddingVertical: spacing.lg,
    paddingHorizontal: spacing.md,
  },
  headerContent: {
    marginBottom: spacing.md,
  },
  logoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  logo: {
    width: 40,
    height: 40,
    marginRight: spacing.sm,
  },
  hotelInfo: {
    flex: 1,
  },
  hotelName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.white,
    marginBottom: 2,
  },
  hotelTagline: {
    fontSize: 10,
    color: colors.white,
    opacity: 0.9,
    fontStyle: 'italic',
  },
  receiptBadge: {
    backgroundColor: colors.white,
    borderRadius: 8,
    padding: spacing.sm,
    alignItems: 'center',
    alignSelf: 'flex-end',
    minWidth: 100,
  },
  receiptLabel: {
    fontSize: 10,
    fontWeight: 'bold',
    color: colors.primary,
    marginTop: 2,
    letterSpacing: 1,
  },
  receiptNumber: {
    fontSize: 14,
    fontWeight: 'bold',
    color: colors.primary,
    marginTop: 2,
  },
  contactInfo: {
    gap: 4,
  },
  contactRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.xs,
  },
  contactText: {
    color: colors.white,
    fontSize: 10,
    opacity: 0.9,
  },
  content: {
    padding: spacing.md,
  },
  section: {
    marginBottom: spacing.md,
  },
  sectionTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: colors.primary,
    marginBottom: spacing.sm,
  },
  infoGrid: {
    gap: spacing.xs,
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 2,
  },
  infoLabel: {
    fontSize: 12,
    color: colors.onSurfaceVariant,
    flex: 1,
  },
  infoValue: {
    fontSize: 12,
    fontWeight: '500',
    color: colors.onSurface,
    flex: 1,
    textAlign: 'right',
  },
  divider: {
    marginVertical: spacing.sm,
  },
  paymentSummary: {
    padding: spacing.sm,
    backgroundColor: colors.surfaceVariant,
    borderRadius: 6,
    marginBottom: spacing.md,
  },
  summaryDivider: {
    marginVertical: spacing.xs,
    backgroundColor: colors.primary,
    height: 1,
  },
  totalRow: {
    borderTopWidth: 1,
    borderTopColor: colors.primary,
    paddingTop: spacing.xs,
    marginTop: spacing.xs,
  },
  totalLabel: {
    fontSize: 14,
    fontWeight: 'bold',
    color: colors.onSurface,
  },
  totalValue: {
    fontSize: 14,
    fontWeight: 'bold',
    color: colors.primary,
  },
  statusContainer: {
    flexDirection: 'row',
    gap: spacing.sm,
    marginBottom: spacing.md,
  },
  statusItem: {
    flex: 1,
    alignItems: 'center',
  },
  statusLabel: {
    fontSize: 10,
    color: colors.onSurfaceVariant,
    marginBottom: spacing.xs,
    textAlign: 'center',
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
    minWidth: 60,
    alignItems: 'center',
    justifyContent: 'center',
  },
  statusText: {
    fontSize: 9,
    fontWeight: '700',
    textAlign: 'center',
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  footer: {
    alignItems: 'center',
    paddingTop: spacing.md,
    borderTopWidth: 1,
    borderTopColor: colors.outline,
  },
  thankYouText: {
    fontSize: 12,
    fontWeight: 'bold',
    color: colors.primary,
    textAlign: 'center',
    marginBottom: spacing.xs,
  },
  supportText: {
    fontSize: 10,
    color: colors.onSurfaceVariant,
    textAlign: 'center',
  },
});
