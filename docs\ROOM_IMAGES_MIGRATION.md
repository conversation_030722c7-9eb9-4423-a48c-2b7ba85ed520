# Room Images Migration Guide

## Overview

This document describes the migration from legacy string-based room images to the new RoomImage object format with unique IDs.

## Problem Statement

Previously, room images were stored as simple string arrays containing only URLs:
```typescript
images: string[] // ["url1", "url2", "url3"]
```

This caused several issues:
1. **No unique keys**: React components used array indices as keys, causing rendering issues
2. **No image metadata**: Couldn't track alt text, upload dates, file sizes, etc.
3. **Difficult image management**: Hard to update or delete specific images
4. **Poor accessibility**: No alt text for screen readers

## Solution

### New RoomImage Interface

```typescript
interface RoomImage {
  id: string;           // Unique identifier for the image
  url: string;          // Image URL (same as before)
  alt_text?: string;    // Accessibility text
  upload_date?: string; // When the image was uploaded
  file_name?: string;   // Original file name
  file_size?: number;   // File size in bytes
}
```

### Updated Room Interface

```typescript
interface Room {
  // ... other fields
  images: RoomImage[];  // Changed from string[] to RoomImage[]
}
```

## Migration Strategy

### 1. Backward Compatibility

The system maintains full backward compatibility:
- Legacy string arrays are automatically converted to RoomImage objects
- Existing data continues to work without manual migration
- New uploads use the enhanced format

### 2. Automatic Conversion

The `RoomImageMigrationService` handles conversion automatically:

```typescript
// Legacy format
const legacyImages = ["url1", "url2"];

// Automatically converted to
const newImages = [
  {
    id: "legacy_1234567890_0_abc123",
    url: "url1",
    alt_text: "Room image 1",
    upload_date: "2024-01-01T00:00:00.000Z"
  },
  // ...
];
```

### 3. Database Migration (Optional)

For better performance and consistency, you can migrate existing data:

```sql
-- Run the migration script
\i supabase/migrate-room-images.sql
```

## Implementation Details

### Files Modified

1. **Type Definitions**
   - `src/types/index.ts` - Added RoomImage interface
   - `src/types/database.ts` - Updated database types
   - `src/types/models.ts` - Updated model types

2. **Services**
   - `src/services/imageService.ts` - Enhanced image upload/management
   - `src/services/supabase.ts` - Added automatic conversion
   - `src/services/roomImageMigrationService.ts` - New migration utilities

3. **Components**
   - `src/screens/guest/RoomDetailsScreen.tsx` - Uses proper image IDs as keys
   - `src/components/cards/RoomCard.tsx` - Handles both formats
   - `src/screens/admin/AddEditRoomScreen.tsx` - Enhanced image management

### Key Features

1. **Unique Image IDs**: Each image has a unique identifier for React keys
2. **Accessibility**: Alt text support for screen readers
3. **Metadata**: Track upload dates, file names, and sizes
4. **Backward Compatibility**: Legacy data works seamlessly
5. **Migration Tools**: Utilities for data conversion

## Usage Examples

### Displaying Images with Proper Keys

```typescript
// Before (problematic)
{room.images.map((image, index) => (
  <Image key={index} source={{ uri: image }} />
))}

// After (correct)
{room.images.map((image) => (
  <Image 
    key={image.id} 
    source={{ uri: image.url }}
    accessibilityLabel={image.alt_text}
  />
))}
```

### Creating New Images

```typescript
const newImage: RoomImage = {
  id: imageService.generateImageId(),
  url: uploadedUrl,
  alt_text: `Room ${roomNumber} image`,
  upload_date: new Date().toISOString(),
  file_name: 'room-image.jpg',
  file_size: 1024000
};
```

### Handling Legacy Data

```typescript
// Automatic conversion
const normalizedImages = RoomImageMigrationService.normalizeRoomImages(room.images);

// Check format
if (RoomImageMigrationService.isLegacyFormat(room.images)) {
  // Handle legacy format
}
```

## Testing

### Manual Testing

1. **Legacy Data**: Verify existing rooms with string arrays display correctly
2. **New Uploads**: Confirm new images use the RoomImage format
3. **Image Management**: Test adding/removing images in admin panel
4. **Accessibility**: Verify alt text is properly set

### Automated Testing

```typescript
// Test image conversion
const legacyImages = ["url1", "url2"];
const converted = RoomImageMigrationService.convertLegacyImages(legacyImages);
expect(converted[0]).toHaveProperty('id');
expect(converted[0]).toHaveProperty('url', 'url1');
```

## Rollback Plan

If issues arise, you can temporarily revert to legacy format:

1. Update components to use array indices as keys
2. Extract URLs from RoomImage objects: `images.map(img => img.url)`
3. Restore database schema if migration was applied

## Performance Considerations

1. **Memory**: RoomImage objects use slightly more memory than strings
2. **Database**: JSONB storage is efficient for the new format
3. **Network**: No impact on API response sizes
4. **Rendering**: Proper keys improve React performance

## Future Enhancements

1. **Image Optimization**: Add thumbnail URLs and responsive images
2. **CDN Integration**: Support for content delivery networks
3. **Image Validation**: Verify image dimensions and formats
4. **Bulk Operations**: Tools for managing multiple images
5. **Analytics**: Track image view counts and performance

## Conclusion

The migration to RoomImage objects provides:
- ✅ Unique image identifiers
- ✅ Better accessibility
- ✅ Enhanced metadata
- ✅ Improved React performance
- ✅ Backward compatibility
- ✅ Future extensibility

The implementation is production-ready and maintains full compatibility with existing data.
