import { reportService, ReportData } from '../services/reportService';
import { generateSampleReservations, generateSampleRooms } from './sampleData';

// Test function to verify report generation works
export const testReportGeneration = async (): Promise<void> => {
  console.log('🧪 Testing report generation...');

  try {
    // Create sample data
    const sampleReservations = generateSampleReservations();
    const sampleRooms = generateSampleRooms();

    // Create sample analytics data
    const sampleReportData: ReportData = {
      totalRevenue: 250000,
      totalReservations: 45,
      averageBookingValue: 5555,
      occupancyRate: 75.5,
      cancelationRate: 8.9,
      topRoomTypes: [
        { type: 'deluxe', count: 15, revenue: 120000 },
        { type: 'suite', count: 10, revenue: 80000 },
        { type: 'standard', count: 12, revenue: 36000 },
        { type: 'presidential', count: 3, revenue: 24000 },
      ],
      monthlyRevenue: [
        { month: 'Jan', revenue: 45000 },
        { month: 'Feb', revenue: 38000 },
        { month: 'Mar', revenue: 52000 },
        { month: 'Apr', revenue: 48000 },
        { month: 'May', revenue: 41000 },
        { month: 'Jun', revenue: 46000 },
      ],
      bookingsByStatus: [
        { status: 'Confirmed', count: 25, color: '#10B981' },
        { status: 'Checked In', count: 8, color: '#3B82F6' },
        { status: 'Checked Out', count: 7, color: '#6B7280' },
        { status: 'Pending', count: 3, color: '#F59E0B' },
        { status: 'Cancelled', count: 2, color: '#EF4444' },
      ],
      seasonalTrends: [
        { period: 'Q1', bookings: 18 },
        { period: 'Q2', bookings: 22 },
        { period: 'Q3', bookings: 15 },
        { period: 'Q4', bookings: 20 },
      ],
      period: 'month',
      generatedAt: new Date().toISOString(),
      reservations: sampleReservations,
      rooms: sampleRooms,
    };

    // Test CSV generation
    console.log('📊 Testing CSV report generation...');
    const csvFile = await reportService.generateCSVReport(sampleReportData, {
      format: 'csv',
      type: 'summary',
    });
    console.log('✅ CSV report generated:', csvFile);

    // Test detailed CSV generation
    console.log('📊 Testing detailed CSV report generation...');
    const detailedCsvFile = await reportService.generateCSVReport(sampleReportData, {
      format: 'csv',
      type: 'detailed',
    });
    console.log('✅ Detailed CSV report generated:', detailedCsvFile);

    // Test reservations CSV generation
    console.log('📊 Testing reservations CSV report generation...');
    const reservationsCsvFile = await reportService.generateCSVReport(sampleReportData, {
      format: 'csv',
      type: 'reservations',
    });
    console.log('✅ Reservations CSV report generated:', reservationsCsvFile);

    // Test rooms CSV generation
    console.log('📊 Testing rooms CSV report generation...');
    const roomsCsvFile = await reportService.generateCSVReport(sampleReportData, {
      format: 'csv',
      type: 'rooms',
    });
    console.log('✅ Rooms CSV report generated:', roomsCsvFile);

    // Test HTML/PDF generation
    console.log('📊 Testing HTML report generation...');
    const htmlFile = await reportService.generatePDFReport(sampleReportData, {
      format: 'pdf',
      type: 'summary',
    });
    console.log('✅ HTML report generated:', htmlFile);

    console.log('🎉 All report generation tests passed!');

  } catch (error) {
    console.error('❌ Report generation test failed:', error);
    throw error;
  }
};

// Test function that can be called from the analytics screen
export const runReportTests = async (): Promise<boolean> => {
  try {
    await testReportGeneration();
    return true;
  } catch (error) {
    console.error('Report tests failed:', error);
    return false;
  }
};
