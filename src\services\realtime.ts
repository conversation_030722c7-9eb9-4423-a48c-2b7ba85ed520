import { supabase } from './supabase';
import type { RealtimeChannel } from '@supabase/supabase-js';

export interface RealtimeSubscription {
  channel: RealtimeChannel;
  unsubscribe: () => void;
}

export class RealtimeService {
  private subscriptions: Map<string, RealtimeSubscription> = new Map();

  // Subscribe to room updates
  subscribeToRooms(callback: (payload: any) => void): RealtimeSubscription {
    const channel = supabase
      .channel('rooms-changes')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'rooms',
        },
        (payload) => {
          console.log('Room change received:', payload);
          callback(payload);
        }
      )
      .subscribe();

    const subscription = {
      channel,
      unsubscribe: () => {
        supabase.removeChannel(channel);
        this.subscriptions.delete('rooms');
      },
    };

    this.subscriptions.set('rooms', subscription);
    return subscription;
  }

  // Subscribe to reservation updates
  subscribeToReservations(callback: (payload: any) => void): RealtimeSubscription {
    const channel = supabase
      .channel('reservations-changes')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'reservations',
        },
        (payload) => {
          console.log('Reservation change received:', payload);
          callback(payload);
        }
      )
      .subscribe();

    const subscription = {
      channel,
      unsubscribe: () => {
        supabase.removeChannel(channel);
        this.subscriptions.delete('reservations');
      },
    };

    this.subscriptions.set('reservations', subscription);
    return subscription;
  }

  // Subscribe to user-specific reservations
  subscribeToUserReservations(userId: string, callback: (payload: any) => void): RealtimeSubscription {
    const channel = supabase
      .channel(`user-reservations-${userId}`)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'reservations',
          filter: `guest_id=eq.${userId}`,
        },
        (payload) => {
          console.log('User reservation change received:', payload);
          callback(payload);
        }
      )
      .subscribe();

    const subscription = {
      channel,
      unsubscribe: () => {
        supabase.removeChannel(channel);
        this.subscriptions.delete(`user-reservations-${userId}`);
      },
    };

    this.subscriptions.set(`user-reservations-${userId}`, subscription);
    return subscription;
  }

  // Subscribe to payment updates
  subscribeToPayments(callback: (payload: any) => void): RealtimeSubscription {
    const channel = supabase
      .channel('payments-changes')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'payments',
        },
        (payload) => {
          console.log('Payment change received:', payload);
          callback(payload);
        }
      )
      .subscribe();

    const subscription = {
      channel,
      unsubscribe: () => {
        supabase.removeChannel(channel);
        this.subscriptions.delete('payments');
      },
    };

    this.subscriptions.set('payments', subscription);
    return subscription;
  }

  // Subscribe to user presence
  subscribeToPresence(roomId: string, userId: string, userInfo: any): RealtimeChannel {
    const channel = supabase.channel(`presence-${roomId}`, {
      config: {
        presence: {
          key: userId,
        },
      },
    });

    channel
      .on('presence', { event: 'sync' }, () => {
        console.log('Presence sync:', channel.presenceState());
      })
      .on('presence', { event: 'join' }, ({ key, newPresences }) => {
        console.log('User joined:', key, newPresences);
      })
      .on('presence', { event: 'leave' }, ({ key, leftPresences }) => {
        console.log('User left:', key, leftPresences);
      })
      .subscribe(async (status) => {
        if (status === 'SUBSCRIBED') {
          await channel.track(userInfo);
        }
      });

    return channel;
  }

  // Unsubscribe from specific channel
  unsubscribe(key: string): void {
    const subscription = this.subscriptions.get(key);
    if (subscription) {
      subscription.unsubscribe();
    }
  }

  // Unsubscribe from all channels
  unsubscribeAll(): void {
    this.subscriptions.forEach((subscription) => {
      subscription.unsubscribe();
    });
    this.subscriptions.clear();
  }

  // Send real-time message
  async sendMessage(channel: string, event: string, payload: any): Promise<void> {
    const channelInstance = supabase.channel(channel);
    await channelInstance.send({
      type: 'broadcast',
      event,
      payload,
    });
  }

  // Listen to broadcast messages
  subscribeToBroadcast(
    channel: string,
    event: string,
    callback: (payload: any) => void
  ): RealtimeChannel {
    return supabase
      .channel(channel)
      .on('broadcast', { event }, callback)
      .subscribe();
  }
}

export const realtimeService = new RealtimeService();
