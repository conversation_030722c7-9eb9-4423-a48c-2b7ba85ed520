import { Platform } from 'expo-modules-core';
import * as Device from 'expo-device';
import * as Notifications from 'expo-notifications';
import Constants from 'expo-constants';

// Check if we're running in Expo Go
const isExpoGo = Constants.executionEnvironment === 'storeClient';

// Configure notification handler only if not in Expo Go
if (!isExpoGo) {
  Notifications.setNotificationHandler({
    handleNotification: async () => ({
      shouldShowAlert: true,
      shouldPlaySound: true,
      shouldSetBadge: true,
    }),
  });
}

export interface NotificationData {
  title: string;
  body: string;
  data?: Record<string, any>;
  categoryId?: string;
}

export interface PushToken {
  token: string;
  platform: 'ios' | 'android' | 'web';
}

export class NotificationService {
  private pushToken: string | null = null;

  // Initialize notification service
  async initialize(): Promise<void> {
    // Skip initialization in Expo Go
    if (isExpoGo) {
      console.log('🔔 Push notifications disabled in Expo Go. Use a development build for full functionality.');
      console.log('📱 Local notifications will still work for testing purposes.');
      return;
    }

    try {
      if (Platform.OS === 'android') {
        await Notifications.setNotificationChannelAsync('default', {
          name: 'default',
          importance: Notifications.AndroidImportance.MAX,
          vibrationPattern: [0, 250, 250, 250],
          lightColor: '#FF231F7C',
        });

        // Create specific channels for different notification types
        await this.createNotificationChannels();
      }

      // Set up notification categories
      await this.setupNotificationCategories();
    } catch (error) {
      console.error('Failed to initialize notification service:', error);
    }
  }

  // Create notification channels (Android)
  private async createNotificationChannels(): Promise<void> {
    const channels = [
      {
        name: 'booking',
        description: 'Booking related notifications',
        importance: Notifications.AndroidImportance.HIGH,
      },
      {
        name: 'payment',
        description: 'Payment related notifications',
        importance: Notifications.AndroidImportance.HIGH,
      },
      {
        name: 'reminder',
        description: 'Reminder notifications',
        importance: Notifications.AndroidImportance.DEFAULT,
      },
      {
        name: 'promotional',
        description: 'Promotional notifications',
        importance: Notifications.AndroidImportance.LOW,
      },
    ];

    for (const channel of channels) {
      await Notifications.setNotificationChannelAsync(channel.name, {
        name: channel.description,
        importance: channel.importance,
        vibrationPattern: [0, 250, 250, 250],
        lightColor: '#FF231F7C',
      });
    }
  }

  // Setup notification categories with actions
  private async setupNotificationCategories(): Promise<void> {
    await Notifications.setNotificationCategoryAsync('booking', [
      {
        identifier: 'view',
        buttonTitle: 'View Details',
        options: {
          opensAppToForeground: true,
        },
      },
      {
        identifier: 'cancel',
        buttonTitle: 'Cancel',
        options: {
          isDestructive: true,
        },
      },
    ]);

    await Notifications.setNotificationCategoryAsync('payment', [
      {
        identifier: 'pay_now',
        buttonTitle: 'Pay Now',
        options: {
          opensAppToForeground: true,
        },
      },
      {
        identifier: 'view_details',
        buttonTitle: 'View Details',
        options: {
          opensAppToForeground: true,
        },
      },
    ]);

    await Notifications.setNotificationCategoryAsync('reminder', [
      {
        identifier: 'dismiss',
        buttonTitle: 'Dismiss',
      },
      {
        identifier: 'snooze',
        buttonTitle: 'Remind Later',
      },
    ]);
  }
  // Register for push notifications
  async registerForPushNotifications(): Promise<PushToken | null> {
    // Skip push notification registration in Expo Go
    if (isExpoGo) {
      console.log('🚫 Push notifications not available in Expo Go. Use a development build for full functionality.');
      console.log('💡 To enable push notifications: npx expo run:android or npx expo run:ios');
      return null;
    }

    if (!Device.isDevice) {
      console.log('Must use physical device for Push Notifications');
      return null;
    }

    const { status: existingStatus } = await Notifications.getPermissionsAsync();
    let finalStatus = existingStatus;

    if (existingStatus !== 'granted') {
      const { status } = await Notifications.requestPermissionsAsync();
      finalStatus = status;
    }

    if (finalStatus !== 'granted') {
      console.log('Failed to get push token for push notification!');
      return null;
    }

    try {
      // Get project ID from multiple sources
      const projectId = Constants.expoConfig?.extra?.eas?.projectId || 
                       Constants.easConfig?.projectId ||
                       process.env.EXPO_PUBLIC_EAS_PROJECT_ID;

      if (!projectId) {
        console.warn('No projectId found. Push notifications will use local notifications only.');
        console.log('To enable push notifications, add your EAS project ID to app.json');
        return null;
      }

      const token = await Notifications.getExpoPushTokenAsync({
        projectId,
      });

      this.pushToken = token.data;
      
      return {
        token: token.data,
        platform: Platform.OS as 'ios' | 'android',
      };
    } catch (error) {
      console.error('Error getting push token:', error);
      // For development, we can still use local notifications
      console.log('Falling back to local notifications only');
      return null;
    }
  }

  // Get current push token
  getPushToken(): string | null {
    return this.pushToken;
  }

  // Schedule local notification
  async scheduleLocalNotification(
    notificationData: NotificationData,
    triggerOptions?: any
  ): Promise<string> {
    const { title, body, data, categoryId } = notificationData;

    const trigger = triggerOptions || null;

    return await Notifications.scheduleNotificationAsync({
      content: {
        title,
        body,
        data,
        categoryIdentifier: categoryId,
        sound: true,
      },
      trigger,
    });
  }

  // Send immediate local notification
  async sendLocalNotification(notificationData: NotificationData): Promise<void> {
    await this.scheduleLocalNotification(notificationData);
  }

  // Cancel notification
  async cancelNotification(notificationId: string): Promise<void> {
    await Notifications.cancelScheduledNotificationAsync(notificationId);
  }

  // Cancel all scheduled notifications
  async cancelAllNotifications(): Promise<void> {
    await Notifications.cancelAllScheduledNotificationsAsync();
  }

  // Get scheduled notifications
  async getScheduledNotifications(): Promise<any[]> {
    return await Notifications.getAllScheduledNotificationsAsync();
  }

  // Handle notification response
  addNotificationResponseReceivedListener(
    listener: (response: any) => void
  ): any {
    return Notifications.addNotificationResponseReceivedListener(listener);
  }

  // Handle notification received while app is in foreground
  addNotificationReceivedListener(
    listener: (notification: any) => void
  ): any {
    return Notifications.addNotificationReceivedListener(listener);
  }

  // Hotel-specific notification helpers
  async sendBookingConfirmation(reservationId: string, roomNumber: string, checkInDate: string): Promise<void> {
    await this.sendLocalNotification({
      title: 'Booking Confirmed! 🎉',
      body: `Your reservation for Room ${roomNumber} has been confirmed. Check-in: ${checkInDate}`,
      data: {
        type: 'booking_confirmation',
        reservationId,
        roomNumber,
        checkInDate,
      },
      categoryId: 'booking',
    });
  }

  async sendPaymentReminder(reservationId: string, amount: number): Promise<void> {
    const { formatPrice } = await import('../utils/currency');
    await this.sendLocalNotification({
      title: 'Payment Pending 💳',
      body: `Complete your payment of ${formatPrice(amount)} to confirm your booking.`,
      data: {
        type: 'payment_reminder',
        reservationId,
        amount,
      },
      categoryId: 'payment',
    });
  }

  async sendCheckInReminder(roomNumber: string): Promise<void> {
    await this.sendLocalNotification({
      title: 'Check-in Available ✨',
      body: `Your room ${roomNumber} is ready for check-in!`,
      data: {
        type: 'checkin_reminder',
        roomNumber,
      },
      categoryId: 'reminder',
    });
  }

  async sendCheckOutReminder(roomNumber: string): Promise<void> {
    await this.sendLocalNotification({
      title: 'Check-out Reminder 🛍️',
      body: `Don't forget to check out of Room ${roomNumber} by 11:00 AM.`,
      data: {
        type: 'checkout_reminder',
        roomNumber,
      },
      categoryId: 'reminder',
    });
  }

  async sendPromotionalNotification(title: string, message: string): Promise<void> {
    await this.sendLocalNotification({
      title,
      body: message,
      data: {
        type: 'promotional',
      },
      categoryId: 'promotional',
    });
  }

  // Schedule check-in reminder (day before)
  async scheduleCheckInReminder(roomNumber: string, checkInDate: Date): Promise<string> {
    const reminderDate = new Date(checkInDate);
    reminderDate.setDate(reminderDate.getDate() - 1);
    reminderDate.setHours(18, 0, 0, 0); // 6 PM day before

    return await this.scheduleLocalNotification(
      {
        title: 'Check-in Tomorrow! 🏨',
        body: `Your room ${roomNumber} will be ready for check-in tomorrow.`,
        data: {
          type: 'checkin_reminder',
          roomNumber,
        },
        categoryId: 'reminder',
      },
      {
        date: reminderDate,
      }
    );
  }

  // Schedule check-out reminder (morning of checkout)
  async scheduleCheckOutReminder(roomNumber: string, checkOutDate: Date): Promise<string> {
    const reminderDate = new Date(checkOutDate);
    reminderDate.setHours(9, 0, 0, 0); // 9 AM on checkout day

    return await this.scheduleLocalNotification(
      {
        title: 'Check-out Today 🛄',
        body: `Remember to check out of Room ${roomNumber} by 11:00 AM.`,
        data: {
          type: 'checkout_reminder',
          roomNumber,
        },
        categoryId: 'reminder',
      },
      {
        date: reminderDate,
      }
    );
  }

  // Admin notifications
  async sendNewBookingAlert(guestName: string, roomNumber: string): Promise<void> {
    await this.sendLocalNotification({
      title: 'New Booking Alert 📋',
      body: `${guestName} has booked Room ${roomNumber}.`,
      data: {
        type: 'admin_new_booking',
        guestName,
        roomNumber,
      },
      categoryId: 'booking',
    });
  }

  async sendMaintenanceAlert(roomNumber: string, issue: string): Promise<void> {
    await this.sendLocalNotification({
      title: 'Maintenance Required 🔧',
      body: `Room ${roomNumber}: ${issue}`,
      data: {
        type: 'admin_maintenance',
        roomNumber,
        issue,
      },
      categoryId: 'reminder',
    });
  }

  async sendLowOccupancyAlert(occupancyRate: number): Promise<void> {
    await this.sendLocalNotification({
      title: 'Low Occupancy Alert 📊',
      body: `Current occupancy is ${occupancyRate}%. Consider promotional offers.`,
      data: {
        type: 'admin_low_occupancy',
        occupancyRate,
      },
      categoryId: 'reminder',
    });
  }
}

export const notificationService = new NotificationService();
