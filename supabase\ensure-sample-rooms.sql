-- ============================================================================
-- ENSURE SAMPLE ROOMS EXIST
-- This script ensures there are sample rooms in the database for testing
-- ============================================================================

-- Check if rooms exist
DO $$
DECLARE
    room_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO room_count FROM public.rooms;
    
    IF room_count = 0 THEN
        RAISE NOTICE 'No rooms found. Adding sample rooms...';
        
        -- Insert sample rooms with all required fields
        INSERT INTO public.rooms (
            room_number, 
            room_type, 
            price_per_night, 
            description, 
            amenities, 
            max_occupancy, 
            bed_type,
            size_sqm,
            status,
            is_available,
            images
        ) VALUES
        -- Standard Rooms
        ('101', 'standard', 8500.00, 
         'Comfortable standard room with city view. Perfect for business travelers and short stays.',
         ARRAY['WiFi', 'Air Conditioning', 'TV', 'Safe'], 
         2, 'Double Bed', 25.0, 'available', true,
         ARRAY['https://images.unsplash.com/photo-1566665797739-1674de7a421a?w=800&q=80']),
        
        ('102', 'standard', 8500.00, 
         'Cozy standard room with modern amenities and comfortable bedding.',
         ARRAY['WiFi', 'Air Conditioning', 'TV', 'Safe', 'Mini Bar'], 
         2, 'Double Bed', 25.0, 'available', true,
         ARRAY['https://images.unsplash.com/photo-1582719478250-c89cae4dc85b?w=800&q=80']),
        
        ('103', 'standard', 8500.00, 
         'Well-appointed standard room with all essential amenities.',
         ARRAY['WiFi', 'Air Conditioning', 'TV', 'Safe'], 
         2, 'Double Bed', 25.0, 'available', true,
         ARRAY['https://images.unsplash.com/photo-1566665797739-1674de7a421a?w=800&q=80']),
        
        -- Deluxe Rooms
        ('201', 'deluxe', 12500.00, 
         'Spacious deluxe room with premium furnishings and enhanced amenities.',
         ARRAY['WiFi', 'Air Conditioning', 'TV', 'Mini Bar', 'Room Service', 'Balcony'], 
         3, 'Queen Bed', 35.0, 'available', true,
         ARRAY['https://images.unsplash.com/photo-1590490360182-c33d57733427?w=800&q=80']),
        
        ('202', 'deluxe', 12500.00, 
         'Elegant deluxe room featuring a work area and upgraded bathroom facilities.',
         ARRAY['WiFi', 'Air Conditioning', 'TV', 'Mini Bar', 'Room Service', 'Workspace', 'Safe'], 
         3, 'Queen Bed', 35.0, 'available', true,
         ARRAY['https://images.unsplash.com/photo-1566195992011-5f6b21e539aa?w=800&q=80']),
        
        ('203', 'deluxe', 12500.00, 
         'Premium deluxe room with stunning views and luxury amenities.',
         ARRAY['WiFi', 'Air Conditioning', 'TV', 'Mini Bar', 'Room Service', 'City View', 'Safe'], 
         3, 'Queen Bed', 35.0, 'maintenance', false,
         ARRAY['https://images.unsplash.com/photo-1560185007-c5ca9d2c014d?w=800&q=80']),
        
        -- Suite Rooms
        ('301', 'suite', 22000.00, 
         'Luxurious suite with separate living area and stunning ocean views.',
         ARRAY['WiFi', 'Air Conditioning', 'TV', 'Mini Bar', 'Room Service', 'Ocean View', 'Balcony', 'Kitchen', 'Safe'], 
         4, 'King Bed', 50.0, 'available', true,
         ARRAY['https://images.unsplash.com/photo-1578683010236-d716f9a3f461?w=800&q=80']),
        
        ('302', 'suite', 22000.00, 
         'Executive suite with premium amenities and panoramic city views.',
         ARRAY['WiFi', 'Air Conditioning', 'TV', 'Mini Bar', 'Room Service', 'City View', 'Workspace', 'Kitchen', 'Safe'], 
         4, 'King Bed', 50.0, 'available', true,
         ARRAY['https://images.unsplash.com/photo-1611892440504-42a792e24d32?w=800&q=80']),
        
        -- Presidential Suite
        ('401', 'presidential', 45000.00, 
         'The ultimate luxury experience with exclusive amenities and personalized service.',
         ARRAY['WiFi', 'Air Conditioning', 'TV', 'Mini Bar', 'Room Service', 'Ocean View', 'Balcony', 'Kitchen', 'Jacuzzi', 'Butler Service', 'Safe'], 
         6, 'King Bed', 80.0, 'available', true,
         ARRAY['https://images.unsplash.com/photo-1591088398332-8a7791972843?w=800&q=80', 'https://images.unsplash.com/photo-1540518614846-7eded433c457?w=800&q=80']);
        
        RAISE NOTICE 'Sample rooms added successfully!';
    ELSE
        RAISE NOTICE 'Found % existing rooms. No need to add sample data.', room_count;
    END IF;
END $$;

-- Show current room status
SELECT 
    'Current Rooms in Database' as info,
    COUNT(*) as total_rooms,
    COUNT(*) FILTER (WHERE status = 'available') as available_rooms,
    COUNT(*) FILTER (WHERE status = 'maintenance') as maintenance_rooms,
    COUNT(*) FILTER (WHERE status = 'cleaning') as cleaning_rooms
FROM public.rooms;

-- Show sample of rooms
SELECT 
    'Sample Room Data' as info,
    room_number,
    room_type,
    bed_type,
    size_sqm,
    price_per_night,
    max_occupancy,
    status,
    is_available,
    array_length(amenities, 1) as amenity_count,
    array_length(images, 1) as image_count
FROM public.rooms
ORDER BY room_number
LIMIT 5;

-- Instructions
DO $$
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '✅ SAMPLE ROOMS ENSURED!';
    RAISE NOTICE '';
    RAISE NOTICE '📋 WHAT WAS DONE:';
    RAISE NOTICE '- Checked for existing rooms';
    RAISE NOTICE '- Added sample rooms if none existed';
    RAISE NOTICE '- All rooms have complete data including bed_type and size_sqm';
    RAISE NOTICE '';
    RAISE NOTICE '🔄 NEXT STEPS:';
    RAISE NOTICE '1. Run the fix-rooms-schema.sql script if you haven''t already';
    RAISE NOTICE '2. Refresh the admin rooms screen in your app';
    RAISE NOTICE '3. You should now see all rooms displayed correctly';
END;
$$;
