import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import {
  Card,
  Title,
  List,
  Switch,
  Divider,
  Button,
  TextInput,
  Dialog,
  Portal,
  Paragraph,
  RadioButton,
} from 'react-native-paper';
import { MaterialIcons } from '@expo/vector-icons';
import { colors, spacing, typography } from '../../constants';
import { useAuthStore } from '../../store/authStore';

interface NotificationSettings {
  newBookings: boolean;
  cancellations: boolean;
  checkIns: boolean;
  checkOuts: boolean;
  lowOccupancy: boolean;
  maintenance: boolean;
  reviews: boolean;
  payments: boolean;
}

interface BusinessSettings {
  hotelName: string;
  address: string;
  phone: string;
  email: string;
  checkInTime: string;
  checkOutTime: string;
  cancellationPolicy: string;
  currency: string;
  timezone: string;
}

export const AdminSettingsScreen = ({ navigation }: any) => {
  const { user, signOut } = useAuthStore();
  
  const [notificationSettings, setNotificationSettings] = useState<NotificationSettings>({
    newBookings: true,
    cancellations: true,
    checkIns: true,
    checkOuts: true,
    lowOccupancy: false,
    maintenance: true,
    reviews: true,
    payments: true,
  });

  const [businessSettings, setBusinessSettings] = useState<BusinessSettings>({
    hotelName: 'Sunset View Hotel',
    address: '123 Ocean Drive, Nairobi, Kenya',
    phone: '+254 123 456 789',
    email: '<EMAIL>',
    checkInTime: '15:00',
    checkOutTime: '11:00',
    cancellationPolicy: '24_hours',
    currency: 'KES',
    timezone: 'Africa/Nairobi',
  });

  const [businessDialogVisible, setBusinessDialogVisible] = useState(false);
  const [logoutDialogVisible, setLogoutDialogVisible] = useState(false);

  const handleNotificationToggle = (key: keyof NotificationSettings) => {
    setNotificationSettings(prev => ({
      ...prev,
      [key]: !prev[key],
    }));
    // Here you would typically save to backend
  };

  const handleBusinessSettingsChange = (key: keyof BusinessSettings, value: string) => {
    setBusinessSettings(prev => ({
      ...prev,
      [key]: value,
    }));
  };

  const saveBusinessSettings = () => {
    // Here you would typically save to backend
    setBusinessDialogVisible(false);
    Alert.alert('Success', 'Business settings updated successfully');
  };

  const handleLogout = async () => {
    try {
      await signOut();
      setLogoutDialogVisible(false);
      // Navigation will be handled by auth state change
    } catch (error) {
      Alert.alert('Error', 'Failed to logout');
    }
  };

  const handleBackup = () => {
    Alert.alert(
      'Backup Data',
      'This will create a backup of all your hotel data.',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Backup', onPress: () => Alert.alert('Success', 'Backup created successfully') },
      ]
    );
  };

  const handleRestore = () => {
    Alert.alert(
      'Restore Data',
      'This will restore data from a backup file. This action cannot be undone.',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Restore', onPress: () => Alert.alert('Success', 'Data restored successfully') },
      ]
    );
  };

  const handleClearCache = () => {
    Alert.alert(
      'Clear Cache',
      'This will clear all cached data. The app may run slower until data is reloaded.',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Clear', onPress: () => Alert.alert('Success', 'Cache cleared successfully') },
      ]
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView}>
        {/* Profile Section */}
        <Card style={styles.card}>
          <Card.Content>
            <Title style={styles.sectionTitle}>Profile</Title>
            <List.Item
              title={`${user?.first_name} ${user?.last_name}`}
              description={user?.email}
              left={() => <MaterialIcons name="person" size={24} color={colors.primary} />}
              right={() => <MaterialIcons name="chevron-right" size={24} color={colors.onSurfaceVariant} />}
              onPress={() => navigation.navigate('Profile')}
            />
          </Card.Content>
        </Card>

        {/* Business Settings */}
        <Card style={styles.card}>
          <Card.Content>
            <Title style={styles.sectionTitle}>Business Settings</Title>
            <List.Item
              title="Hotel Information"
              description="Name, address, contact details"
              left={() => <MaterialIcons name="business" size={24} color={colors.primary} />}
              right={() => <MaterialIcons name="chevron-right" size={24} color={colors.onSurfaceVariant} />}
              onPress={() => setBusinessDialogVisible(true)}
            />
            <Divider />
            <List.Item
              title="Check-in/Check-out Times"
              description={`Check-in: ${businessSettings.checkInTime}, Check-out: ${businessSettings.checkOutTime}`}
              left={() => <MaterialIcons name="schedule" size={24} color={colors.primary} />}
              right={() => <MaterialIcons name="chevron-right" size={24} color={colors.onSurfaceVariant} />}
            />
            <Divider />
            <List.Item
              title="Cancellation Policy"
              description="24 hours before check-in"
              left={() => <MaterialIcons name="policy" size={24} color={colors.primary} />}
              right={() => <MaterialIcons name="chevron-right" size={24} color={colors.onSurfaceVariant} />}
            />
          </Card.Content>
        </Card>

        {/* Notification Settings */}
        <Card style={styles.card}>
          <Card.Content>
            <Title style={styles.sectionTitle}>Notifications</Title>
            
            <List.Item
              title="New Bookings"
              description="Get notified when new reservations are made"
              left={() => <MaterialIcons name="event" size={24} color={colors.primary} />}
              right={() => (
                <Switch
                  value={notificationSettings.newBookings}
                  onValueChange={() => handleNotificationToggle('newBookings')}
                />
              )}
            />
            <Divider />

            <List.Item
              title="Cancellations"
              description="Get notified when bookings are cancelled"
              left={() => <MaterialIcons name="cancel" size={24} color={colors.error} />}
              right={() => (
                <Switch
                  value={notificationSettings.cancellations}
                  onValueChange={() => handleNotificationToggle('cancellations')}
                />
              )}
            />
            <Divider />

            <List.Item
              title="Check-ins"
              description="Get notified when guests check in"
              left={() => <MaterialIcons name="login" size={24} color={colors.success} />}
              right={() => (
                <Switch
                  value={notificationSettings.checkIns}
                  onValueChange={() => handleNotificationToggle('checkIns')}
                />
              )}
            />
            <Divider />

            <List.Item
              title="Check-outs"
              description="Get notified when guests check out"
              left={() => <MaterialIcons name="logout" size={24} color={colors.info} />}
              right={() => (
                <Switch
                  value={notificationSettings.checkOuts}
                  onValueChange={() => handleNotificationToggle('checkOuts')}
                />
              )}
            />
            <Divider />

            <List.Item
              title="Low Occupancy"
              description="Get notified when occupancy is below threshold"
              left={() => <MaterialIcons name="trending-down" size={24} color={colors.warning} />}
              right={() => (
                <Switch
                  value={notificationSettings.lowOccupancy}
                  onValueChange={() => handleNotificationToggle('lowOccupancy')}
                />
              )}
            />
            <Divider />

            <List.Item
              title="Maintenance Alerts"
              description="Get notified about room maintenance"
              left={() => <MaterialIcons name="build" size={24} color={colors.warning} />}
              right={() => (
                <Switch
                  value={notificationSettings.maintenance}
                  onValueChange={() => handleNotificationToggle('maintenance')}
                />
              )}
            />
            <Divider />

            <List.Item
              title="Reviews"
              description="Get notified about new guest reviews"
              left={() => <MaterialIcons name="star" size={24} color={colors.primary} />}
              right={() => (
                <Switch
                  value={notificationSettings.reviews}
                  onValueChange={() => handleNotificationToggle('reviews')}
                />
              )}
            />
            <Divider />

            <List.Item
              title="Payment Alerts"
              description="Get notified about payment updates"
              left={() => <MaterialIcons name="payment" size={24} color={colors.success} />}
              right={() => (
                <Switch
                  value={notificationSettings.payments}
                  onValueChange={() => handleNotificationToggle('payments')}
                />
              )}
            />
          </Card.Content>
        </Card>

        {/* Data Management */}
        <Card style={styles.card}>
          <Card.Content>
            <Title style={styles.sectionTitle}>Data Management</Title>
            
            <List.Item
              title="Backup Data"
              description="Create a backup of your hotel data"
              left={() => <MaterialIcons name="backup" size={24} color={colors.primary} />}
              right={() => <MaterialIcons name="chevron-right" size={24} color={colors.onSurfaceVariant} />}
              onPress={handleBackup}
            />
            <Divider />

            <List.Item
              title="Restore Data"
              description="Restore data from a backup file"
              left={() => <MaterialIcons name="restore" size={24} color={colors.warning} />}
              right={() => <MaterialIcons name="chevron-right" size={24} color={colors.onSurfaceVariant} />}
              onPress={handleRestore}
            />
            <Divider />

            <List.Item
              title="Clear Cache"
              description="Clear cached data to free up space"
              left={() => <MaterialIcons name="clear" size={24} color={colors.info} />}
              right={() => <MaterialIcons name="chevron-right" size={24} color={colors.onSurfaceVariant} />}
              onPress={handleClearCache}
            />
          </Card.Content>
        </Card>

        {/* Support */}
        <Card style={styles.card}>
          <Card.Content>
            <Title style={styles.sectionTitle}>Support</Title>
            
            <List.Item
              title="Help Center"
              description="Get help and support"
              left={() => <MaterialIcons name="help" size={24} color={colors.primary} />}
              right={() => <MaterialIcons name="chevron-right" size={24} color={colors.onSurfaceVariant} />}
            />
            <Divider />

            <List.Item
              title="Contact Support"
              description="Get in touch with our support team"
              left={() => <MaterialIcons name="support" size={24} color={colors.primary} />}
              right={() => <MaterialIcons name="chevron-right" size={24} color={colors.onSurfaceVariant} />}
            />
            <Divider />

            <List.Item
              title="Privacy Policy"
              description="Read our privacy policy"
              left={() => <MaterialIcons name="privacy-tip" size={24} color={colors.primary} />}
              right={() => <MaterialIcons name="chevron-right" size={24} color={colors.onSurfaceVariant} />}
            />
            <Divider />

            <List.Item
              title="Terms of Service"
              description="Read our terms of service"
              left={() => <MaterialIcons name="description" size={24} color={colors.primary} />}
              right={() => <MaterialIcons name="chevron-right" size={24} color={colors.onSurfaceVariant} />}
            />
          </Card.Content>
        </Card>

        {/* Account Actions */}
        <Card style={styles.card}>
          <Card.Content>
            <Title style={styles.sectionTitle}>Account</Title>
            
            <List.Item
              title="Logout"
              description="Sign out of your account"
              left={() => <MaterialIcons name="logout" size={24} color={colors.error} />}
              right={() => <MaterialIcons name="chevron-right" size={24} color={colors.onSurfaceVariant} />}
              onPress={() => setLogoutDialogVisible(true)}
            />
          </Card.Content>
        </Card>

        {/* App Info */}
        <Card style={styles.card}>
          <Card.Content>
            <Title style={styles.sectionTitle}>App Information</Title>
            <List.Item
              title="Version"
              description="1.0.0"
              left={() => <MaterialIcons name="info" size={24} color={colors.primary} />}
            />
            <Divider />
            <List.Item
              title="Build"
              description="2024.01.01"
              left={() => <MaterialIcons name="build" size={24} color={colors.primary} />}
            />
          </Card.Content>
        </Card>
      </ScrollView>

      {/* Business Settings Dialog */}
      <Portal>
        <Dialog visible={businessDialogVisible} onDismiss={() => setBusinessDialogVisible(false)}>
          <Dialog.Title>Business Settings</Dialog.Title>
          <Dialog.ScrollArea>
            <ScrollView style={styles.dialogContent}>
              <TextInput
                label="Hotel Name"
                value={businessSettings.hotelName}
                onChangeText={(text) => handleBusinessSettingsChange('hotelName', text)}
                style={styles.dialogInput}
              />
              
              <TextInput
                label="Address"
                value={businessSettings.address}
                onChangeText={(text) => handleBusinessSettingsChange('address', text)}
                multiline
                numberOfLines={3}
                style={styles.dialogInput}
              />
              
              <TextInput
                label="Phone"
                value={businessSettings.phone}
                onChangeText={(text) => handleBusinessSettingsChange('phone', text)}
                style={styles.dialogInput}
              />
              
              <TextInput
                label="Email"
                value={businessSettings.email}
                onChangeText={(text) => handleBusinessSettingsChange('email', text)}
                style={styles.dialogInput}
              />
              
              <TextInput
                label="Check-in Time"
                value={businessSettings.checkInTime}
                onChangeText={(text) => handleBusinessSettingsChange('checkInTime', text)}
                style={styles.dialogInput}
              />
              
              <TextInput
                label="Check-out Time"
                value={businessSettings.checkOutTime}
                onChangeText={(text) => handleBusinessSettingsChange('checkOutTime', text)}
                style={styles.dialogInput}
              />
            </ScrollView>
          </Dialog.ScrollArea>
          <Dialog.Actions>
            <Button onPress={() => setBusinessDialogVisible(false)}>Cancel</Button>
            <Button onPress={saveBusinessSettings}>Save</Button>
          </Dialog.Actions>
        </Dialog>
      </Portal>

      {/* Logout Confirmation Dialog */}
      <Portal>
        <Dialog visible={logoutDialogVisible} onDismiss={() => setLogoutDialogVisible(false)}>
          <Dialog.Title>Logout</Dialog.Title>
          <Dialog.Content>
            <Paragraph>Are you sure you want to logout?</Paragraph>
          </Dialog.Content>
          <Dialog.Actions>
            <Button onPress={() => setLogoutDialogVisible(false)}>Cancel</Button>
            <Button onPress={handleLogout} textColor={colors.error}>Logout</Button>
          </Dialog.Actions>
        </Dialog>
      </Portal>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  scrollView: {
    flex: 1,
  },
  card: {
    margin: spacing.lg,
    marginBottom: spacing.md,
  },
  sectionTitle: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.semibold as any,
    marginBottom: spacing.md,
    color: colors.primary,
  },
  dialogContent: {
    maxHeight: 400,
    paddingHorizontal: spacing.md,
  },
  dialogInput: {
    marginBottom: spacing.md,
  },
});
