-- ============================================================================
-- FIX RESERVATION COLUMNS - ADD MISSING check_in AND check_out COLUMNS
-- This script adds the missing columns that the frontend expects
-- ============================================================================

-- Step 1: Add the missing columns as computed columns (views) or actual columns
-- Option A: Add actual columns that mirror the existing date columns
ALTER TABLE public.reservations 
ADD COLUMN IF NOT EXISTS check_in DATE;

ALTER TABLE public.reservations 
ADD COLUMN IF NOT EXISTS check_out DATE;

-- Step 2: Create a trigger to keep the columns in sync
CREATE OR REPLACE FUNCTION sync_reservation_dates()
RETURNS TRIGGER AS $$
BEGIN
    -- When check_in_date or check_out_date changes, update the corresponding columns
    IF TG_OP = 'INSERT' OR TG_OP = 'UPDATE' THEN
        NEW.check_in = NEW.check_in_date;
        NEW.check_out = NEW.check_out_date;
        
        -- Also sync the other way if someone updates check_in/check_out directly
        IF NEW.check_in IS NOT NULL AND NEW.check_in != OLD.check_in_date THEN
            NEW.check_in_date = NEW.check_in;
        END IF;
        
        IF NEW.check_out IS NOT NULL AND NEW.check_out != OLD.check_out_date THEN
            NEW.check_out_date = NEW.check_out;
        END IF;
        
        RETURN NEW;
    END IF;
    
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Step 3: Create the trigger
DROP TRIGGER IF EXISTS trigger_sync_reservation_dates ON public.reservations;
CREATE TRIGGER trigger_sync_reservation_dates
    BEFORE INSERT OR UPDATE ON public.reservations
    FOR EACH ROW
    EXECUTE FUNCTION sync_reservation_dates();

-- Step 4: Update existing reservations to populate the new columns
UPDATE public.reservations 
SET 
    check_in = check_in_date,
    check_out = check_out_date
WHERE check_in IS NULL OR check_out IS NULL;

-- Step 5: Add constraints to ensure data consistency
ALTER TABLE public.reservations 
ADD CONSTRAINT check_in_consistency 
CHECK (check_in = check_in_date);

ALTER TABLE public.reservations 
ADD CONSTRAINT check_out_consistency 
CHECK (check_out = check_out_date);

-- Step 6: Create a view that provides both column naming conventions
CREATE OR REPLACE VIEW reservations_flexible AS
SELECT 
    id,
    guest_id,
    room_id,
    check_in_date,
    check_out_date,
    check_in_date as check_in,
    check_out_date as check_out,
    total_amount,
    special_requests,
    status,
    payment_status,
    payment_id,
    created_at,
    updated_at
FROM public.reservations;

-- Grant permissions on the view
GRANT SELECT ON reservations_flexible TO authenticated;
GRANT SELECT ON reservations_flexible TO anon;
GRANT INSERT ON reservations_flexible TO authenticated;
GRANT UPDATE ON reservations_flexible TO authenticated;
GRANT DELETE ON reservations_flexible TO authenticated;

-- Step 7: Create an INSTEAD OF trigger for the view to handle inserts/updates
CREATE OR REPLACE FUNCTION handle_reservations_flexible_changes()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        -- For inserts, use whichever date columns are provided
        INSERT INTO public.reservations (
            id, guest_id, room_id, check_in_date, check_out_date,
            total_amount, special_requests, status, payment_status, payment_id
        ) VALUES (
            COALESCE(NEW.id, uuid_generate_v4()),
            NEW.guest_id,
            NEW.room_id,
            COALESCE(NEW.check_in_date, NEW.check_in),
            COALESCE(NEW.check_out_date, NEW.check_out),
            NEW.total_amount,
            NEW.special_requests,
            COALESCE(NEW.status, 'pending'),
            COALESCE(NEW.payment_status, 'pending'),
            NEW.payment_id
        );
        RETURN NEW;
    ELSIF TG_OP = 'UPDATE' THEN
        UPDATE public.reservations SET
            guest_id = NEW.guest_id,
            room_id = NEW.room_id,
            check_in_date = COALESCE(NEW.check_in_date, NEW.check_in),
            check_out_date = COALESCE(NEW.check_out_date, NEW.check_out),
            total_amount = NEW.total_amount,
            special_requests = NEW.special_requests,
            status = NEW.status,
            payment_status = NEW.payment_status,
            payment_id = NEW.payment_id,
            updated_at = NOW()
        WHERE id = NEW.id;
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        DELETE FROM public.reservations WHERE id = OLD.id;
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Create the INSTEAD OF triggers for the view
DROP TRIGGER IF EXISTS trigger_reservations_flexible_insert ON reservations_flexible;
CREATE TRIGGER trigger_reservations_flexible_insert
    INSTEAD OF INSERT ON reservations_flexible
    FOR EACH ROW
    EXECUTE FUNCTION handle_reservations_flexible_changes();

DROP TRIGGER IF EXISTS trigger_reservations_flexible_update ON reservations_flexible;
CREATE TRIGGER trigger_reservations_flexible_update
    INSTEAD OF UPDATE ON reservations_flexible
    FOR EACH ROW
    EXECUTE FUNCTION handle_reservations_flexible_changes();

DROP TRIGGER IF EXISTS trigger_reservations_flexible_delete ON reservations_flexible;
CREATE TRIGGER trigger_reservations_flexible_delete
    INSTEAD OF DELETE ON reservations_flexible
    FOR EACH ROW
    EXECUTE FUNCTION handle_reservations_flexible_changes();

-- Step 8: Test the setup
DO $$
BEGIN
    RAISE NOTICE '🎉 RESERVATION COLUMNS FIX COMPLETED!';
    RAISE NOTICE '';
    RAISE NOTICE '✅ What was done:';
    RAISE NOTICE '- Added check_in and check_out columns to reservations table';
    RAISE NOTICE '- Created triggers to keep date columns in sync';
    RAISE NOTICE '- Created flexible view that accepts both naming conventions';
    RAISE NOTICE '- Added INSTEAD OF triggers for seamless data operations';
    RAISE NOTICE '';
    RAISE NOTICE '🚀 How to use in your app:';
    RAISE NOTICE '1. Your existing code will now work without changes';
    RAISE NOTICE '2. You can use either check_in/check_out OR check_in_date/check_out_date';
    RAISE NOTICE '3. Both column sets will stay synchronized automatically';
    RAISE NOTICE '';
    RAISE NOTICE '🔧 The missing check_in column issue is now FIXED!';
END;
$$;

-- Step 9: Show current reservation structure
SELECT 
    'Reservation Columns Fix Complete!' as status,
    COUNT(*) as total_reservations,
    COUNT(*) FILTER (WHERE check_in IS NOT NULL) as reservations_with_check_in,
    COUNT(*) FILTER (WHERE check_out IS NOT NULL) as reservations_with_check_out
FROM public.reservations;

-- Show sample of the new structure
SELECT 
    id,
    check_in_date,
    check_out_date,
    check_in,
    check_out,
    status
FROM public.reservations 
LIMIT 3;
