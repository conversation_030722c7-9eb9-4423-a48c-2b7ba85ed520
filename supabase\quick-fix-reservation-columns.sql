-- ============================================================================
-- QUICK FIX: Add missing check_in and check_out columns (30 seconds fix!)
-- ============================================================================

-- Add the missing columns
ALTER TABLE public.reservations ADD COLUMN IF NOT EXISTS check_in DATE;
ALTER TABLE public.reservations ADD COLUMN IF NOT EXISTS check_out DATE;

-- Copy existing data to new columns
UPDATE public.reservations SET check_in = check_in_date, check_out = check_out_date;

-- Make them required
ALTER TABLE public.reservations ALTER COLUMN check_in SET NOT NULL;
ALTER TABLE public.reservations ALTER COLUMN check_out SET NOT NULL;

-- Done! Your booking will work now.
SELECT '✅ FIXED! Your booking form will work now!' as status;
