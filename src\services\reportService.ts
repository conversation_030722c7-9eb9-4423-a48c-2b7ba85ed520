import * as FileSystem from 'expo-file-system';
import * as Sharing from 'expo-sharing';
import * as Print from 'expo-print';
import { Platform } from 'expo-modules-core';
import { formatPrice } from '../utils/currency';
import type { Reservation, Room } from '../types';

export interface ReportData {
  totalRevenue: number;
  totalReservations: number;
  averageBookingValue: number;
  occupancyRate: number;
  cancelationRate: number;
  topRoomTypes: Array<{ type: string; count: number; revenue: number }>;
  monthlyRevenue: Array<{ month: string; revenue: number }>;
  bookingsByStatus: Array<{ status: string; count: number; color: string }>;
  seasonalTrends: Array<{ period: string; bookings: number }>;
  period: 'week' | 'month' | 'year';
  generatedAt: string;
  reservations: Reservation[];
  rooms: Room[];
}

export interface ReportOptions {
  format: 'csv' | 'pdf';
  type: 'summary' | 'detailed' | 'reservations' | 'rooms';
  dateRange?: {
    start: string;
    end: string;
  };
}

export class ReportService {
  private static instance: ReportService;

  static getInstance(): ReportService {
    if (!ReportService.instance) {
      ReportService.instance = new ReportService();
    }
    return ReportService.instance;
  }

  // Generate CSV report
  async generateCSVReport(data: ReportData, options: ReportOptions): Promise<string> {
    console.log('📊 Generating CSV report...', options);

    let csvContent = '';
    const timestamp = new Date().toISOString().split('T')[0];

    switch (options.type) {
      case 'summary':
        csvContent = this.generateSummaryCSV(data);
        break;
      case 'detailed':
        csvContent = this.generateDetailedCSV(data);
        break;
      case 'reservations':
        csvContent = this.generateReservationsCSV(data.reservations);
        break;
      case 'rooms':
        csvContent = this.generateRoomsCSV(data.rooms);
        break;
      default:
        csvContent = this.generateSummaryCSV(data);
    }

    // Save to file
    const fileName = `sunset_view_hotel_${options.type}_report_${timestamp}.csv`;
    const fileUri = `${FileSystem.documentDirectory}${fileName}`;

    await FileSystem.writeAsStringAsync(fileUri, csvContent, {
      encoding: FileSystem.EncodingType.UTF8,
    });

    console.log('✅ CSV report generated:', fileUri);
    return fileUri;
  }

  // Generate PDF report
  async generatePDFReport(data: ReportData, options: ReportOptions): Promise<string> {
    console.log('📊 Generating PDF report...', options);

    try {
      const htmlContent = this.generateHTMLReport(data, options);
      const timestamp = new Date().toISOString().split('T')[0];
      const fileName = `sunset_view_hotel_${options.type}_report_${timestamp}.pdf`;

      console.log('📄 Converting HTML to PDF...');

      // Use expo-print to convert HTML to PDF
      const { uri } = await Print.printToFileAsync({
        html: htmlContent,
        base64: false,
      });

      // Move the generated PDF to a permanent location with our custom filename
      const finalUri = `${FileSystem.documentDirectory}${fileName}`;
      await FileSystem.moveAsync({
        from: uri,
        to: finalUri,
      });

      console.log('✅ PDF report generated successfully:', finalUri);
      return finalUri;
    } catch (error) {
      console.error('❌ Error generating PDF:', error);
      throw new Error(`Failed to generate PDF: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  // Share report file
  async shareReport(fileUri: string): Promise<void> {
    try {
      console.log('📤 Checking if sharing is available...');
      const isAvailable = await Sharing.isAvailableAsync();
      console.log('📤 Sharing available:', isAvailable);

      if (isAvailable) {
        console.log('📤 Sharing file:', fileUri);

        // Determine MIME type based on file extension
        let mimeType = 'application/octet-stream';
        if (fileUri.endsWith('.csv')) {
          mimeType = 'text/csv';
        } else if (fileUri.endsWith('.pdf')) {
          mimeType = 'application/pdf';
        } else if (fileUri.endsWith('.html')) {
          mimeType = 'text/html';
        }

        await Sharing.shareAsync(fileUri, {
          mimeType,
          dialogTitle: 'Share Analytics Report',
        });
        console.log('✅ File shared successfully');
      } else {
        throw new Error('Sharing is not available on this platform');
      }
    } catch (error) {
      console.error('❌ Error sharing report:', error);
      throw error;
    }
  }

  // Generate summary CSV
  private generateSummaryCSV(data: ReportData): string {
    const lines = [
      'Sunset View Hotel - Analytics Summary Report',
      `Generated: ${data.generatedAt}`,
      `Period: ${data.period}`,
      '',
      'Key Metrics',
      'Metric,Value',
      `Total Revenue,${formatPrice(data.totalRevenue)}`,
      `Total Reservations,${data.totalReservations}`,
      `Average Booking Value,${formatPrice(data.averageBookingValue)}`,
      `Occupancy Rate,${data.occupancyRate.toFixed(1)}%`,
      `Cancellation Rate,${data.cancelationRate.toFixed(1)}%`,
      '',
      'Top Room Types by Revenue',
      'Room Type,Bookings,Revenue',
      ...data.topRoomTypes.map(item => 
        `${item.type},${item.count},${formatPrice(item.revenue)}`
      ),
      '',
      'Monthly Revenue',
      'Month,Revenue',
      ...data.monthlyRevenue.map(item => 
        `${item.month},${formatPrice(item.revenue)}`
      ),
      '',
      'Bookings by Status',
      'Status,Count',
      ...data.bookingsByStatus.map(item => 
        `${item.status},${item.count}`
      ),
    ];

    return lines.join('\n');
  }

  // Generate detailed CSV
  private generateDetailedCSV(data: ReportData): string {
    const summary = this.generateSummaryCSV(data);
    
    const seasonalData = [
      '',
      'Seasonal Trends',
      'Period,Bookings',
      ...data.seasonalTrends.map(item => 
        `${item.period},${item.bookings}`
      ),
    ];

    return summary + '\n' + seasonalData.join('\n');
  }

  // Generate reservations CSV
  private generateReservationsCSV(reservations: Reservation[]): string {
    const lines = [
      'Sunset View Hotel - Reservations Report',
      `Generated: ${new Date().toISOString()}`,
      '',
      'Reservation ID,Guest Name,Guest Email,Room Number,Check In,Check Out,Total Amount,Status,Payment Status,Created At',
      ...reservations.map(reservation => 
        `${reservation.id},${reservation.guest_name || 'N/A'},${reservation.guest_email || 'N/A'},${reservation.room_number || 'N/A'},${reservation.check_in_date},${reservation.check_out_date},${formatPrice(reservation.total_amount)},${reservation.status},${reservation.payment_status},${reservation.created_at}`
      ),
    ];

    return lines.join('\n');
  }

  // Generate rooms CSV
  private generateRoomsCSV(rooms: Room[]): string {
    const lines = [
      'Sunset View Hotel - Rooms Report',
      `Generated: ${new Date().toISOString()}`,
      '',
      'Room ID,Room Number,Room Type,Price per Night,Max Occupancy,Status,Bed Type,Size (sqm),Amenities Count',
      ...rooms.map(room => 
        `${room.id},${room.room_number},${room.room_type},${formatPrice(room.price_per_night)},${room.max_occupancy},${room.status},${room.bed_type || 'N/A'},${room.size_sqm || 'N/A'},${room.amenities?.length || 0}`
      ),
    ];

    return lines.join('\n');
  }

  // Generate HTML report for PDF conversion
  private generateHTMLReport(data: ReportData, options: ReportOptions): string {
    const styles = `
      <style>
        body { font-family: Arial, sans-serif; margin: 20px; color: #333; }
        .header { text-align: center; border-bottom: 2px solid #2E8B57; padding-bottom: 20px; margin-bottom: 30px; }
        .logo { color: #2E8B57; font-size: 24px; font-weight: bold; }
        .subtitle { color: #666; margin-top: 10px; }
        .metrics-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 30px 0; }
        .metric-card { border: 1px solid #ddd; border-radius: 8px; padding: 20px; text-align: center; }
        .metric-value { font-size: 24px; font-weight: bold; color: #2E8B57; }
        .metric-label { color: #666; margin-top: 5px; }
        .section { margin: 30px 0; }
        .section-title { font-size: 18px; font-weight: bold; color: #2E8B57; border-bottom: 1px solid #ddd; padding-bottom: 10px; }
        table { width: 100%; border-collapse: collapse; margin-top: 15px; }
        th, td { border: 1px solid #ddd; padding: 12px; text-align: left; }
        th { background-color: #f8f9fa; font-weight: bold; }
        .footer { margin-top: 50px; text-align: center; color: #666; font-size: 12px; }
      </style>
    `;

    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="UTF-8">
        <title>Sunset View Hotel - Analytics Report</title>
        ${styles}
      </head>
      <body>
        <div class="header">
          <div class="logo">🌅 Sunset View Hotel</div>
          <div class="subtitle">Analytics Report - ${options.type.toUpperCase()}</div>
          <div class="subtitle">Generated: ${data.generatedAt} | Period: ${data.period}</div>
        </div>

        <div class="metrics-grid">
          <div class="metric-card">
            <div class="metric-value">${formatPrice(data.totalRevenue)}</div>
            <div class="metric-label">Total Revenue</div>
          </div>
          <div class="metric-card">
            <div class="metric-value">${data.totalReservations}</div>
            <div class="metric-label">Total Bookings</div>
          </div>
          <div class="metric-card">
            <div class="metric-value">${formatPrice(data.averageBookingValue)}</div>
            <div class="metric-label">Avg. Booking Value</div>
          </div>
          <div class="metric-card">
            <div class="metric-value">${data.occupancyRate.toFixed(1)}%</div>
            <div class="metric-label">Occupancy Rate</div>
          </div>
        </div>

        <div class="section">
          <div class="section-title">Top Room Types by Revenue</div>
          <table>
            <thead>
              <tr><th>Room Type</th><th>Bookings</th><th>Revenue</th></tr>
            </thead>
            <tbody>
              ${data.topRoomTypes.map(item => 
                `<tr><td>${item.type}</td><td>${item.count}</td><td>${formatPrice(item.revenue)}</td></tr>`
              ).join('')}
            </tbody>
          </table>
        </div>

        <div class="section">
          <div class="section-title">Bookings by Status</div>
          <table>
            <thead>
              <tr><th>Status</th><th>Count</th></tr>
            </thead>
            <tbody>
              ${data.bookingsByStatus.map(item => 
                `<tr><td>${item.status}</td><td>${item.count}</td></tr>`
              ).join('')}
            </tbody>
          </table>
        </div>

        <div class="footer">
          <p>This report was generated automatically by Sunset View Hotel Management System</p>
          <p>For questions or support, contact: <EMAIL></p>
        </div>
      </body>
      </html>
    `;
  }
}

export const reportService = ReportService.getInstance();
