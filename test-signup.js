// Test script to verify signup functionality
// Run this in your browser console or as a Node.js script

const testSignup = async () => {
  try {
    console.log('Testing signup functionality...');
    
    // Test data
    const testUser = {
      email: '<EMAIL>',
      password: 'testpassword123',
      full_name: 'Test User',
      phone: '+1234567890'
    };
    
    console.log('Test user data:', testUser);
    
    // This would be your actual signup call
    // const result = await authService.signUp(testUser.email, testUser.password, {
    //   full_name: testUser.full_name,
    //   phone: testUser.phone
    // });
    
    console.log('Signup test completed. Check the actual app for results.');
    
  } catch (error) {
    console.error('Test error:', error);
  }
};

// Uncomment to run the test
// testSignup();
