-- ============================================================================
-- SIMPLE FIX: ADD check_in AND check_out COLUMNS TO RESERVATIONS TABLE
-- This is a simple script that just adds the missing columns
-- ============================================================================

-- Add the missing columns
ALTER TABLE public.reservations 
ADD COLUMN IF NOT EXISTS check_in DATE;

ALTER TABLE public.reservations 
ADD COLUMN IF NOT EXISTS check_out DATE;

-- Populate the new columns with existing data
UPDATE public.reservations 
SET 
    check_in = check_in_date,
    check_out = check_out_date
WHERE check_in IS NULL OR check_out IS NULL;

-- Add NOT NULL constraints after populating data
ALTER TABLE public.reservations 
ALTER COLUMN check_in SET NOT NULL;

ALTER TABLE public.reservations 
ALTER COLUMN check_out SET NOT NULL;

-- Add check constraints to ensure data consistency
ALTER TABLE public.reservations 
ADD CONSTRAINT valid_check_in_out_dates 
CHECK (check_out > check_in);

-- Update the existing date constraint to include new columns
ALTER TABLE public.reservations 
DROP CONSTRAINT IF EXISTS valid_dates;

ALTER TABLE public.reservations 
ADD CONSTRAINT valid_dates 
CHECK (check_out_date > check_in_date AND check_out > check_in);

-- Create indexes for the new columns
CREATE INDEX IF NOT EXISTS idx_reservations_check_in_out 
ON public.reservations(check_in, check_out);

-- Show the results
SELECT 
    'Simple Check-in Columns Fix Complete!' as status,
    COUNT(*) as total_reservations,
    COUNT(*) FILTER (WHERE check_in IS NOT NULL) as reservations_with_check_in,
    COUNT(*) FILTER (WHERE check_out IS NOT NULL) as reservations_with_check_out
FROM public.reservations;

-- Display the updated table structure
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'reservations' 
    AND table_schema = 'public'
    AND column_name IN ('check_in_date', 'check_out_date', 'check_in', 'check_out')
ORDER BY ordinal_position;

-- Final success message
DO $$
BEGIN
    RAISE NOTICE '🎉 SIMPLE CHECK-IN COLUMNS FIX COMPLETED!';
    RAISE NOTICE '';
    RAISE NOTICE '✅ Added columns:';
    RAISE NOTICE '- check_in (DATE, NOT NULL)';
    RAISE NOTICE '- check_out (DATE, NOT NULL)';
    RAISE NOTICE '';
    RAISE NOTICE '🚀 Your booking form should now work!';
    RAISE NOTICE 'The reservations table now has both:';
    RAISE NOTICE '- check_in_date & check_out_date (original columns)';
    RAISE NOTICE '- check_in & check_out (new columns for frontend compatibility)';
    RAISE NOTICE '';
    RAISE NOTICE '⚠️  IMPORTANT: You may need to update your frontend code to use';
    RAISE NOTICE 'either the new columns OR modify it to use the original column names.';
END;
$$;
