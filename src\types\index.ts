// Core type definitions for the Sunset View Hotel Reservation App

// Re-export all types from database
export * from './database';

// Re-export navigation types
export * from './navigation';

export interface User {
  id: string;
  email: string;
  full_name: string;
  phone?: string;
  role: UserRole;
  created_at: string;
  updated_at: string;
}

export type UserRole = 'guest' | 'receptionist' | 'admin';

// Room image interface for enhanced image handling
export interface RoomImage {
  id: string;
  url: string;
  alt_text?: string;
  upload_date?: string;
  file_name?: string;
  file_size?: number;
}

export interface Room {
  id: string;
  room_number: string;
  room_type: RoomType;
  type?: RoomType; // Frontend compatibility field (mapped from room_type)
  price_per_night: number;
  description: string;
  amenities: string[];
  max_occupancy: number;
  bed_type?: string;
  size_sqm?: number;
  images: RoomImage[];
  status: RoomStatus;
  is_available?: boolean; // Frontend compatibility field
  created_at: string;
  updated_at: string;
}

export type RoomType = 'standard' | 'deluxe' | 'suite' | 'presidential';
export type RoomStatus = 'available' | 'booked' | 'maintenance' | 'cleaning';

export interface Reservation {
  id: string;
  guest_id: string;
  room_id: string;
  check_in_date: string;
  check_out_date: string;
  // Legacy aliases for backward compatibility
  check_in: string;
  check_out: string;
  total_amount: number;
  guests: number;
  room_number: string;
  special_requests?: string;
  status: ReservationStatus;
  payment_status: PaymentStatus;
  payment_id?: string;
  created_at: string;
  updated_at: string;
  // Flattened guest fields for compatibility
  guest_name?: string;
  guest_email?: string;
  guest_phone?: string;
  // Relations
  guest?: User;
  room?: Room;
  payment?: Payment;
}

export type ReservationStatus = 'pending' | 'confirmed' | 'checked_in' | 'checked_out' | 'cancelled';
export type PaymentStatus = 'pending' | 'paid' | 'failed' | 'refunded';

export interface ReservationWithDetails extends Reservation {
  payment: {
    id: string;
    reservation_id: string;
    amount: number;
    currency: string;
    payment_method: PaymentMethod;
    paystack_reference: string | null;
    paystack_transaction_id: string | null;
    status: PaymentStatus;
    paid_at: string | null;
    created_at: string;
    updated_at: string;
  };
}

export interface Payment {
  id: string;
  reservation_id: string;
  amount: number;
  currency: string;
  payment_method: PaymentMethod;
  paystack_reference: string | null;
  paystack_transaction_id: string | null;
  status: PaymentStatus;
  paid_at: string | null;
  created_at: string;
  updated_at: string;
}

export type PaymentMethod = 'card' | 'bank_transfer' | 'cash' | 'pos' | 'mobile_money';

export interface BookingFormData {
  check_in_date: string;
  check_out_date: string;
  room_id: string;
  special_requests?: string;
}

export interface RoomFilter {
  check_in_date?: string;
  check_out_date?: string;
  room_type?: RoomType;
  min_price?: number;
  max_price?: number;
  amenities?: string[];
  status?: RoomStatus;
  max_occupancy?: number;
}

export interface AuthState {
  user: User | null;
  session: any;
  loading: boolean;
}

export interface AppError {
  message: string;
  code?: string;
  details?: any;
}

// Navigation types
export type RootStackParamList = {
  Auth: undefined;
  Main: undefined;
  RoomDetails: { roomId: string };
  Booking: { roomId: string };
  Payment: { reservationId: string };
  BookingConfirmation: { reservationId: string };
};

export type AuthStackParamList = {
  Login: undefined;
  Register: undefined;
  ForgotPassword: undefined;
};

export type MainTabParamList = {
  Home: undefined;
  Bookings: undefined;
  Profile: undefined;
  Admin: undefined;
};

export type AdminStackParamList = {
  Dashboard: undefined;
  RoomManagement: undefined;
  ReservationManagement: undefined;
  Analytics: undefined;
  AddRoom: undefined;
  EditRoom: { roomId: string };
};
