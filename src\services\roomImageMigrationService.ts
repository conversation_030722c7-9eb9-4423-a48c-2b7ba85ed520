/**
 * Room Image Migration Service
 * Handles migration from legacy string arrays to RoomImage objects
 * Provides backward compatibility and data transformation utilities
 */

import { RoomImage } from '../types';

export class RoomImageMigrationService {
  /**
   * Convert legacy string array to RoomImage array
   */
  static convertLegacyImages(imageUrls: string[]): RoomImage[] {
    if (!imageUrls || !Array.isArray(imageUrls)) {
      return [];
    }

    return imageUrls.map((url, index) => ({
      id: `legacy_${Date.now()}_${index}_${Math.random().toString(36).substr(2, 9)}`,
      url,
      alt_text: `Room image ${index + 1}`,
      upload_date: new Date().toISOString(),
    }));
  }

  /**
   * Extract URLs from RoomImage array for backward compatibility
   */
  static extractImageUrls(roomImages: RoomImage[]): string[] {
    if (!roomImages || !Array.isArray(roomImages)) {
      return [];
    }

    return roomImages.map(image => image.url);
  }

  /**
   * Normalize room images - handles both legacy and new formats
   */
  static normalizeRoomImages(images: any[]): RoomImage[] {
    if (!images || !Array.isArray(images)) {
      return [];
    }

    return images.map((image, index) => {
      if (typeof image === 'string') {
        // Legacy format - convert to RoomImage
        return {
          id: `legacy_${Date.now()}_${index}_${Math.random().toString(36).substr(2, 9)}`,
          url: image,
          alt_text: `Room image ${index + 1}`,
          upload_date: new Date().toISOString(),
        };
      } else if (image && typeof image === 'object' && image.url) {
        // New format - ensure all required fields are present
        return {
          id: image.id || `migrated_${Date.now()}_${index}_${Math.random().toString(36).substr(2, 9)}`,
          url: image.url,
          alt_text: image.alt_text || `Room image ${index + 1}`,
          upload_date: image.upload_date || new Date().toISOString(),
          file_name: image.file_name,
          file_size: image.file_size,
        };
      } else {
        // Invalid format - create a placeholder
        return {
          id: `invalid_${Date.now()}_${index}_${Math.random().toString(36).substr(2, 9)}`,
          url: '',
          alt_text: `Invalid image ${index + 1}`,
          upload_date: new Date().toISOString(),
        };
      }
    });
  }

  /**
   * Check if images array is in legacy format
   */
  static isLegacyFormat(images: any[]): boolean {
    if (!images || !Array.isArray(images) || images.length === 0) {
      return false;
    }

    return images.every(image => typeof image === 'string');
  }

  /**
   * Check if images array is in new format
   */
  static isNewFormat(images: any[]): boolean {
    if (!images || !Array.isArray(images) || images.length === 0) {
      return true; // Empty array is considered new format
    }

    return images.every(image => 
      typeof image === 'object' && 
      image !== null && 
      typeof image.id === 'string' && 
      typeof image.url === 'string'
    );
  }

  /**
   * Validate RoomImage object
   */
  static validateRoomImage(image: any): image is RoomImage {
    return (
      typeof image === 'object' &&
      image !== null &&
      typeof image.id === 'string' &&
      typeof image.url === 'string' &&
      image.id.length > 0 &&
      image.url.length > 0
    );
  }

  /**
   * Generate a unique image ID
   */
  static generateImageId(prefix: string = 'img'): string {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substr(2, 9);
    return `${prefix}_${timestamp}_${random}`;
  }

  /**
   * Create a new RoomImage object from a URL
   */
  static createRoomImageFromUrl(
    url: string, 
    altText?: string, 
    fileName?: string,
    fileSize?: number
  ): RoomImage {
    return {
      id: this.generateImageId(),
      url,
      alt_text: altText || 'Room image',
      upload_date: new Date().toISOString(),
      file_name: fileName,
      file_size: fileSize,
    };
  }

  /**
   * Update alt text for all images in an array
   */
  static updateAltTexts(images: RoomImage[], roomNumber: string): RoomImage[] {
    return images.map((image, index) => ({
      ...image,
      alt_text: `Room ${roomNumber} image ${index + 1}`,
    }));
  }

  /**
   * Remove duplicate images based on URL
   */
  static removeDuplicateImages(images: RoomImage[]): RoomImage[] {
    const seen = new Set<string>();
    return images.filter(image => {
      if (seen.has(image.url)) {
        return false;
      }
      seen.add(image.url);
      return true;
    });
  }

  /**
   * Sort images by upload date (newest first)
   */
  static sortImagesByDate(images: RoomImage[], ascending: boolean = false): RoomImage[] {
    return [...images].sort((a, b) => {
      const dateA = new Date(a.upload_date || 0).getTime();
      const dateB = new Date(b.upload_date || 0).getTime();
      return ascending ? dateA - dateB : dateB - dateA;
    });
  }

  /**
   * Get image statistics
   */
  static getImageStats(images: RoomImage[]): {
    total: number;
    withAltText: number;
    withFileSize: number;
    totalSize: number;
    averageSize: number;
  } {
    const total = images.length;
    const withAltText = images.filter(img => img.alt_text && img.alt_text.trim().length > 0).length;
    const withFileSize = images.filter(img => typeof img.file_size === 'number').length;
    const totalSize = images.reduce((sum, img) => sum + (img.file_size || 0), 0);
    const averageSize = withFileSize > 0 ? totalSize / withFileSize : 0;

    return {
      total,
      withAltText,
      withFileSize,
      totalSize,
      averageSize,
    };
  }

  /**
   * Prepare images for database storage
   * Ensures all images are in the correct format for the database
   */
  static prepareForDatabase(images: RoomImage[]): any[] {
    return images.map(image => ({
      id: image.id,
      url: image.url,
      alt_text: image.alt_text || '',
      upload_date: image.upload_date || new Date().toISOString(),
      file_name: image.file_name || '',
      file_size: image.file_size || null,
    }));
  }

  /**
   * Parse images from database
   * Handles both JSONB and TEXT[] formats
   */
  static parseFromDatabase(dbImages: any): RoomImage[] {
    if (!dbImages) {
      return [];
    }

    // If it's already an array of objects (JSONB format)
    if (Array.isArray(dbImages) && dbImages.length > 0 && typeof dbImages[0] === 'object') {
      return this.normalizeRoomImages(dbImages);
    }

    // If it's an array of strings (legacy TEXT[] format)
    if (Array.isArray(dbImages) && dbImages.length > 0 && typeof dbImages[0] === 'string') {
      return this.convertLegacyImages(dbImages);
    }

    // If it's a JSON string, try to parse it
    if (typeof dbImages === 'string') {
      try {
        const parsed = JSON.parse(dbImages);
        return this.parseFromDatabase(parsed);
      } catch (error) {
        console.error('Failed to parse images JSON:', error);
        return [];
      }
    }

    return [];
  }
}

export default RoomImageMigrationService;
