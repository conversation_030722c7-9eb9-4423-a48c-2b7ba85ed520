-- ============================================================================
-- FIX ADMIN ROLE SYNCHRONIZATION
-- This script fixes the issue where role was changed in public.users 
-- but not in auth.users.raw_user_meta_data
-- ============================================================================

-- ============================================================================
-- STEP 1: Show current state
-- ============================================================================

-- Show users with role mismatch
SELECT 
    'Current State' as info,
    pu.email,
    pu.role as public_role,
    au.raw_user_meta_data->>'role' as auth_metadata_role,
    CASE 
        WHEN pu.role::text != COALESCE(au.raw_user_meta_data->>'role', 'guest') 
        THEN '❌ MISMATCH' 
        ELSE '✅ SYNCED' 
    END as status
FROM public.users pu
JOIN auth.users au ON pu.id = au.id
ORDER BY pu.created_at DESC;

-- ============================================================================
-- STEP 2: Manually set admin role for specific users
-- ============================================================================

-- Set <EMAIL> as admin (update both tables)
UPDATE public.users
SET role = 'admin', updated_at = NOW()
WHERE email = '<EMAIL>';

UPDATE auth.users
SET
    raw_user_meta_data = jsonb_set(
        COALESCE(raw_user_meta_data, '{}'::jsonb),
        '{role}',
        '"admin"'
    ),
    updated_at = NOW()
WHERE email = '<EMAIL>';

-- Set <EMAIL> as admin (update both tables)
UPDATE public.users
SET role = 'admin', updated_at = NOW()
WHERE email = '<EMAIL>';

UPDATE auth.users
SET
    raw_user_meta_data = jsonb_set(
        COALESCE(raw_user_meta_data, '{}'::jsonb),
        '{role}',
        '"admin"'
    ),
    updated_at = NOW()
WHERE email = '<EMAIL>';

-- ============================================================================
-- STEP 2B: General fix for any admin/receptionist users
-- ============================================================================

-- Sync all admin and receptionist roles from public.users to auth.users metadata
UPDATE auth.users
SET
    raw_user_meta_data = jsonb_set(
        COALESCE(raw_user_meta_data, '{}'::jsonb),
        '{role}',
        to_jsonb(pu.role::text)
    ),
    updated_at = NOW()
FROM public.users pu
WHERE auth.users.id = pu.id
AND pu.role IN ('admin', 'receptionist')
AND COALESCE(auth.users.raw_user_meta_data->>'role', 'guest') != pu.role::text;

-- ============================================================================
-- STEP 3: Verify the fix
-- ============================================================================

-- Show updated state
SELECT 
    'After Fix' as info,
    pu.email,
    pu.role as public_role,
    au.raw_user_meta_data->>'role' as auth_metadata_role,
    CASE 
        WHEN pu.role::text = COALESCE(au.raw_user_meta_data->>'role', 'guest') 
        THEN '✅ SYNCED' 
        ELSE '❌ STILL MISMATCH' 
    END as status
FROM public.users pu
JOIN auth.users au ON pu.id = au.id
WHERE pu.role IN ('admin', 'receptionist')
ORDER BY pu.created_at DESC;

-- ============================================================================
-- STEP 4: Instructions
-- ============================================================================

DO $$
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '🔧 ADMIN ROLE SYNC COMPLETED!';
    RAISE NOTICE '';
    RAISE NOTICE '📋 NEXT STEPS FOR THE USER:';
    RAISE NOTICE '1. 🚪 Log out of the app completely';
    RAISE NOTICE '2. 🔄 Close and restart the app';
    RAISE NOTICE '3. 🔐 Log back in with the same credentials';
    RAISE NOTICE '4. ✅ You should now see the admin dashboard!';
    RAISE NOTICE '';
    RAISE NOTICE '🔍 If still not working:';
    RAISE NOTICE '- Clear app cache/data';
    RAISE NOTICE '- Check the "After Fix" results above';
    RAISE NOTICE '- Ensure both public_role and auth_metadata_role show "admin"';
    RAISE NOTICE '';
    RAISE NOTICE '💡 The issue was that changing role in Supabase dashboard';
    RAISE NOTICE '   only updates public.users table, not the JWT metadata!';
END;
$$;
