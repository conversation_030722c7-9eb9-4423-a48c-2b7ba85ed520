-- Simple fix for infinite recursion in RLS policies
-- This approach removes role-based policies and relies on application-level security

-- Drop all problematic policies that cause infinite recursion
DROP POLICY IF EXISTS "Admins can view all users" ON public.users;
DROP POLICY IF EXISTS "Admins can manage all users" ON public.users;
DROP POLICY IF EXISTS "Only admins can manage rooms" ON public.rooms;
DROP POLICY IF EXISTS "Staff can view all reservations" ON public.reservations;
DROP POLICY IF EXISTS "Staff can manage all reservations" ON public.reservations;
DROP POLICY IF EXISTS "Staff can view all payments" ON public.payments;
DROP POLICY IF EXISTS "Admins can upload room images" ON storage.objects;
DROP POLICY IF EXISTS "Admins can update room images" ON storage.objects;
DROP POLICY IF EXISTS "Admins can delete room images" ON storage.objects;

-- Keep only basic user policies (no recursion)
CREATE POLICY "Users can view their own profile" ON public.users
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update their own profile" ON public.users
    FOR UPDATE USING (auth.uid() = id);

-- Allow INSERT for service role and authenticated users
CREATE POLICY "Enable insert for authenticated users" ON public.users
    FOR INSERT WITH CHECK (
        auth.role() = 'service_role' OR
        auth.role() = 'authenticated'
    );

-- Simplified room policies - let application handle admin checks
CREATE POLICY "Anyone can view available rooms" ON public.rooms
    FOR SELECT USING (true);

CREATE POLICY "Authenticated users can manage rooms" ON public.rooms
    FOR ALL USING (auth.role() = 'authenticated');

-- Simplified reservation policies
CREATE POLICY "Users can view their own reservations" ON public.reservations
    FOR SELECT USING (auth.uid() = guest_id);

CREATE POLICY "Users can create their own reservations" ON public.reservations
    FOR INSERT WITH CHECK (auth.uid() = guest_id);

CREATE POLICY "Users can update their own reservations" ON public.reservations
    FOR UPDATE USING (auth.uid() = guest_id);

CREATE POLICY "Authenticated users can view all reservations" ON public.reservations
    FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Authenticated users can manage all reservations" ON public.reservations
    FOR ALL USING (auth.role() = 'authenticated');

-- Simplified payment policies
CREATE POLICY "Users can view their own payments" ON public.payments
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.reservations 
            WHERE id = reservation_id AND guest_id = auth.uid()
        )
    );

CREATE POLICY "Authenticated users can view all payments" ON public.payments
    FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "System can manage payments" ON public.payments
    FOR ALL USING (true);

-- Simplified storage policies
CREATE POLICY "Anyone can view room images" ON storage.objects
    FOR SELECT USING (bucket_id = 'room-images');

CREATE POLICY "Authenticated users can upload room images" ON storage.objects
    FOR INSERT WITH CHECK (
        bucket_id = 'room-images' AND
        auth.role() = 'authenticated'
    );

CREATE POLICY "Authenticated users can update room images" ON storage.objects
    FOR UPDATE USING (
        bucket_id = 'room-images' AND
        auth.role() = 'authenticated'
    );

CREATE POLICY "Authenticated users can delete room images" ON storage.objects
    FOR DELETE USING (
        bucket_id = 'room-images' AND
        auth.role() = 'authenticated'
    );

-- Refresh the schema
NOTIFY pgrst, 'reload schema';
