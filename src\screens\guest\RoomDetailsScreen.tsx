import React, { useEffect, useState, useRef } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  Dimensions,
  Image,
  Alert,
  TouchableOpacity,
  ActivityIndicator,
} from 'react-native';
import {
  Text,
  Button,
  Card,
  Chip,
  Surface,
  IconButton,
} from 'react-native-paper';
import { useRoute, useNavigation } from '@react-navigation/native';
import { MaterialIcons } from '@expo/vector-icons';
import PagerView from 'react-native-pager-view';

import { useRoomStore } from '../../store/roomStore';
import { useAuthStore } from '../../store/authStore';
import { Colors, Spacing, Typography } from '../../constants';
import { formatPrice } from '../../utils/currency';
import type { GuestNavigationProp } from '../../navigation/GuestNavigator';

const { width } = Dimensions.get('window');

interface RouteParams {
  roomId: string;
}

export const RoomDetailsScreen = () => {
  const route = useRoute();
  const navigation = useNavigation<GuestNavigationProp>();
  const { roomId } = route.params as RouteParams;
  
  const { user } = useAuthStore();
  const { 
    selectedRoom, 
    loading, 
    error, 
    fetchRoomById, 
    checkAvailability,
    setSelectedRoom 
  } = useRoomStore();
  
  const [imageIndex, setImageIndex] = useState(0);
  const [checkingAvailability, setCheckingAvailability] = useState(false);
  const pagerRef = useRef<PagerView>(null);

  useEffect(() => {
    loadRoomDetails();
  }, [roomId]);

  useEffect(() => {
    return () => {
      setSelectedRoom(null);
    };
  }, []);

  const loadRoomDetails = async () => {
    await fetchRoomById(roomId);
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={Colors.primary} />
        <Text style={styles.loadingText}>Loading room details...</Text>
      </View>
    );
  }

  if (error || !selectedRoom) {
    return (
      <View style={styles.errorContainer}>
        <MaterialIcons name="error-outline" size={64} color={Colors.error} />
        <Text style={styles.errorTitle}>Room Not Found</Text>
        <Text style={styles.errorMessage}>
          {error || 'Unable to load room details'}
        </Text>
        <Button
          mode="outlined"
          onPress={loadRoomDetails}
          style={styles.retryButton}
        >
          Retry
        </Button>
      </View>
    );
  }

  const room = selectedRoom;

  const handleBookNowPress = async () => {
    if (!user) {
      Alert.alert(
        'Sign In Required',
        'Please sign in to make a reservation.',
        [
          { text: 'Cancel', style: 'cancel' },
          { text: 'Sign In', onPress: () => navigation.navigate('Auth') }
        ]
      );
      return;
    }

    // Navigate to booking screen with proper parameters
    // Using default dates for now - in a real app, you'd get these from a date picker
    const today = new Date();
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    const checkIn = today.toISOString().split('T')[0];
    const checkOut = tomorrow.toISOString().split('T')[0];

    navigation.navigate('Booking', {
      room: room,
      checkIn,
      checkOut,
      guests: 1
    });
  };

  const getRoomTypeIcon = (type: string) => {
    switch (type) {
      case 'standard':
        return 'bed';
      case 'deluxe':
        return 'bed-queen';
      case 'suite':
        return 'domain';
      case 'presidential':
        return 'crown';
      default:
        return 'bed';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'available':
        return Colors.success;
      case 'booked':
        return Colors.error;
      case 'maintenance':
        return Colors.warning;
      case 'cleaning':
        return Colors.info;
      default:
        return Colors.textSecondary;
    }
  };



  const renderImagePager = () => {
    if (!room.images || room.images.length === 0) {
      return (
        <View style={styles.noImageContainer}>
          <MaterialIcons name="image-not-supported" size={64} color={Colors.textSecondary} />
          <Text style={styles.noImageText}>No images available</Text>
        </View>
      );
    }

    // Handle both legacy string arrays and new RoomImage objects
    const imageItems = room.images.map((image, index) => {
      if (typeof image === 'string') {
        // Legacy format - convert to RoomImage-like object
        return {
          id: `legacy_${index}`,
          url: image,
          alt_text: `Room ${room.room_number} image ${index + 1}`,
        };
      }
      // New format - already a RoomImage object
      return image;
    });

    return (
      <View style={styles.imageContainer}>
        <PagerView
          ref={pagerRef}
          style={styles.pager}
          initialPage={0}
          onPageSelected={(e) => setImageIndex(e.nativeEvent.position)}
        >
          {imageItems.map((image, index) => (
            <View key={image.id} style={styles.imagePage}>
              <Image
                source={{ uri: image.url }}
                style={styles.roomImage}
                resizeMode="cover"
                accessibilityLabel={image.alt_text || `Room image ${index + 1}`}
              />
            </View>
          ))}
        </PagerView>

        {imageItems.length > 1 && (
          <>
            <View style={styles.imageIndicators}>
              {imageItems.map((image, index) => (
                <View
                  key={`indicator_${image.id}`}
                  style={[
                    styles.indicator,
                    index === imageIndex && styles.activeIndicator
                  ]}
                />
              ))}
            </View>

            <IconButton
              icon="chevron-left"
              size={24}
              iconColor={Colors.white}
              containerColor="rgba(0,0,0,0.5)"
              style={[styles.imageNavButton, styles.prevButton]}
              onPress={() => {
                const prevIndex = imageIndex > 0 ? imageIndex - 1 : imageItems.length - 1;
                pagerRef.current?.setPage(prevIndex);
              }}
            />

            <IconButton
              icon="chevron-right"
              size={24}
              iconColor={Colors.white}
              containerColor="rgba(0,0,0,0.5)"
              style={[styles.imageNavButton, styles.nextButton]}
              onPress={() => {
                const nextIndex = imageIndex < imageItems.length - 1 ? imageIndex + 1 : 0;
                pagerRef.current?.setPage(nextIndex);
              }}
            />
          </>
        )}
      </View>
    );
  };

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      {/* Room Images */}
      {renderImagePager()}

      <View style={styles.content}>
        {/* Room Header */}
        <View style={styles.roomHeader}>
          <View style={styles.roomTitleContainer}>
            <Text style={styles.roomNumber}>Room {room.room_number}</Text>
            <View style={styles.roomTypeContainer}>
              <MaterialIcons 
                name={getRoomTypeIcon(room.room_type)} 
                size={20} 
                color={Colors.primary} 
              />
              <Text style={styles.roomType}>{room.room_type}</Text>
            </View>
          </View>
          
          <Chip
            mode="outlined"
            textStyle={[styles.statusText, { color: getStatusColor(room.status) }]}
            style={[styles.statusChip, { borderColor: getStatusColor(room.status) }]}
            compact={false}
          >
            {room.status}
          </Chip>
        </View>

        {/* Price */}
        <Surface style={styles.priceCard}>
          <Text style={styles.priceLabel}>Price per night</Text>
          <Text style={styles.price}>{formatPrice(room.price_per_night)}</Text>
        </Surface>

        {/* Room Info */}
        <Card style={styles.infoCard}>
          <Card.Content>
            <Text style={styles.sectionTitle}>Room Information</Text>
            <View style={styles.infoGrid}>
              <View style={styles.infoItem}>
                <MaterialIcons name="people" size={24} color={Colors.primary} />
                <View>
                  <Text style={styles.infoLabel}>Max Occupancy</Text>
                  <Text style={styles.infoValue}>{room.max_occupancy} guests</Text>
                </View>
              </View>
              
              <View style={styles.infoItem}>
                <MaterialIcons name="square-foot" size={24} color={Colors.primary} />
                <View>
                  <Text style={styles.infoLabel}>Room Type</Text>
                  <Text style={styles.infoValue}>{room.room_type}</Text>
                </View>
              </View>
            </View>
          </Card.Content>
        </Card>

        {/* Description */}
        <Card style={styles.descriptionCard}>
          <Card.Content>
            <Text style={styles.sectionTitle}>Description</Text>
            <Text style={styles.description}>{room.description}</Text>
          </Card.Content>
        </Card>

        {/* Amenities */}
        {room.amenities && room.amenities.length > 0 && (
          <Card style={styles.amenitiesCard}>
            <Card.Content>
              <Text style={styles.sectionTitle}>Amenities</Text>
              <View style={styles.amenitiesGrid}>
                {room.amenities.map((amenity, index) => (
                  <View key={index} style={styles.amenityItem}>
                    <MaterialIcons name="check" size={16} color={Colors.success} />
                    <Text style={styles.amenityText}>{amenity}</Text>
                  </View>
                ))}
              </View>
            </Card.Content>
          </Card>
        )}

        {/* Booking Button */}
        <View style={styles.bookingSection}>
          <TouchableOpacity
            onPress={handleBookNowPress}
            disabled={room.status !== 'available' || checkingAvailability}
            style={[
              styles.bookButton,
              {
                backgroundColor: room.status !== 'available' ? Colors.disabled : Colors.primary,
                opacity: (room.status !== 'available' || checkingAvailability) ? 0.6 : 1,
              }
            ]}
            activeOpacity={0.8}
          >
            {checkingAvailability ? (
              <ActivityIndicator size="small" color="#FFFFFF" style={{ marginRight: 8 }} />
            ) : null}
            <Text style={styles.bookButtonText}>
              {checkingAvailability
                ? 'Checking...'
                : room.status !== 'available'
                ? `Room ${room.status.charAt(0).toUpperCase() + room.status.slice(1)}`
                : 'Book Now'
              }
            </Text>
          </TouchableOpacity>

          <Text style={styles.bookingNote}>
            {room.status === 'available'
              ? 'Free cancellation within 24 hours'
              : 'This room is currently not available for booking'
            }
          </Text>
        </View>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.background,
  },
  loadingText: {
    ...Typography.body,
    marginTop: Spacing.medium,
    color: Colors.textSecondary,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: Spacing.large,
  },
  errorTitle: {
    ...Typography.h3,
    fontWeight: 'bold',
    marginTop: Spacing.medium,
    marginBottom: Spacing.small,
    color: Colors.textPrimary,
  },
  errorMessage: {
    ...Typography.body,
    color: Colors.textSecondary,
    textAlign: 'center',
    marginBottom: Spacing.large,
  },
  retryButton: {
    marginTop: Spacing.medium,
  },
  imageContainer: {
    height: 300,
    position: 'relative',
  },
  pager: {
    flex: 1,
  },
  imagePage: {
    flex: 1,
  },
  roomImage: {
    width: '100%',
    height: '100%',
  },
  noImageContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.surface,
  },
  noImageText: {
    ...Typography.body,
    color: Colors.textSecondary,
    marginTop: Spacing.small,
  },
  imageIndicators: {
    position: 'absolute',
    bottom: Spacing.medium,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'center',
    gap: Spacing.small,
  },
  indicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: 'rgba(255,255,255,0.5)',
  },
  activeIndicator: {
    backgroundColor: Colors.white,
  },
  imageNavButton: {
    position: 'absolute',
    top: '50%',
    marginTop: -20,
  },
  prevButton: {
    left: Spacing.medium,
  },
  nextButton: {
    right: Spacing.medium,
  },
  content: {
    padding: Spacing.medium,
  },
  roomHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: Spacing.medium,
    minHeight: 80,
  },
  roomTitleContainer: {
    flex: 1,
    paddingRight: Spacing.medium,
  },
  roomNumber: {
    ...Typography.h2,
    fontWeight: 'bold',
    color: Colors.textPrimary,
    marginBottom: Spacing.small,
    fontSize: 28,
  },
  roomTypeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.small,
    marginTop: 4,
  },
  roomType: {
    ...Typography.subtitle,
    color: Colors.textPrimary,
    textTransform: 'capitalize',
    fontWeight: '600',
  },
  statusChip: {
    marginTop: Spacing.small,
    minWidth: 80,
    height: 32,
    paddingHorizontal: 8,
  },
  statusText: {
    fontSize: 12,
    textTransform: 'capitalize',
    fontWeight: '500',
  },
  priceCard: {
    padding: Spacing.medium,
    marginBottom: Spacing.medium,
    borderRadius: 8,
    elevation: 2,
  },
  priceLabel: {
    ...Typography.body,
    color: Colors.textSecondary,
    marginBottom: Spacing.small,
    fontSize: 14,
    fontWeight: '500',
  },
  price: {
    ...Typography.h2,
    fontWeight: 'bold',
    color: Colors.primary,
    fontSize: 28,
  },
  sectionTitle: {
    ...Typography.h4,
    fontWeight: 'bold',
    color: Colors.textPrimary,
    marginBottom: Spacing.medium,
    fontSize: 20,
  },
  infoCard: {
    marginBottom: Spacing.medium,
  },
  infoGrid: {
    gap: Spacing.medium,
  },
  infoItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.medium,
  },
  infoLabel: {
    ...Typography.caption,
    color: Colors.textSecondary,
    fontSize: 12,
    marginBottom: 2,
  },
  infoValue: {
    ...Typography.body,
    fontWeight: '600',
    color: Colors.textPrimary,
    fontSize: 16,
  },
  descriptionCard: {
    marginBottom: Spacing.medium,
  },
  description: {
    ...Typography.body,
    color: Colors.textPrimary,
    lineHeight: 24,
    fontSize: 16,
    fontWeight: '400',
  },
  amenitiesCard: {
    marginBottom: Spacing.medium,
  },
  amenitiesGrid: {
    gap: Spacing.small,
  },
  amenityItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.small,
  },
  amenityText: {
    ...Typography.body,
    color: Colors.textPrimary,
    flex: 1,
    fontSize: 16,
    fontWeight: '500',
  },
  bookingSection: {
    marginTop: Spacing.large,
    marginBottom: Spacing.extraLarge,
  },
  bookButton: {
    marginBottom: Spacing.medium,
    borderRadius: 12,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    paddingVertical: Spacing.medium,
    paddingHorizontal: Spacing.large,
    height: 56,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  bookButtonText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FFFFFF',
    textAlign: 'center',
  },
  bookingNote: {
    ...Typography.caption,
    color: Colors.textSecondary,
    textAlign: 'center',
    fontSize: 14,
    fontWeight: '400',
    marginTop: 8,
  },
});
