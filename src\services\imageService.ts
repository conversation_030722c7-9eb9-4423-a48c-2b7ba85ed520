import * as ImagePicker from 'expo-image-picker';
import * as ImageManipulator from 'expo-image-manipulator';
import { Platform } from 'expo-modules-core';
import { storageService } from './supabase';

export interface ImagePickerOptions {
  mediaTypes?: ImagePicker.MediaTypeOptions;
  allowsEditing?: boolean;
  aspect?: [number, number];
  quality?: number;
  maxWidth?: number;
  maxHeight?: number;
  compress?: boolean;
}

export interface ImageUploadResult {
  uri: string;
  url?: string;
  width: number;
  height: number;
  fileSize?: number;
}

export class ImageService {
  // Request camera permissions
  async requestCameraPermissions(): Promise<boolean> {
    if (Platform.OS !== 'web') {
      const { status } = await ImagePicker.requestCameraPermissionsAsync();
      return status === 'granted';
    }
    return true;
  }

  // Request media library permissions
  async requestMediaLibraryPermissions(): Promise<boolean> {
    if (Platform.OS !== 'web') {
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
      return status === 'granted';
    }
    return true;
  }

  // Pick image from camera
  async pickFromCamera(options: ImagePickerOptions = {}): Promise<ImageUploadResult | null> {
    const hasPermission = await this.requestCameraPermissions();
    if (!hasPermission) {
      throw new Error('Camera permission denied');
    }

    const defaultOptions: ImagePicker.CameraOptions = {
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [4, 3],
      quality: 0.8,
      ...options,
    };

    const result = await ImagePicker.launchCameraAsync(defaultOptions);

    if (result.canceled || !result.assets?.[0]) {
      return null;
    }

    const asset = result.assets[0];
    return await this.processImage(asset, options);
  }

  // Pick image from gallery
  async pickFromGallery(options: ImagePickerOptions = {}): Promise<ImageUploadResult | null> {
    const hasPermission = await this.requestMediaLibraryPermissions();
    if (!hasPermission) {
      throw new Error('Media library permission denied');
    }

    const defaultOptions: ImagePicker.ImagePickerOptions = {
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [4, 3],
      quality: 0.8,
      ...options,
    };

    const result = await ImagePicker.launchImageLibraryAsync(defaultOptions);

    if (result.canceled || !result.assets?.[0]) {
      return null;
    }

    const asset = result.assets[0];
    return await this.processImage(asset, options);
  }

  // Pick multiple images from gallery
  async pickMultipleFromGallery(options: ImagePickerOptions = {}): Promise<ImageUploadResult[]> {
    const hasPermission = await this.requestMediaLibraryPermissions();
    if (!hasPermission) {
      throw new Error('Media library permission denied');
    }

    const defaultOptions: ImagePicker.ImagePickerOptions = {
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: false,
      quality: 0.8,
      allowsMultipleSelection: true,
      selectionLimit: 10,
      ...options,
    };

    const result = await ImagePicker.launchImageLibraryAsync(defaultOptions);

    if (result.canceled || !result.assets) {
      return [];
    }

    const processedImages = await Promise.all(
      result.assets.map(asset => this.processImage(asset, options))
    );

    return processedImages.filter((img): img is ImageUploadResult => img !== null);
  }

  // Process and optimize image
  private async processImage(
    asset: ImagePicker.Asset,
    options: ImagePickerOptions
  ): Promise<ImageUploadResult | null> {
    try {
      let processedImage = asset;

      // Compress and resize if needed
      if (options.compress !== false || options.maxWidth || options.maxHeight) {
        const manipulateOptions: ImageManipulator.Action[] = [];

        // Resize if dimensions are specified
        if (options.maxWidth || options.maxHeight) {
          const resize: ImageManipulator.Action = {
            resize: {
              width: options.maxWidth,
              height: options.maxHeight,
            },
          };
          manipulateOptions.push(resize);
        }

        const manipulateResult = await ImageManipulator.manipulateAsync(
          asset.uri,
          manipulateOptions,
          {
            compress: options.quality || 0.8,
            format: ImageManipulator.SaveFormat.JPEG,
          }
        );

        processedImage = {
          ...asset,
          uri: manipulateResult.uri,
          width: manipulateResult.width,
          height: manipulateResult.height,
        };
      }

      return {
        uri: processedImage.uri,
        width: processedImage.width || 0,
        height: processedImage.height || 0,
        fileSize: processedImage.fileSize,
      };
    } catch (error) {
      console.error('Error processing image:', error);
      return null;
    }
  }

  // Upload image to Supabase storage and return RoomImage object
  async uploadRoomImage(imageUri: string, roomId: string): Promise<import('../types').RoomImage | null> {
    try {
      // Convert URI to blob for upload
      const response = await fetch(imageUri);
      const blob = await response.blob();

      // Generate unique filename
      const timestamp = Date.now();
      const fileName = `room-${roomId}-${timestamp}.jpg`;

      // Create File object
      const file = new File([blob], fileName, {
        type: 'image/jpeg',
      });

      const { data: url, error } = await storageService.uploadRoomImage(file, roomId);

      if (error) {
        console.error('Upload error:', error);
        return null;
      }

      // Return RoomImage object with metadata
      return {
        id: `img_${timestamp}_${Math.random().toString(36).substr(2, 9)}`,
        url: url,
        alt_text: `Room ${roomId} image`,
        upload_date: new Date().toISOString(),
        file_name: fileName,
        file_size: blob.size,
      };
    } catch (error) {
      console.error('Error uploading image:', error);
      return null;
    }
  }

  // Upload multiple room images
  async uploadMultipleRoomImages(
    imageUris: string[],
    roomId: string,
    onProgress?: (progress: number) => void
  ): Promise<import('../types').RoomImage[]> {
    const uploadedImages: import('../types').RoomImage[] = [];

    for (let i = 0; i < imageUris.length; i++) {
      const roomImage = await this.uploadRoomImage(imageUris[i], roomId);
      if (roomImage) {
        uploadedImages.push(roomImage);
      }

      // Report progress
      if (onProgress) {
        onProgress((i + 1) / imageUris.length);
      }
    }

    return uploadedImages;
  }

  // Delete image from storage using RoomImage object
  async deleteRoomImage(roomImage: import('../types').RoomImage): Promise<boolean> {
    try {
      // Extract path from URL or use file_name
      const fileName = roomImage.file_name || roomImage.url.split('/').pop() || '';
      const roomIdMatch = fileName.match(/room-([^-]+)-/);

      if (roomIdMatch) {
        const roomId = roomIdMatch[1];
        const imagePath = `${roomId}/${fileName}`;

        const { error } = await storageService.deleteRoomImage(imagePath);
        return !error;
      }

      return false;
    } catch (error) {
      console.error('Error deleting image:', error);
      return false;
    }
  }

  // Legacy method for backward compatibility
  async deleteRoomImageByUrl(imageUrl: string): Promise<boolean> {
    try {
      // Extract path from URL
      const urlParts = imageUrl.split('/');
      const fileName = urlParts[urlParts.length - 1];
      const roomIdMatch = fileName.match(/room-([^-]+)-/);

      if (roomIdMatch) {
        const roomId = roomIdMatch[1];
        const imagePath = `${roomId}/${fileName}`;

        const { error } = await storageService.deleteRoomImage(imagePath);
        return !error;
      }

      return false;
    } catch (error) {
      console.error('Error deleting image:', error);
      return false;
    }
  }

  // Get optimized image URL with transformations
  getOptimizedImageUrl(
    originalUrl: string,
    options: {
      width?: number;
      height?: number;
      quality?: number;
      format?: 'webp' | 'jpeg' | 'png';
    } = {}
  ): string {
    // For Supabase storage, you might implement image transformations
    // This is a placeholder implementation
    const params = new URLSearchParams();
    
    if (options.width) params.append('width', options.width.toString());
    if (options.height) params.append('height', options.height.toString());
    if (options.quality) params.append('quality', options.quality.toString());
    if (options.format) params.append('format', options.format);
    
    const queryString = params.toString();
    return queryString ? `${originalUrl}?${queryString}` : originalUrl;
  }

  // Show image picker action sheet
  async showImagePickerOptions(): Promise<ImageUploadResult | null> {
    return new Promise((resolve) => {
      // You would typically show an action sheet here
      // For now, we'll default to gallery picker
      this.pickFromGallery().then(resolve);
    });
  }

  // Validate image file
  validateImage(asset: ImagePicker.Asset): { valid: boolean; error?: string } {
    // Check file size (max 10MB)
    if (asset.fileSize && asset.fileSize > 10 * 1024 * 1024) {
      return { valid: false, error: 'Image file size must be less than 10MB' };
    }

    // Check dimensions (min 300x300, max 4000x4000)
    if (asset.width && asset.height) {
      if (asset.width < 300 || asset.height < 300) {
        return { valid: false, error: 'Image dimensions must be at least 300x300 pixels' };
      }
      
      if (asset.width > 4000 || asset.height > 4000) {
        return { valid: false, error: 'Image dimensions must be less than 4000x4000 pixels' };
      }
    }

    // Check aspect ratio for room images (should be reasonable)
    if (asset.width && asset.height) {
      const aspectRatio = asset.width / asset.height;
      if (aspectRatio < 0.5 || aspectRatio > 3) {
        return { valid: false, error: 'Image aspect ratio should be between 1:2 and 3:1' };
      }
    }

    return { valid: true };
  }

  // Create image thumbnail
  async createThumbnail(
    imageUri: string,
    size: { width: number; height: number } = { width: 200, height: 200 }
  ): Promise<string | null> {
    try {
      const result = await ImageManipulator.manipulateAsync(
        imageUri,
        [
          {
            resize: {
              width: size.width,
              height: size.height,
            },
          },
        ],
        {
          compress: 0.7,
          format: ImageManipulator.SaveFormat.JPEG,
        }
      );

      return result.uri;
    } catch (error) {
      console.error('Error creating thumbnail:', error);
      return null;
    }
  }

  // Utility function to convert legacy string arrays to RoomImage arrays
  convertLegacyImages(imageUrls: string[]): import('../types').RoomImage[] {
    return imageUrls.map((url, index) => ({
      id: `legacy_${Date.now()}_${index}`,
      url,
      alt_text: `Room image ${index + 1}`,
      upload_date: new Date().toISOString(),
    }));
  }

  // Utility function to extract URLs from RoomImage arrays
  extractImageUrls(roomImages: import('../types').RoomImage[]): string[] {
    return roomImages.map(image => image.url);
  }
}

export const imageService = new ImageService();
