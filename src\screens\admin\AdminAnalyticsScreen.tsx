import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  RefreshControl,
  Dimensions,
  StatusBar,
  Animated,
  TouchableOpacity,
  ActivityIndicator,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import {
  Card,
  Chip,
  Button,
  Surface,
} from 'react-native-paper';
import { <PERSON><PERSON><PERSON>, BarChart, Pie<PERSON>hart } from 'react-native-chart-kit';
import { MaterialIcons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { colors, spacing, typography } from '../../constants';
import { useReservationStore } from '../../store/reservationStore';
import { useRoomStore } from '../../store/roomStore';
import { formatPrice } from '../../utils/currency';
import { Reservation, Room } from '../../types/database';
import { ReportDownloadModal } from '../../components/admin/ReportDownloadModal';
import { ReportData } from '../../services/reportService';
import { generateSampleReservations, generateSampleRooms, shouldUseSampleData } from '../../utils/sampleData';
import { runReportTests } from '../../utils/testReports';

const { width: screenWidth } = Dimensions.get('window');

interface AnalyticsData {
  totalRevenue: number;
  totalReservations: number;
  averageBookingValue: number;
  occupancyRate: number;
  cancelationRate: number;
  topRoomTypes: Array<{ type: string; count: number; revenue: number }>;
  monthlyRevenue: Array<{ month: string; revenue: number }>;
  bookingsByStatus: Array<{ status: string; count: number; color: string }>;
  seasonalTrends: Array<{ period: string; bookings: number }>;
}

export const AdminAnalyticsScreen = () => {
  const { reservations, fetchAllReservations, loading: reservationsLoading, error: reservationsError } = useReservationStore();
  const { rooms, fetchRooms, loading: roomsLoading, error: roomsError } = useRoomStore();
  const [refreshing, setRefreshing] = useState(false);
  const [selectedPeriod, setSelectedPeriod] = useState<'week' | 'month' | 'year'>('month');
  const [showDownloadModal, setShowDownloadModal] = useState(false);
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData>({
    totalRevenue: 0,
    totalReservations: 0,
    averageBookingValue: 0,
    occupancyRate: 0,
    cancelationRate: 0,
    topRoomTypes: [],
    monthlyRevenue: [],
    bookingsByStatus: [],
    seasonalTrends: [],
  });

  useEffect(() => {
    loadAnalyticsData();
  }, [selectedPeriod]);

  const loadAnalyticsData = async () => {
    try {
      console.log('🔄 Analytics: Loading data...');
      await Promise.all([fetchAllReservations(), fetchRooms()]);
      console.log('✅ Analytics: Data loaded, calculating analytics...');
      calculateAnalytics();
    } catch (error) {
      console.error('❌ Analytics: Error loading data:', error);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadAnalyticsData();
    setRefreshing(false);
  };

  const calculateAnalytics = () => {
    console.log('🔄 Analytics: Starting calculation...');
    console.log('📊 Reservations data:', reservations.length);
    console.log('🏨 Rooms data:', rooms.length);

    // Use sample data if no real data is available
    let analyticsReservations = reservations;
    let analyticsRooms = rooms;

    if (shouldUseSampleData(reservations, rooms)) {
      console.log('📊 Using sample data for analytics...');
      analyticsReservations = generateSampleReservations();
      analyticsRooms = generateSampleRooms();
    }

    const now = new Date();
    let startDate: Date;

    switch (selectedPeriod) {
      case 'week':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case 'month':
        startDate = new Date(now.getFullYear(), now.getMonth() - 1, now.getDate());
        break;
      case 'year':
        startDate = new Date(now.getFullYear() - 1, now.getMonth(), now.getDate());
        break;
    }

    const filteredReservations = analyticsReservations.filter(
      reservation => new Date(reservation.created_at) >= startDate
    );

    console.log('📊 Filtered reservations:', filteredReservations.length);

    // Basic metrics
    const totalRevenue = filteredReservations
      .filter(r => r.status !== 'cancelled')
      .reduce((sum, r) => sum + (r.total_amount || 0), 0);

    const totalReservations = filteredReservations.length;
    const averageBookingValue = totalReservations > 0 ? totalRevenue / totalReservations : 0;

    // Occupancy rate calculation
    const occupiedRooms = analyticsRooms.filter(room => !room.is_available).length;
    const occupancyRate = analyticsRooms.length > 0 ? (occupiedRooms / analyticsRooms.length) * 100 : 0;

    // Cancellation rate
    const cancelledReservations = filteredReservations.filter(r => r.status === 'cancelled').length;
    const cancelationRate = totalReservations > 0 ? (cancelledReservations / totalReservations) * 100 : 0;

    // Top room types - Fix field mapping issue
    const roomTypeStats = analyticsRooms.reduce((acc, room) => {
      // Use room_type from database or type from frontend mapping
      const roomType = room.room_type || room.type || 'unknown';
      const reservationsForRoom = filteredReservations.filter(r => r.room_id === room.id);
      const revenue = reservationsForRoom
        .filter(r => r.status !== 'cancelled')
        .reduce((sum, r) => sum + (r.total_amount || 0), 0);

      if (!acc[roomType]) {
        acc[roomType] = { count: 0, revenue: 0 };
      }
      acc[roomType].count += reservationsForRoom.length;
      acc[roomType].revenue += revenue;
      return acc;
    }, {} as Record<string, { count: number; revenue: number }>);

    const topRoomTypes = Object.entries(roomTypeStats)
      .map(([type, stats]) => ({ 
        type, 
        count: (stats as any).count, 
        revenue: (stats as any).revenue 
      }))
      .sort((a, b) => b.revenue - a.revenue);

    console.log('📊 Room type stats:', roomTypeStats);

    // Monthly revenue (last 6 months)
    const monthlyRevenue = [];
    for (let i = 5; i >= 0; i--) {
      const date = new Date(now.getFullYear(), now.getMonth() - i, 1);
      const monthStart = new Date(date.getFullYear(), date.getMonth(), 1);
      const monthEnd = new Date(date.getFullYear(), date.getMonth() + 1, 0);

      const monthReservations = analyticsReservations.filter(r => {
        const reservationDate = new Date(r.created_at);
        return reservationDate >= monthStart && reservationDate <= monthEnd && r.status !== 'cancelled';
      });

      const revenue = monthReservations.reduce((sum, r) => sum + (r.total_amount || 0), 0);

      monthlyRevenue.push({
        month: date.toLocaleDateString('en-US', { month: 'short' }),
        revenue,
      });
    }

    console.log('📊 Monthly revenue data:', monthlyRevenue);

    // Bookings by status
    const statusCounts = filteredReservations.reduce((acc, reservation) => {
      acc[reservation.status] = (acc[reservation.status] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const statusColors = {
      pending: colors.warning,
      confirmed: colors.success,
      checked_in: colors.info,
      checked_out: colors.onSurfaceVariant,
      cancelled: colors.error,
    };

    const bookingsByStatus = Object.entries(statusCounts).map(([status, count]) => ({
      status: status.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase()),
      count,
      color: statusColors[status as keyof typeof statusColors] || colors.onSurfaceVariant,
    }));

    // Seasonal trends (last 4 quarters)
    const seasonalTrends = [];
    for (let i = 3; i >= 0; i--) {
      const quarterStart = new Date(now.getFullYear(), now.getMonth() - (i * 3), 1);
      const quarterEnd = new Date(now.getFullYear(), now.getMonth() - (i * 3) + 3, 0);
      
      const quarterReservations = analyticsReservations.filter(r => {
        const reservationDate = new Date(r.created_at);
        return reservationDate >= quarterStart && reservationDate <= quarterEnd;
      });
      
      seasonalTrends.push({
        period: `Q${Math.floor((now.getMonth() - (i * 3)) / 3) + 1}`,
        bookings: quarterReservations.length,
      });
    }

    console.log('📊 Final analytics data:', {
      totalRevenue,
      totalReservations,
      averageBookingValue,
      occupancyRate,
      cancelationRate,
      topRoomTypesCount: topRoomTypes.length,
      monthlyRevenueCount: monthlyRevenue.length,
      bookingsByStatusCount: bookingsByStatus.length,
      seasonalTrendsCount: seasonalTrends.length,
    });

    setAnalyticsData({
      totalRevenue,
      totalReservations,
      averageBookingValue,
      occupancyRate,
      cancelationRate,
      topRoomTypes,
      monthlyRevenue,
      bookingsByStatus,
      seasonalTrends,
    });
  };

  // Prepare report data for download
  const getReportData = (): ReportData => {
    // Use sample data if no real data is available
    let reportReservations = reservations;
    let reportRooms = rooms;

    if (shouldUseSampleData(reservations, rooms)) {
      reportReservations = generateSampleReservations();
      reportRooms = generateSampleRooms();
    }

    return {
      ...analyticsData,
      period: selectedPeriod,
      generatedAt: new Date().toISOString(),
      reservations: reportReservations,
      rooms: reportRooms,
    };
  };

  const chartConfig = {
    backgroundGradientFrom: colors.surface,
    backgroundGradientTo: colors.surface,
    color: (opacity = 1) => `rgba(${colors.primaryRGB}, ${opacity})`,
    labelColor: (opacity = 1) => `rgba(${colors.onSurfaceRGB}, ${opacity})`,
    strokeWidth: 2,
    barPercentage: 0.5,
    useShadowColorFromDataset: false,
  };

  const MetricCard = ({ 
    title, 
    value, 
    subtitle, 
    icon, 
    color = colors.primary,
    gradient = [colors.primary + '20', colors.primary + '10']
  }: {
    title: string;
    value: string;
    subtitle?: string;
    icon: string;
    color?: string;
    gradient?: string[];
  }) => {
    const animatedValue = new Animated.Value(0);

    React.useEffect(() => {
      Animated.spring(animatedValue, {
        toValue: 1,
        useNativeDriver: true,
        tension: 100,
        friction: 8,
      }).start();
    }, []);

    return (
      <Animated.View 
        style={[
          styles.metricCard,
          {
            transform: [
              {
                scale: animatedValue.interpolate({
                  inputRange: [0, 1],
                  outputRange: [0.8, 1],
                })
              }
            ],
            opacity: animatedValue
          }
        ]}
      >
        <LinearGradient
          colors={gradient}
          style={styles.metricGradient}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
        >
          <Surface style={styles.metricSurface} elevation={2}>
            <View style={styles.metricContent}>
              <View style={styles.metricHeader}>
                <View style={[styles.metricIconContainer, { backgroundColor: color + '15' }]}>
                  <MaterialIcons name={icon as any} size={28} color={color} />
                </View>
                <View style={styles.metricTextContainer}>
                  <Text style={[styles.metricValue, { color: colors.onSurface }]}>{value}</Text>
                  <Text style={styles.metricTitle}>{title}</Text>
                  {subtitle && <Text style={[styles.metricSubtitle, { color }]}>{subtitle}</Text>}
                </View>
              </View>
            </View>
          </Surface>
        </LinearGradient>
      </Animated.View>
    );
  };

  // Show loading state
  if (reservationsLoading || roomsLoading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={styles.loadingText}>Loading analytics data...</Text>
        </View>
      </SafeAreaView>
    );
  }

  // Show error state
  if (reservationsError || roomsError) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>
            Error loading data: {reservationsError || roomsError}
          </Text>
          <Button mode="contained" onPress={loadAnalyticsData} style={styles.retryButton}>
            Retry
          </Button>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor={colors.primary} />
      
      {/* Modern Header with Gradient */}
      <LinearGradient
        colors={[colors.primary, colors.primaryDark]}
        style={styles.headerGradient}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        <SafeAreaView>
          <View style={styles.headerContent}>
            <View style={styles.headerTextContainer}>
              <Text style={styles.headerTitle}>Analytics Dashboard</Text>
              <Text style={styles.headerSubtitle}>Hotel Performance Overview</Text>
            </View>
            <TouchableOpacity
              style={styles.downloadButton}
              onPress={() => setShowDownloadModal(true)}
              activeOpacity={0.8}
            >
              <MaterialIcons name="download" size={24} color={colors.onPrimary} />
              <Text style={styles.downloadButtonText}>Export</Text>
            </TouchableOpacity>
          </View>
        </SafeAreaView>
      </LinearGradient>

      {/* Modern Period Selector */}
      <View style={styles.periodSelectorContainer}>
        <Surface style={styles.periodSelectorSurface} elevation={1}>
          <View style={styles.segmentedButtons}>
            {[
              { value: 'week', label: 'Week', icon: 'today' },
              { value: 'month', label: 'Month', icon: 'event' },
              { value: 'year', label: 'Year', icon: 'date-range' },
            ].map((button) => (
              <TouchableOpacity
                key={button.value}
                style={[
                  styles.segmentButton,
                  selectedPeriod === button.value && styles.selectedSegmentButton
                ]}
                onPress={() => setSelectedPeriod(button.value as any)}
                activeOpacity={0.7}
              >
                <MaterialIcons 
                  name={button.icon as any} 
                  size={20} 
                  color={selectedPeriod === button.value ? colors.onPrimary : colors.onSurfaceVariant}
                />
                <Text style={[
                  styles.segmentButtonText,
                  selectedPeriod === button.value && styles.selectedSegmentButtonText
                ]}>
                  {button.label}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </Surface>
      </View>

      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {/* Key Metrics */}
        <View style={styles.metricsGrid}>
          <MetricCard
            title="Total Revenue"
            value={formatPrice(analyticsData.totalRevenue)}
            icon="attach-money"
            color={colors.success}
            gradient={[colors.success + '20', colors.success + '05']}
          />
          <MetricCard
            title="Total Bookings"
            value={analyticsData.totalReservations.toString()}
            icon="event"
            color={colors.primary}
            gradient={[colors.primary + '20', colors.primary + '05']}
          />
          <MetricCard
            title="Avg. Booking Value"
            value={formatPrice(Math.round(analyticsData.averageBookingValue))}
            icon="trending-up"
            color={colors.info}
            gradient={[colors.info + '20', colors.info + '05']}
          />
          <MetricCard
            title="Occupancy Rate"
            value={`${analyticsData.occupancyRate.toFixed(1)}%`}
            icon="hotel"
            color={colors.warning}
            gradient={[colors.warning + '20', colors.warning + '05']}
          />
        </View>

        {/* Additional Metrics */}
        <View style={styles.additionalMetrics}>
          <MetricCard
            title="Cancellation Rate"
            value={`${analyticsData.cancelationRate.toFixed(1)}%`}
            subtitle={analyticsData.cancelationRate < 10 ? 'Excellent' : 'Needs attention'}
            icon="cancel"
            color={analyticsData.cancelationRate < 10 ? colors.success : colors.error}
            gradient={analyticsData.cancelationRate < 10 
              ? [colors.success + '20', colors.success + '05']
              : [colors.error + '20', colors.error + '05']
            }
          />
        </View>

        {/* Revenue Trend Chart */}
        <Card style={styles.chartCard}>
          <Card.Content>
            <Text style={styles.chartTitle}>Revenue Trend (Last 6 Months)</Text>
            {analyticsData.monthlyRevenue.length > 0 ? (
              <LineChart
                data={{
                  labels: analyticsData.monthlyRevenue.map(item => item.month),
                  datasets: [{
                    data: analyticsData.monthlyRevenue.map(item => Math.max(item.revenue, 0)),
                  }],
                }}
                width={screenWidth - 60}
                height={220}
                chartConfig={chartConfig}
                bezier
                style={styles.chart}
              />
            ) : (
              <View style={styles.noDataContainer}>
                <Text style={styles.noDataText}>No revenue data available for the selected period</Text>
              </View>
            )}
          </Card.Content>
        </Card>

        {/* Bookings by Status */}
        <Card style={styles.chartCard}>
          <Card.Content>
            <Text style={styles.chartTitle}>Bookings by Status</Text>
            <View style={styles.statusGrid}>
              {analyticsData.bookingsByStatus.map((item, index) => (
                <View key={index} style={styles.statusItem}>
                  <View style={[styles.statusDot, { backgroundColor: item.color }]} />
                  <Text style={styles.statusLabel}>{item.status}</Text>
                  <Text style={styles.statusCount}>{item.count}</Text>
                </View>
              ))}
            </View>
          </Card.Content>
        </Card>

        {/* Top Room Types */}
        <Card style={styles.chartCard}>
          <Card.Content>
            <Text style={styles.chartTitle}>Top Room Types by Revenue</Text>
            <View style={styles.customTable}>
              <View style={styles.tableHeader}>
                <Text style={[styles.tableHeaderText, { flex: 2 }]}>Room Type</Text>
                <Text style={[styles.tableHeaderText, { flex: 1, textAlign: 'center' }]}>Bookings</Text>
                <Text style={[styles.tableHeaderText, { flex: 1, textAlign: 'right' }]}>Revenue</Text>
              </View>

              {analyticsData.topRoomTypes.slice(0, 5).map((item, index) => (
                <View key={index} style={styles.tableRow}>
                  <Text style={[styles.roomTypeText, { flex: 2 }]}>
                    {item.type.charAt(0).toUpperCase() + item.type.slice(1)}
                  </Text>
                  <Text style={[styles.tableText, { flex: 1, textAlign: 'center' }]}>
                    {item.count}
                  </Text>
                  <Text style={[styles.revenueText, { flex: 1, textAlign: 'right' }]}>
                    {formatPrice(item.revenue)}
                  </Text>
                </View>
              ))}
            </View>
          </Card.Content>
        </Card>

        {/* Seasonal Trends */}
        <Card style={styles.chartCard}>
          <Card.Content>
            <Text style={styles.chartTitle}>Seasonal Trends</Text>
            {analyticsData.seasonalTrends.length > 0 ? (
              <BarChart
                data={{
                  labels: analyticsData.seasonalTrends.map(item => item.period),
                  datasets: [{
                    data: analyticsData.seasonalTrends.map(item => Math.max(item.bookings, 0)),
                  }],
                }}
                width={screenWidth - 60}
                height={220}
                chartConfig={chartConfig}
                style={styles.chart}
              />
            ) : (
              <View style={styles.noDataContainer}>
                <Text style={styles.noDataText}>No seasonal data available</Text>
              </View>
            )}
          </Card.Content>
        </Card>

        {/* Performance Insights */}
        <Card style={styles.chartCard}>
          <Card.Content>
            <Text style={styles.chartTitle}>Performance Insights</Text>
            <View style={styles.insightsContainer}>
              <View style={styles.insightItem}>
                <MaterialIcons 
                  name={analyticsData.occupancyRate > 70 ? "trending-up" : "trending-down"} 
                  size={20} 
                  color={analyticsData.occupancyRate > 70 ? colors.success : colors.warning} 
                />
                <Text style={styles.insightText}>
                  Occupancy rate is {analyticsData.occupancyRate > 70 ? 'healthy' : 'below average'}
                </Text>
              </View>
              
              <View style={styles.insightItem}>
                <MaterialIcons 
                  name={analyticsData.cancelationRate < 10 ? "check-circle" : "warning"} 
                  size={20} 
                  color={analyticsData.cancelationRate < 10 ? colors.success : colors.error} 
                />
                <Text style={styles.insightText}>
                  Cancellation rate is {analyticsData.cancelationRate < 10 ? 'excellent' : 'concerning'}
                </Text>
              </View>
              
              <View style={styles.insightItem}>
                <MaterialIcons 
                  name="info" 
                  size={20} 
                  color={colors.info} 
                />
                <Text style={styles.insightText}>
                  Average booking value: {formatPrice(Math.round(analyticsData.averageBookingValue))}
                </Text>
              </View>
            </View>
          </Card.Content>
        </Card>
      </ScrollView>

      {/* Download Modal */}
      <ReportDownloadModal
        visible={showDownloadModal}
        onDismiss={() => setShowDownloadModal(false)}
        reportData={getReportData()}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  headerGradient: {
    paddingBottom: spacing.lg,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingTop: spacing.md,
  },
  headerTextContainer: {
    flex: 1,
  },
  headerTitle: {
    fontSize: typography.sizes.xxl,
    fontWeight: typography.weights.bold as any,
    color: colors.onPrimary,
    marginBottom: spacing.xs,
  },
  headerSubtitle: {
    fontSize: typography.sizes.md,
    color: colors.onPrimary + '80',
    opacity: 0.8,
  },
  downloadButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: 12,
    gap: spacing.xs,
  },
  downloadButtonText: {
    color: colors.onPrimary,
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.medium as any,
  },
  periodSelectorContainer: {
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    marginTop: -spacing.lg,
  },
  periodSelectorSurface: {
    borderRadius: 16,
    overflow: 'hidden',
  },
  segmentedButtons: {
    flexDirection: 'row',
    backgroundColor: 'transparent',
    borderRadius: 12,
    padding: spacing.xs,
  },
  segmentButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: spacing.sm,
    paddingHorizontal: spacing.md,
    borderRadius: 8,
    gap: spacing.xs,
    backgroundColor: 'transparent',
  },
  selectedSegmentButton: {
    backgroundColor: colors.primary,
  },
  segmentButtonText: {
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.medium as any,
    color: colors.onSurfaceVariant,
  },
  selectedSegmentButtonText: {
    color: colors.onPrimary,
  },
  customTable: {
    backgroundColor: colors.surface,
    borderRadius: 12,
    overflow: 'hidden',
  },
  tableHeader: {
    flexDirection: 'row',
    backgroundColor: colors.surfaceVariant + '40',
    paddingVertical: spacing.md,
    paddingHorizontal: spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: colors.outline + '30',
  },
  tableHeaderText: {
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.bold as any,
    color: colors.onSurfaceVariant,
  },
  tableRow: {
    flexDirection: 'row',
    paddingVertical: spacing.md,
    paddingHorizontal: spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: colors.outline + '20',
    alignItems: 'center',
  },
  tableText: {
    fontSize: typography.sizes.sm,
    color: colors.onSurface,
  },
  scrollView: {
    flex: 1,
  },
  metricsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    paddingHorizontal: spacing.lg,
    gap: spacing.md,
    marginTop: spacing.md,
  },
  additionalMetrics: {
    paddingHorizontal: spacing.lg,
    marginTop: spacing.md,
  },
  metricCard: {
    flex: 1,
    minWidth: '45%',
    marginBottom: spacing.md,
    borderRadius: 16,
    overflow: 'hidden',
  },
  metricGradient: {
    borderRadius: 16,
    padding: 1,
  },
  metricSurface: {
    borderRadius: 15,
    backgroundColor: colors.surface,
  },
  metricContent: {
    padding: spacing.lg,
  },
  metricHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.md,
  },
  metricIconContainer: {
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
  },
  metricTextContainer: {
    flex: 1,
  },
  metricValue: {
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.bold as any,
    marginBottom: spacing.xs,
  },
  metricTitle: {
    fontSize: typography.sizes.sm,
    color: colors.onSurfaceVariant,
    fontWeight: typography.weights.medium as any,
  },
  metricSubtitle: {
    fontSize: typography.sizes.xs,
    marginTop: spacing.xs,
    fontWeight: typography.weights.medium as any,
  },
  chartCard: {
    margin: spacing.lg,
    marginTop: spacing.md,
    borderRadius: 20,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
  },
  chartTitle: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.bold as any,
    marginBottom: spacing.md,
    color: colors.onSurface,
  },
  chart: {
    marginVertical: spacing.sm,
    borderRadius: 16,
  },
  statusGrid: {
    gap: spacing.md,
  },
  statusItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: spacing.md,
    paddingHorizontal: spacing.lg,
    backgroundColor: colors.surfaceVariant + '30',
    borderRadius: 12,
    marginBottom: spacing.sm,
  },
  statusDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: spacing.sm,
  },
  statusLabel: {
    flex: 1,
    fontSize: typography.sizes.md,
    color: colors.onSurface,
    fontWeight: typography.weights.medium as any,
  },
  statusCount: {
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.bold as any,
    color: colors.primary,
  },
  roomTypeText: {
    fontSize: typography.sizes.md,
    color: colors.onSurface,
    fontWeight: typography.weights.medium as any,
  },
  revenueText: {
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.bold as any,
    color: colors.success,
  },
  insightsContainer: {
    gap: spacing.md,
  },
  insightItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.sm,
    padding: spacing.md,
    backgroundColor: colors.surfaceVariant + '20',
    borderRadius: 12,
    borderLeftWidth: 4,
    borderLeftColor: colors.primary,
  },
  insightText: {
    fontSize: typography.sizes.md,
    color: colors.onSurface,
    flex: 1,
    fontWeight: typography.weights.medium as any,
  },
  noDataContainer: {
    padding: spacing.xl,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 120,
    backgroundColor: colors.surface,
    borderRadius: 16,
    margin: spacing.lg,
  },
  noDataText: {
    fontSize: typography.sizes.md,
    color: colors.onSurfaceVariant,
    textAlign: 'center',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: spacing.xl,
    backgroundColor: colors.background,
  },
  loadingText: {
    fontSize: typography.sizes.md,
    color: colors.onSurfaceVariant,
    marginTop: spacing.md,
    textAlign: 'center',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: spacing.xl,
    backgroundColor: colors.background,
  },
  errorText: {
    fontSize: typography.sizes.md,
    color: colors.error,
    textAlign: 'center',
    marginBottom: spacing.lg,
  },
  retryButton: {
    marginTop: spacing.md,
    borderRadius: 12,
  },
});
