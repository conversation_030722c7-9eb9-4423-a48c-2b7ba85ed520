import type { Reservation, Room } from '../types';

// Sample data for testing analytics when no real data is available
export const generateSampleReservations = (): Reservation[] => {
  const sampleReservations: Reservation[] = [];
  const statuses = ['confirmed', 'pending', 'checked_in', 'checked_out', 'cancelled'];
  const paymentStatuses = ['paid', 'pending', 'failed'];
  
  // Generate reservations for the last 6 months
  const now = new Date();
  for (let i = 0; i < 50; i++) {
    const daysAgo = Math.floor(Math.random() * 180); // Last 6 months
    const createdDate = new Date(now.getTime() - daysAgo * 24 * 60 * 60 * 1000);
    const checkInDate = new Date(createdDate.getTime() + Math.random() * 30 * 24 * 60 * 60 * 1000);
    const checkOutDate = new Date(checkInDate.getTime() + (1 + Math.random() * 7) * 24 * 60 * 60 * 1000);
    
    const reservation: Reservation = {
      id: `sample-${i + 1}`,
      guest_id: `guest-${i + 1}`,
      room_id: `room-${Math.floor(Math.random() * 10) + 1}`,
      check_in_date: checkInDate.toISOString().split('T')[0],
      check_out_date: checkOutDate.toISOString().split('T')[0],
      check_in: checkInDate.toISOString().split('T')[0],
      check_out: checkOutDate.toISOString().split('T')[0],
      total_amount: 5000 + Math.random() * 15000, // KES 5,000 - 20,000
      guests: Math.floor(Math.random() * 4) + 1,
      room_number: `${Math.floor(Math.random() * 3) + 1}0${Math.floor(Math.random() * 10) + 1}`,
      special_requests: Math.random() > 0.7 ? 'Early check-in requested' : undefined,
      status: statuses[Math.floor(Math.random() * statuses.length)] as any,
      payment_status: paymentStatuses[Math.floor(Math.random() * paymentStatuses.length)] as any,
      created_at: createdDate.toISOString(),
      updated_at: createdDate.toISOString(),
      guest_name: `Guest ${i + 1}`,
      guest_email: `guest${i + 1}@example.com`,
      guest_phone: `+254700${String(i + 1).padStart(6, '0')}`,
    };
    
    sampleReservations.push(reservation);
  }
  
  return sampleReservations;
};

export const generateSampleRooms = (): Room[] => {
  const roomTypes = ['standard', 'deluxe', 'suite', 'presidential'];
  const statuses = ['available', 'booked', 'maintenance', 'cleaning'];
  const amenities = [
    ['WiFi', 'Air Conditioning', 'TV'],
    ['WiFi', 'Air Conditioning', 'TV', 'Mini Bar', 'Balcony'],
    ['WiFi', 'Air Conditioning', 'TV', 'Mini Bar', 'Balcony', 'Ocean View', 'Jacuzzi'],
    ['WiFi', 'Air Conditioning', 'TV', 'Mini Bar', 'Balcony', 'Ocean View', 'Jacuzzi', 'Kitchenette', 'Work Desk'],
  ];
  
  const sampleRooms: Room[] = [];
  
  for (let i = 0; i < 20; i++) {
    const roomTypeIndex = Math.floor(Math.random() * roomTypes.length);
    const roomType = roomTypes[roomTypeIndex] as any;
    
    const room: Room = {
      id: `room-${i + 1}`,
      room_number: `${Math.floor(i / 10) + 1}0${(i % 10) + 1}`,
      room_type: roomType,
      type: roomType,
      price_per_night: 3000 + roomTypeIndex * 2000 + Math.random() * 1000, // KES 3,000 - 10,000
      description: `Beautiful ${roomType} room with modern amenities`,
      amenities: amenities[roomTypeIndex],
      max_occupancy: roomTypeIndex + 2,
      bed_type: ['Double Bed', 'Queen Bed', 'King Bed', 'King Bed'][roomTypeIndex],
      size_sqm: 25 + roomTypeIndex * 15,
      images: [],
      status: statuses[Math.floor(Math.random() * statuses.length)] as any,
      is_available: Math.random() > 0.3,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };
    
    sampleRooms.push(room);
  }
  
  return sampleRooms;
};

// Check if we should use sample data (when no real data is available)
export const shouldUseSampleData = (reservations: Reservation[], rooms: Room[]): boolean => {
  return reservations.length === 0 && rooms.length === 0;
};
