-- Sample data for Sunset View Hotel

-- Insert sample rooms (prices in Kenyan Shillings)
INSERT INTO public.rooms (room_number, room_type, price_per_night, description, amenities, max_occupancy, status) VALUES
('101', 'standard', 8500.00, 'Comfortable standard room with city view. Perfect for business travelers and short stays.',
 ARRAY['WiFi', 'Air Conditioning', 'TV', 'Safe'], 2, 'available'),

('102', 'standard', 8500.00, 'Cozy standard room with modern amenities and comfortable bedding.',
 ARRAY['WiFi', 'Air Conditioning', 'TV', 'Safe', 'Mini Bar'], 2, 'available'),

('201', 'deluxe', 12500.00, 'Spacious deluxe room with premium furnishings and enhanced amenities.',
 ARRAY['WiFi', 'Air Conditioning', 'TV', 'Mini Bar', 'Room Service', 'Balcony'], 3, 'available'),

('202', 'deluxe', 12500.00, 'Elegant deluxe room featuring a work area and upgraded bathroom facilities.',
 ARRAY['WiFi', 'Air Conditioning', 'TV', 'Mini Bar', 'Room Service', 'Workspace', 'Safe'], 3, 'available'),

('301', 'suite', 22000.00, 'Luxurious suite with separate living area and stunning ocean views.',
 ARRAY['WiFi', 'Air Conditioning', 'TV', 'Mini Bar', 'Room Service', 'Ocean View', 'Balcony', 'Kitchen', 'Safe'], 4, 'available'),

('302', 'suite', 22000.00, 'Premium suite with modern kitchen facilities and spacious living room.',
 ARRAY['WiFi', 'Air Conditioning', 'TV', 'Mini Bar', 'Room Service', 'Kitchen', 'Workspace', 'Safe', 'Laundry Service'], 4, 'available'),

('401', 'presidential', 45000.00, 'Ultimate luxury presidential suite with exclusive services and panoramic views.',
 ARRAY['WiFi', 'Air Conditioning', 'TV', 'Mini Bar', 'Room Service', 'Ocean View', 'Jacuzzi', 'Kitchen', 'Workspace', 'Safe', 'Laundry Service'], 6, 'available'),

('103', 'standard', 8500.00, 'Standard room currently under maintenance for upgrades.',
 ARRAY['WiFi', 'Air Conditioning', 'TV'], 2, 'maintenance'),

('203', 'deluxe', 12500.00, 'Deluxe room being prepared for incoming guests.',
 ARRAY['WiFi', 'Air Conditioning', 'TV', 'Mini Bar', 'Room Service'], 3, 'cleaning');

-- Note: Sample users, reservations, and payments would typically be created through the application
-- as they require proper authentication and user creation flow.

-- You can manually create test users through Supabase Auth dashboard and then create sample reservations:

-- Example reservation data (to be inserted after creating test users):
/*
INSERT INTO public.reservations (guest_id, room_id, check_in_date, check_out_date, total_amount, status, payment_status) VALUES
('user-uuid-here', (SELECT id FROM public.rooms WHERE room_number = '101'), '2024-02-01', '2024-02-03', 17000.00, 'confirmed', 'paid'),
('user-uuid-here', (SELECT id FROM public.rooms WHERE room_number = '201'), '2024-02-05', '2024-02-07', 25000.00, 'pending', 'pending');
*/

-- Create a function to get available rooms for a date range
CREATE OR REPLACE FUNCTION get_available_rooms(
    check_in_date DATE,
    check_out_date DATE,
    room_type_filter room_type DEFAULT NULL
)
RETURNS TABLE (
    id UUID,
    room_number TEXT,
    room_type room_type,
    price_per_night DECIMAL,
    description TEXT,
    amenities TEXT[],
    max_occupancy INTEGER,
    images TEXT[]
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        r.id,
        r.room_number,
        r.room_type,
        r.price_per_night,
        r.description,
        r.amenities,
        r.max_occupancy,
        r.images
    FROM public.rooms r
    WHERE 
        r.status = 'available'
        AND (room_type_filter IS NULL OR r.room_type = room_type_filter)
        AND NOT EXISTS (
            SELECT 1 
            FROM public.reservations res
            WHERE 
                res.room_id = r.id
                AND res.status IN ('confirmed', 'checked_in')
                AND (
                    (res.check_in_date <= check_out_date AND res.check_out_date >= check_in_date)
                )
        )
    ORDER BY r.room_type, r.room_number;
END;
$$ LANGUAGE plpgsql;

-- Create a function to calculate total revenue
CREATE OR REPLACE FUNCTION get_revenue_stats(
    start_date DATE DEFAULT NULL,
    end_date DATE DEFAULT NULL
)
RETURNS TABLE (
    total_revenue DECIMAL,
    total_bookings BIGINT,
    average_booking_value DECIMAL
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        COALESCE(SUM(r.total_amount), 0) as total_revenue,
        COUNT(r.id) as total_bookings,
        COALESCE(AVG(r.total_amount), 0) as average_booking_value
    FROM public.reservations r
    WHERE 
        r.payment_status = 'paid'
        AND (start_date IS NULL OR r.check_in_date >= start_date)
        AND (end_date IS NULL OR r.check_in_date <= end_date);
END;
$$ LANGUAGE plpgsql;

-- Create a function to get occupancy rate
CREATE OR REPLACE FUNCTION get_occupancy_rate(
    start_date DATE,
    end_date DATE
)
RETURNS DECIMAL AS $$
DECLARE
    total_room_nights DECIMAL;
    booked_room_nights DECIMAL;
    total_rooms INTEGER;
    total_days INTEGER;
BEGIN
    -- Get total number of rooms
    SELECT COUNT(*) INTO total_rooms FROM public.rooms WHERE status = 'available';
    
    -- Calculate total days in the period
    SELECT (end_date - start_date + 1) INTO total_days;
    
    -- Calculate total possible room nights
    total_room_nights := total_rooms * total_days;
    
    -- Calculate booked room nights
    SELECT 
        COALESCE(SUM(
            LEAST(r.check_out_date, end_date) - GREATEST(r.check_in_date, start_date) + 1
        ), 0)
    INTO booked_room_nights
    FROM public.reservations r
    WHERE 
        r.status IN ('confirmed', 'checked_in', 'checked_out')
        AND r.check_in_date <= end_date
        AND r.check_out_date >= start_date;
    
    -- Return occupancy rate as percentage
    IF total_room_nights > 0 THEN
        RETURN ROUND((booked_room_nights / total_room_nights) * 100, 2);
    ELSE
        RETURN 0;
    END IF;
END;
$$ LANGUAGE plpgsql;
