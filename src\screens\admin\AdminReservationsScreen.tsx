import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  RefreshControl,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import {
  Card,
  Title,
  Button,
  Searchbar,
  Chip,
  Menu,
  Badge,
  Avatar,
  Divider,
  Portal,
  Dialog,
  Paragraph,
} from 'react-native-paper';
import { MaterialIcons } from '@expo/vector-icons';
import { colors, spacing, typography } from '../../constants';
import { useReservationStore } from '../../store/reservationStore';
import { useRoomStore } from '../../store/roomStore';
import { formatPrice } from '../../utils/currency';
import { getInitials, formatStatusText, formatRoomType, safeIncludes, capitalizeString } from '../../utils/stringUtils';
import { Reservation, ReservationStatus } from '../../types/database';
// import { ReservationDebugger } from '../../components/debug/ReservationDebugger';
import { useAuthStore } from '../../store/authStore';
import { usePermissions } from '../../hooks/usePermissions';

export const AdminReservationsScreen = ({ navigation }: any) => {
  const {
    reservations,
    loading,
    error,
    fetchAllReservations,
    updateReservation,
    cancelReservation
  } = useReservationStore();
  const { rooms, fetchRooms } = useRoomStore();
  const { user } = useAuthStore();
  const permissions = usePermissions();

  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedStatus, setSelectedStatus] = useState<ReservationStatus | 'all'>('all');
  const [sortMenuVisible, setSortMenuVisible] = useState(false);
  const [sortBy, setSortBy] = useState<'date' | 'amount' | 'guest'>('date');
  const [selectedReservation, setSelectedReservation] = useState<Reservation | null>(null);
  const [dialogVisible, setDialogVisible] = useState(false);
  const [actionType, setActionType] = useState<'confirm' | 'cancel' | 'checkin' | 'checkout'>('confirm');

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      console.log('🔄 AdminReservationsScreen: Loading data...');
      console.log('🔄 Current user:', user);
      console.log('🔄 Current permissions:', permissions);

      await Promise.all([fetchAllReservations(), fetchRooms()]);

      console.log('✅ AdminReservationsScreen: Data loaded');
      console.log('📊 Reservations count:', reservations.length);
      console.log('🏨 Rooms count:', rooms.length);
    } catch (error) {
      console.error('❌ Error loading reservations:', error);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadData();
    setRefreshing(false);
  };

  const filteredAndSortedReservations = reservations
    .filter(reservation => {
      // Handle null/undefined values safely
      const guestName = reservation.guest_name || '';
      const guestEmail = reservation.guest_email || '';
      const reservationId = reservation.id || '';

      const matchesSearch = !searchQuery ||
        safeIncludes(guestName, searchQuery) ||
        safeIncludes(guestEmail, searchQuery) ||
        safeIncludes(reservationId, searchQuery);
      const matchesStatus = selectedStatus === 'all' || reservation.status === selectedStatus;

      return matchesSearch && matchesStatus;
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'date':
          return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
        case 'amount':
          return b.total_amount - a.total_amount;
        case 'guest':
          const nameA = a.guest_name || '';
          const nameB = b.guest_name || '';
          return nameA.localeCompare(nameB);
        default:
          return 0;
      }
    });

  const getRoomInfo = (roomId: string) => {
    return rooms.find(room => room.id === roomId);
  };

  const getStatusColor = (status: ReservationStatus) => {
    switch (status) {
      case 'confirmed': return colors.success;
      case 'pending': return colors.warning;
      case 'cancelled': return colors.error;
      case 'checked_in': return colors.info;
      case 'checked_out': return colors.onSurfaceVariant;
      default: return colors.onSurfaceVariant;
    }
  };

  const getStatusIcon = (status: ReservationStatus) => {
    switch (status) {
      case 'confirmed': return 'check-circle';
      case 'pending': return 'schedule';
      case 'cancelled': return 'cancel';
      case 'checked_in': return 'login';
      case 'checked_out': return 'logout';
      default: return 'help';
    }
  };

  const handleAction = (reservation: Reservation, action: typeof actionType) => {
    setSelectedReservation(reservation);
    setActionType(action);
    setDialogVisible(true);
  };

  const confirmAction = async () => {
    if (!selectedReservation) return;

    try {
      switch (actionType) {
        case 'confirm':
          await updateReservation(selectedReservation.id, { status: 'confirmed' });
          break;
        case 'checkin':
          await updateReservation(selectedReservation.id, { status: 'checked_in' });
          break;
        case 'checkout':
          await updateReservation(selectedReservation.id, { status: 'checked_out' });
          break;
        case 'cancel':
          await cancelReservation(selectedReservation.id);
          break;
      }
      
      setDialogVisible(false);
      setSelectedReservation(null);
      Alert.alert('Success', `Reservation ${actionType}ed successfully`);
    } catch (error) {
      Alert.alert('Error', `Failed to ${actionType} reservation`);
    }
  };

  const getActionDialogContent = () => {
    switch (actionType) {
      case 'confirm':
        return {
          title: 'Confirm Reservation',
          content: 'Are you sure you want to confirm this reservation?',
        };
      case 'checkin':
        return {
          title: 'Check-in Guest',
          content: 'Mark this reservation as checked-in?',
        };
      case 'checkout':
        return {
          title: 'Check-out Guest',
          content: 'Mark this reservation as checked-out?',
        };
      case 'cancel':
        return {
          title: 'Cancel Reservation',
          content: 'Are you sure you want to cancel this reservation? This action cannot be undone.',
        };
    }
  };

  const reservationStatuses: (ReservationStatus | 'all')[] = [
    'all', 'pending', 'confirmed', 'checked_in', 'checked_out', 'cancelled'
  ];

  const getAvailableActions = (status: ReservationStatus) => {
    switch (status) {
      case 'pending':
        return ['confirm', 'cancel'];
      case 'confirmed':
        return ['checkin', 'cancel'];
      case 'checked_in':
        return ['checkout'];
      default:
        return [];
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Debug Component - Temporarily disabled */}
      {/* <ReservationDebugger visible={true} /> */}

      {/* Search and Filters */}
      <View style={styles.filtersContainer}>
        <Searchbar
          placeholder="Search by guest name, email, or ID..."
          onChangeText={setSearchQuery}
          value={searchQuery}
          style={styles.searchbar}
        />
        
        <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.filtersRow}>
          <View style={styles.filterGroup}>
            {reservationStatuses.map((status) => (
              <Chip
                key={status}
                selected={selectedStatus === status}
                onPress={() => setSelectedStatus(status)}
                style={[
                  styles.filterChip,
                  selectedStatus === status && styles.selectedFilterChip
                ]}
                textStyle={[
                  styles.filterChipText,
                  selectedStatus === status && styles.selectedFilterChipText
                ]}
              >
                {status === 'all' ? 'All' : formatStatusText(status)}
              </Chip>
            ))}
          </View>
        </ScrollView>

        {/* Sort Menu */}
        <View style={styles.sortContainer}>
          <Menu
            visible={sortMenuVisible}
            onDismiss={() => setSortMenuVisible(false)}
            anchor={
              <Button
                mode="outlined"
                onPress={() => setSortMenuVisible(true)}
                icon="sort"
                compact
              >
                Sort by {sortBy}
              </Button>
            }
          >
            <Menu.Item
              onPress={() => {
                setSortBy('date');
                setSortMenuVisible(false);
              }}
              title="Date"
            />
            <Menu.Item
              onPress={() => {
                setSortBy('amount');
                setSortMenuVisible(false);
              }}
              title="Amount"
            />
            <Menu.Item
              onPress={() => {
                setSortBy('guest');
                setSortMenuVisible(false);
              }}
              title="Guest Name"
            />
          </Menu>
        </View>
      </View>

      {/* Results Header */}
      <View style={styles.resultsHeader}>
        <Text style={styles.resultsText}>
          {filteredAndSortedReservations.length} reservation{filteredAndSortedReservations.length !== 1 ? 's' : ''} found
        </Text>
      </View>

      {/* Reservations List */}
      <ScrollView
        style={styles.reservationsList}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {loading && (
          <Card style={styles.reservationCard}>
            <Card.Content>
              <Text style={styles.emptyText}>Loading reservations...</Text>
            </Card.Content>
          </Card>
        )}

        {!loading && error && (
          <Card style={styles.reservationCard}>
            <Card.Content>
              <Text style={styles.errorText}>Error: {error}</Text>
              <Button mode="outlined" onPress={loadData} style={{ marginTop: spacing.sm }}>
                Retry
              </Button>
            </Card.Content>
          </Card>
        )}

        {!loading && !error && filteredAndSortedReservations.length === 0 && (
          <Card style={styles.reservationCard}>
            <Card.Content>
              <Text style={styles.emptyText}>
                No reservations found.
                {reservations.length === 0 ? ' The database appears to be empty.' : ' Try adjusting your filters.'}
              </Text>
              <Text style={styles.debugText}>
                Total reservations in store: {reservations.length}
              </Text>
            </Card.Content>
          </Card>
        )}

        {filteredAndSortedReservations.map((reservation) => {
          const room = getRoomInfo(reservation.room_id);
          const availableActions = getAvailableActions(reservation.status);
          
          return (
            <Card key={reservation.id} style={styles.reservationCard}>
              <Card.Content>
                {/* Header */}
                <View style={styles.reservationHeader}>
                  <View style={styles.guestInfo}>
                    <Avatar.Text
                      size={40}
                      label={getInitials(reservation.guest_name || 'Guest')}
                      style={{ backgroundColor: colors.primary }}
                    />
                    <View style={styles.guestDetails}>
                      <Text style={styles.guestName}>{reservation.guest_name || 'Unknown Guest'}</Text>
                      <Text style={styles.guestEmail}>{reservation.guest_email || 'No email'}</Text>
                      <Text style={styles.reservationId}>ID: {reservation.id.slice(0, 8)}</Text>
                    </View>
                  </View>
                  
                  <Badge
                    style={[
                      styles.statusBadge,
                      { backgroundColor: getStatusColor(reservation.status) }
                    ]}
                  >
                    {formatStatusText(reservation.status).toUpperCase()}
                  </Badge>
                </View>

                <Divider style={styles.divider} />

                {/* Reservation Details */}
                <View style={styles.reservationDetails}>
                  <View style={styles.detailRow}>
                    <MaterialIcons name="hotel" size={16} color={colors.onSurfaceVariant} />
                    <Text style={styles.detailText}>
                      Room {room?.room_number || 'N/A'} - {formatRoomType(room?.type)}
                    </Text>
                  </View>
                  
                  <View style={styles.detailRow}>
                    <MaterialIcons name="calendar-today" size={16} color={colors.onSurfaceVariant} />
                    <Text style={styles.detailText}>
                      {new Date(reservation.check_in_date).toLocaleDateString()} - {new Date(reservation.check_out_date).toLocaleDateString()}
                    </Text>
                  </View>
                  
                  <View style={styles.detailRow}>
                    <MaterialIcons name="people" size={16} color={colors.onSurfaceVariant} />
                    <Text style={styles.detailText}>
                      {reservation.guests || 1} Guest{(reservation.guests || 1) !== 1 ? 's' : ''}
                    </Text>
                  </View>
                  
                  <View style={styles.detailRow}>
                    <MaterialIcons name="attach-money" size={16} color={colors.onSurfaceVariant} />
                    <Text style={styles.totalAmount}>{formatPrice(reservation.total_amount)}</Text>
                  </View>
                  
                  <View style={styles.detailRow}>
                    <MaterialIcons name="schedule" size={16} color={colors.onSurfaceVariant} />
                    <Text style={styles.detailText}>
                      Booked {new Date(reservation.created_at).toLocaleDateString()}
                    </Text>
                  </View>
                </View>

                {/* Special Requests */}
                {reservation.special_requests && (
                  <>
                    <Divider style={styles.divider} />
                    <View style={styles.specialRequests}>
                      <Text style={styles.specialRequestsTitle}>Special Requests:</Text>
                      <Text style={styles.specialRequestsText}>{reservation.special_requests}</Text>
                    </View>
                  </>
                )}

                {/* Actions */}
                {availableActions.length > 0 && (
                  <>
                    <Divider style={styles.divider} />
                    <View style={styles.actions}>
                      {availableActions.map((action) => (
                        <Button
                          key={action}
                          mode={action === 'cancel' ? 'outlined' : 'contained'}
                          onPress={() => handleAction(reservation, action as any)}
                          icon={getStatusIcon(action as ReservationStatus)}
                          compact
                          style={styles.actionButton}
                          buttonColor={action === 'cancel' ? undefined : colors.primary}
                          textColor={action === 'cancel' ? colors.error : undefined}
                        >
                          {capitalizeString(action)}
                        </Button>
                      ))}
                    </View>
                  </>
                )}
              </Card.Content>
            </Card>
          );
        })}

        {filteredAndSortedReservations.length === 0 && !loading && (
          <View style={styles.emptyState}>
            <MaterialIcons name="event-busy" size={64} color={colors.onSurfaceVariant} />
            <Text style={styles.emptyStateText}>No reservations found</Text>
            <Text style={styles.emptyStateSubtext}>
              Try adjusting your search or filters
            </Text>
          </View>
        )}
      </ScrollView>

      {/* Action Confirmation Dialog */}
      <Portal>
        <Dialog visible={dialogVisible} onDismiss={() => setDialogVisible(false)}>
          <Dialog.Title>{getActionDialogContent().title}</Dialog.Title>
          <Dialog.Content>
            <Paragraph>{getActionDialogContent().content}</Paragraph>
          </Dialog.Content>
          <Dialog.Actions>
            <Button onPress={() => setDialogVisible(false)}>Cancel</Button>
            <Button 
              onPress={confirmAction}
              textColor={actionType === 'cancel' ? colors.error : colors.primary}
            >
              {capitalizeString(actionType)}
            </Button>
          </Dialog.Actions>
        </Dialog>
      </Portal>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  filtersContainer: {
    padding: spacing.lg,
    paddingBottom: spacing.md,
  },
  searchbar: {
    marginBottom: spacing.md,
  },
  filtersRow: {
    marginBottom: spacing.sm,
  },
  filterGroup: {
    flexDirection: 'row',
    gap: spacing.sm,
  },
  filterChip: {
    marginRight: spacing.sm,
    backgroundColor: colors.surfaceVariant,
    borderWidth: 1,
    borderColor: colors.outline || colors.border,
  },
  selectedFilterChip: {
    backgroundColor: colors.primary,
    borderColor: colors.primary,
  },
  filterChipText: {
    color: colors.onSurfaceVariant,
    fontSize: typography.sizes.sm,
  },
  selectedFilterChipText: {
    color: colors.onPrimary || colors.surface,
    fontWeight: typography.weights.medium as any,
  },
  sortContainer: {
    alignItems: 'flex-end',
    marginTop: spacing.sm,
  },
  resultsHeader: {
    paddingHorizontal: spacing.lg,
    paddingBottom: spacing.md,
  },
  resultsText: {
    fontSize: typography.sizes.sm,
    color: colors.onSurfaceVariant,
  },
  reservationsList: {
    flex: 1,
    paddingHorizontal: spacing.lg,
  },
  reservationCard: {
    marginBottom: spacing.md,
  },
  reservationHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: spacing.md,
  },
  guestInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  guestDetails: {
    marginLeft: spacing.md,
    flex: 1,
  },
  guestName: {
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.semibold as any,
    color: colors.onSurface,
  },
  guestEmail: {
    fontSize: typography.sizes.sm,
    color: colors.onSurfaceVariant,
  },
  reservationId: {
    fontSize: typography.sizes.xs,
    color: colors.onSurfaceVariant,
  },
  statusBadge: {
    alignSelf: 'flex-start',
  },
  divider: {
    marginVertical: spacing.md,
  },
  reservationDetails: {
    gap: spacing.sm,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.sm,
  },
  detailText: {
    fontSize: typography.sizes.sm,
    color: colors.onSurface,
  },
  totalAmount: {
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.semibold as any,
    color: colors.primary,
  },
  specialRequests: {
    marginTop: spacing.sm,
  },
  specialRequestsTitle: {
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.medium as any,
    color: colors.onSurface,
    marginBottom: spacing.xs,
  },
  specialRequestsText: {
    fontSize: typography.sizes.sm,
    color: colors.onSurfaceVariant,
    fontStyle: 'italic',
  },
  actions: {
    flexDirection: 'row',
    gap: spacing.sm,
    justifyContent: 'flex-end',
    flexWrap: 'wrap',
  },
  actionButton: {
    minWidth: 100,
  },
  emptyState: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: spacing.xxl,
  },
  emptyStateText: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.semibold as any,
    color: colors.onSurfaceVariant,
    marginTop: spacing.md,
  },
  emptyStateSubtext: {
    fontSize: typography.sizes.sm,
    color: colors.onSurfaceVariant,
    marginTop: spacing.xs,
  },
  emptyText: {
    fontSize: typography.sizes.md,
    color: colors.onSurfaceVariant,
    textAlign: 'center',
    marginVertical: spacing.md,
  },
  errorText: {
    fontSize: typography.sizes.md,
    color: colors.error,
    textAlign: 'center',
    marginVertical: spacing.md,
  },
  debugText: {
    fontSize: typography.sizes.sm,
    color: colors.onSurfaceVariant,
    textAlign: 'center',
    marginTop: spacing.sm,
    fontStyle: 'italic',
  },
});
