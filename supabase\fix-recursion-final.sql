-- FINAL FIX FOR INFINITE RECURSION IN SUPABASE RLS POLICIES
-- Run this script in Supabase SQL Editor to completely fix the recursion issue

-- ============================================================================
-- STEP 1: Drop ALL existing policies to start fresh
-- ============================================================================

-- Drop all users table policies
DROP POLICY IF EXISTS "Users can view their own profile" ON public.users;
DROP POLICY IF EXISTS "Users can update their own profile" ON public.users;
DROP POLICY IF EXISTS "Enable insert for authenticated users" ON public.users;
DROP POLICY IF EXISTS "Enable insert for service role and authenticated users" ON public.users;
DROP POLICY IF EXISTS "Admins can view all users" ON public.users;
DROP POLICY IF EXISTS "Admins can manage all users" ON public.users;
DROP POLICY IF EXISTS "Admins can view all users via function" ON public.users;
DROP POLICY IF EXISTS "Admins can manage all users via function" ON public.users;

-- Drop problematic functions that cause recursion
DROP FUNCTION IF EXISTS public.get_user_role(UUID);

-- Drop other table policies
DROP POLICY IF EXISTS "Anyone can view available rooms" ON public.rooms;
DROP POLICY IF EXISTS "Only admins can manage rooms" ON public.rooms;
DROP POLICY IF EXISTS "Admins can manage rooms via function" ON public.rooms;
DROP POLICY IF EXISTS "Authenticated users can manage rooms" ON public.rooms;

DROP POLICY IF EXISTS "Users can view their own reservations" ON public.reservations;
DROP POLICY IF EXISTS "Users can create their own reservations" ON public.reservations;
DROP POLICY IF EXISTS "Users can update their own reservations" ON public.reservations;
DROP POLICY IF EXISTS "Staff can view all reservations" ON public.reservations;
DROP POLICY IF EXISTS "Staff can manage all reservations" ON public.reservations;
DROP POLICY IF EXISTS "Staff can view all reservations via function" ON public.reservations;
DROP POLICY IF EXISTS "Staff can manage all reservations via function" ON public.reservations;
DROP POLICY IF EXISTS "Authenticated users can view all reservations" ON public.reservations;
DROP POLICY IF EXISTS "Authenticated users can manage all reservations" ON public.reservations;

DROP POLICY IF EXISTS "Users can view their own payments" ON public.payments;
DROP POLICY IF EXISTS "Staff can view all payments" ON public.payments;
DROP POLICY IF EXISTS "Staff can view all payments via function" ON public.payments;
DROP POLICY IF EXISTS "Authenticated users can view all payments" ON public.payments;
DROP POLICY IF EXISTS "System can manage payments" ON public.payments;

-- Drop storage policies
DROP POLICY IF EXISTS "Anyone can view room images" ON storage.objects;
DROP POLICY IF EXISTS "Admins can upload room images" ON storage.objects;
DROP POLICY IF EXISTS "Admins can update room images" ON storage.objects;
DROP POLICY IF EXISTS "Admins can delete room images" ON storage.objects;
DROP POLICY IF EXISTS "Admins can upload room images via function" ON storage.objects;
DROP POLICY IF EXISTS "Admins can update room images via function" ON storage.objects;
DROP POLICY IF EXISTS "Admins can delete room images via function" ON storage.objects;
DROP POLICY IF EXISTS "Authenticated users can upload room images" ON storage.objects;
DROP POLICY IF EXISTS "Authenticated users can update room images" ON storage.objects;
DROP POLICY IF EXISTS "Authenticated users can delete room images" ON storage.objects;

-- ============================================================================
-- STEP 2: Create SIMPLE, NON-RECURSIVE policies
-- ============================================================================

-- USERS TABLE: Only basic self-access policies (NO ADMIN POLICIES)
CREATE POLICY "users_select_own" ON public.users
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "users_update_own" ON public.users
    FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "users_insert_authenticated" ON public.users
    FOR INSERT WITH CHECK (auth.role() = 'authenticated');

-- ROOMS TABLE: Public read, authenticated write
CREATE POLICY "rooms_select_all" ON public.rooms
    FOR SELECT USING (true);

CREATE POLICY "rooms_all_authenticated" ON public.rooms
    FOR ALL USING (auth.role() = 'authenticated');

-- RESERVATIONS TABLE: User owns their reservations, authenticated can see all
CREATE POLICY "reservations_select_own" ON public.reservations
    FOR SELECT USING (auth.uid() = guest_id);

CREATE POLICY "reservations_insert_own" ON public.reservations
    FOR INSERT WITH CHECK (auth.uid() = guest_id);

CREATE POLICY "reservations_update_own" ON public.reservations
    FOR UPDATE USING (auth.uid() = guest_id);

CREATE POLICY "reservations_select_all_authenticated" ON public.reservations
    FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "reservations_all_authenticated" ON public.reservations
    FOR ALL USING (auth.role() = 'authenticated');

-- PAYMENTS TABLE: Users see their own, authenticated see all
CREATE POLICY "payments_select_own" ON public.payments
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.reservations 
            WHERE id = reservation_id AND guest_id = auth.uid()
        )
    );

CREATE POLICY "payments_select_all_authenticated" ON public.payments
    FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "payments_all_system" ON public.payments
    FOR ALL USING (true);

-- STORAGE: Public read, authenticated write for room images
CREATE POLICY "storage_select_room_images" ON storage.objects
    FOR SELECT USING (bucket_id = 'room-images');

CREATE POLICY "storage_insert_room_images" ON storage.objects
    FOR INSERT WITH CHECK (
        bucket_id = 'room-images' AND 
        auth.role() = 'authenticated'
    );

CREATE POLICY "storage_update_room_images" ON storage.objects
    FOR UPDATE USING (
        bucket_id = 'room-images' AND 
        auth.role() = 'authenticated'
    );

CREATE POLICY "storage_delete_room_images" ON storage.objects
    FOR DELETE USING (
        bucket_id = 'room-images' AND 
        auth.role() = 'authenticated'
    );

-- ============================================================================
-- STEP 3: Create helper functions (NO RLS DEPENDENCIES)
-- ============================================================================

-- Function to get user role from auth metadata (avoids RLS)
CREATE OR REPLACE FUNCTION public.get_current_user_role()
RETURNS TEXT
LANGUAGE sql
SECURITY DEFINER
AS $$
    SELECT COALESCE(
        (auth.jwt() -> 'user_metadata' ->> 'role'),
        'guest'
    );
$$;

GRANT EXECUTE ON FUNCTION public.get_current_user_role() TO authenticated;

-- Function to check if current user is admin
CREATE OR REPLACE FUNCTION public.is_admin()
RETURNS BOOLEAN
LANGUAGE sql
SECURITY DEFINER
AS $$
    SELECT (auth.jwt() -> 'user_metadata' ->> 'role') = 'admin';
$$;

GRANT EXECUTE ON FUNCTION public.is_admin() TO authenticated;

-- Function to check if current user is staff (admin or receptionist)
CREATE OR REPLACE FUNCTION public.is_staff()
RETURNS BOOLEAN
LANGUAGE sql
SECURITY DEFINER
AS $$
    SELECT (auth.jwt() -> 'user_metadata' ->> 'role') IN ('admin', 'receptionist');
$$;

GRANT EXECUTE ON FUNCTION public.is_staff() TO authenticated;

-- ============================================================================
-- STEP 4: Ensure RLS is enabled and refresh
-- ============================================================================

-- Ensure RLS is enabled on all tables
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.rooms ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.reservations ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.payments ENABLE ROW LEVEL SECURITY;

-- Create performance indexes
CREATE INDEX IF NOT EXISTS idx_users_role ON public.users(role);
CREATE INDEX IF NOT EXISTS idx_reservations_guest_id ON public.reservations(guest_id);
CREATE INDEX IF NOT EXISTS idx_payments_reservation_id ON public.payments(reservation_id);

-- Refresh schema
NOTIFY pgrst, 'reload schema';

-- Update default currency to KES (Kenyan Shilling)
ALTER TABLE public.payments ALTER COLUMN currency SET DEFAULT 'KES';

-- ============================================================================
-- STEP 5: Fix relationship ambiguity between reservations and payments
-- ============================================================================

-- Remove the redundant payment_id foreign key constraint from reservations table
-- This eliminates the circular relationship that causes "more than one relationship" error
ALTER TABLE public.reservations DROP CONSTRAINT IF EXISTS fk_reservations_payment_id;

-- Remove the payment_id column from reservations table (it's redundant)
-- We only need reservation_id in payments table for the relationship
ALTER TABLE public.reservations DROP COLUMN IF EXISTS payment_id;

-- Success message
SELECT 'RLS policies fixed successfully! No more infinite recursion. Currency updated to KES. Relationship ambiguity resolved.' as status;
