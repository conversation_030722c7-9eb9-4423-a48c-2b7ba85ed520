-- ============================================================================
-- ADD SAMPLE IMAGES TO ROOMS
-- This script adds sample hotel room images to your rooms table
-- ============================================================================

-- Add sample images to standard rooms
UPDATE public.rooms 
SET images = ARRAY[
    'https://images.unsplash.com/photo-1566665797739-1674de7a421a?w=800',
    'https://images.unsplash.com/photo-1582719478250-c89cae4dc85b?w=800'
]
WHERE room_type = 'standard';

-- Add sample images to deluxe rooms
UPDATE public.rooms 
SET images = ARRAY[
    'https://images.unsplash.com/photo-1590490360182-c33d57733427?w=800',
    'https://images.unsplash.com/photo-1566195992011-5f6b21e539aa?w=800',
    'https://images.unsplash.com/photo-1560185007-c5ca9d2c014d?w=800'
]
WHERE room_type = 'deluxe';

-- Add sample images to suite rooms
UPDATE public.rooms 
SET images = ARRAY[
    'https://images.unsplash.com/photo-1578683010236-d716f9a3f461?w=800',
    'https://images.unsplash.com/photo-1611892440504-42a792e24d32?w=800',
    'https://images.unsplash.com/photo-1591088398332-8a7791972843?w=800',
    'https://images.unsplash.com/photo-1540518614846-7eded433c457?w=800'
]
WHERE room_type = 'suite';

-- Verify the images were added
SELECT 
    room_number,
    room_type,
    array_length(images, 1) as image_count,
    images
FROM public.rooms
ORDER BY room_type, room_number;

-- Show summary
SELECT 
    'Sample Images Added' as status,
    COUNT(*) as total_rooms,
    COUNT(*) FILTER (WHERE array_length(images, 1) > 0) as rooms_with_images,
    SUM(array_length(images, 1)) as total_images_added
FROM public.rooms;

-- Instructions for next steps
DO $$
BEGIN
    RAISE NOTICE '✅ Sample images added successfully!';
    RAISE NOTICE '📋 Next steps:';
    RAISE NOTICE '1. Run the fix-room-images-working.sql script again';
    RAISE NOTICE '2. This will convert the sample images to the new format with IDs';
    RAISE NOTICE '3. Then update your app to use the new format';
END;
$$;
