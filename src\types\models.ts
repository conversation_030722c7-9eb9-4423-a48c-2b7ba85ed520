// Define the ResevationType interface
export interface ReservationType {
  id: string;
  guest_id: string;
  room_id: string;
  room_number: string;
  check_in_date: string;
  check_out_date: string;
  total_amount: number;
  special_requests?: string | null;
  status: 'pending' | 'confirmed' | 'checked_in' | 'checked_out' | 'cancelled';
  payment_status: 'pending' | 'paid' | 'failed' | 'refunded';
  payment_id?: string | null;
  created_at: string;
  updated_at: string;
}

// Room image interface for enhanced image handling
export interface RoomImage {
  id: string;
  url: string;
  alt_text?: string;
  upload_date?: string;
  file_name?: string;
  file_size?: number;
}

// Define the RoomType interface
export interface RoomType {
  id: string;
  room_number: string;
  room_type: 'standard' | 'deluxe' | 'suite' | 'presidential';
  price_per_night: number;
  description: string;
  amenities: string[];
  max_occupancy: number;
  images: RoomImage[];
  status: 'available' | 'booked' | 'maintenance' | 'cleaning';
  created_at: string;
  updated_at: string;
}

// Define the UserType interface
export interface UserType {
  id: string;
  email: string;
  full_name: string;
  phone?: string | null;
  role: 'guest' | 'receptionist' | 'admin';
  created_at: string;
  updated_at: string;
}
