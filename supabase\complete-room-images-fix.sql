-- ============================================================================
-- COMPLETE ROOM IMAGES FIX - ADD SAMPLE IMAGES AND CONVERT TO NEW FORMAT
-- This script adds sample images and converts them to RoomImage objects with IDs
-- ============================================================================

-- Step 1: Add sample images to your rooms
UPDATE public.rooms 
SET images = ARRAY[
    'https://images.unsplash.com/photo-1566665797739-1674de7a421a?w=800&q=80',
    'https://images.unsplash.com/photo-1582719478250-c89cae4dc85b?w=800&q=80'
]
WHERE room_type = 'standard';

UPDATE public.rooms 
SET images = ARRAY[
    'https://images.unsplash.com/photo-1590490360182-c33d57733427?w=800&q=80',
    'https://images.unsplash.com/photo-1566195992011-5f6b21e539aa?w=800&q=80',
    'https://images.unsplash.com/photo-1560185007-c5ca9d2c014d?w=800&q=80'
]
WHERE room_type = 'deluxe';

UPDATE public.rooms
SET images = ARRAY[
    'https://images.unsplash.com/photo-1578683010236-d716f9a3f461?w=800&q=80',
    'https://images.unsplash.com/photo-1611892440504-42a792e24d32?w=800&q=80',
    'https://images.unsplash.com/photo-1591088398332-8a7791972843?w=800&q=80',
    'https://images.unsplash.com/photo-1540518614846-7eded433c457?w=800&q=80'
]
WHERE room_type = 'suite';

UPDATE public.rooms
SET images = ARRAY[
    'https://images.unsplash.com/photo-1582719478250-c89cae4dc85b?w=800&q=80',
    'https://images.unsplash.com/photo-1571896349842-33c89424de2d?w=800&q=80',
    'https://images.unsplash.com/photo-1590490360182-c33d57733427?w=800&q=80',
    'https://images.unsplash.com/photo-1578683010236-d716f9a3f461?w=800&q=80',
    'https://images.unsplash.com/photo-1611892440504-42a792e24d32?w=800&q=80'
]
WHERE room_type = 'presidential';

-- Step 2: Add the new images column if it doesn't exist
ALTER TABLE public.rooms ADD COLUMN IF NOT EXISTS images_enhanced JSONB DEFAULT '[]'::jsonb;

-- Step 3: Convert the sample images to the new format with unique IDs
DO $$
DECLARE
    room_record RECORD;
    image_url TEXT;
    new_images JSONB := '[]'::jsonb;
    image_index INTEGER;
    image_id TEXT;
    image_object JSONB;
    total_converted INTEGER := 0;
BEGIN
    RAISE NOTICE 'Converting room images to enhanced format with unique IDs...';
    
    -- Loop through all rooms that have images
    FOR room_record IN 
        SELECT id, room_number, room_type, images 
        FROM public.rooms 
        WHERE images IS NOT NULL AND array_length(images, 1) > 0
    LOOP
        -- Reset for each room
        new_images := '[]'::jsonb;
        image_index := 1;
        
        -- Convert each image URL to a RoomImage object
        FOREACH image_url IN ARRAY room_record.images
        LOOP
            -- Generate a unique ID for the image
            image_id := 'img_' || 
                       extract(epoch from now())::bigint || '_' || 
                       image_index || '_' || 
                       substr(md5(random()::text || room_record.id::text), 1, 8);
            
            -- Create the image object with proper structure
            image_object := jsonb_build_object(
                'id', image_id,
                'url', image_url,
                'alt_text', 'Room ' || room_record.room_number || ' (' || room_record.room_type || ') image ' || image_index,
                'upload_date', now()::text,
                'file_name', 'room-' || room_record.room_number || '-' || image_index || '.jpg'
            );
            
            -- Add to the new images array
            new_images := new_images || image_object;
            
            image_index := image_index + 1;
        END LOOP;
        
        -- Update the room with the new image format
        UPDATE public.rooms 
        SET images_enhanced = new_images 
        WHERE id = room_record.id;
        
        total_converted := total_converted + 1;
        
        RAISE NOTICE 'Converted room % (%) with % images', 
                     room_record.room_number, 
                     room_record.room_type, 
                     array_length(room_record.images, 1);
    END LOOP;
    
    RAISE NOTICE 'Successfully converted % rooms to enhanced format!', total_converted;
END;
$$;

-- Step 4: Create a view that uses the enhanced format
CREATE OR REPLACE VIEW rooms_with_image_ids AS
SELECT 
    id,
    room_number,
    room_type,
    price_per_night,
    description,
    amenities,
    max_occupancy,
    COALESCE(images_enhanced, '[]'::jsonb) as images,
    images as images_legacy,
    status,
    created_at,
    updated_at
FROM public.rooms;

-- Grant permissions on the view
GRANT SELECT ON rooms_with_image_ids TO authenticated;
GRANT SELECT ON rooms_with_image_ids TO anon;

-- Step 5: Create helper functions
CREATE OR REPLACE FUNCTION get_image_urls_from_enhanced(room_images JSONB)
RETURNS TEXT[]
LANGUAGE sql
IMMUTABLE
AS $$
    SELECT ARRAY(
        SELECT value->>'url'
        FROM jsonb_array_elements(room_images) AS value
    );
$$;

CREATE OR REPLACE FUNCTION get_image_by_id(room_images JSONB, image_id TEXT)
RETURNS JSONB
LANGUAGE sql
IMMUTABLE
AS $$
    SELECT value
    FROM jsonb_array_elements(room_images) AS value
    WHERE value->>'id' = image_id
    LIMIT 1;
$$;

-- Step 6: Show the results
SELECT 
    'Room Images Fix Complete!' as status,
    COUNT(*) as total_rooms,
    COUNT(*) FILTER (WHERE images_enhanced IS NOT NULL AND jsonb_array_length(images_enhanced) > 0) as rooms_with_enhanced_images,
    SUM(jsonb_array_length(images_enhanced)) FILTER (WHERE images_enhanced IS NOT NULL) as total_images_with_ids
FROM public.rooms;

-- Show sample of the new format
SELECT 
    room_number,
    room_type,
    jsonb_array_length(images_enhanced) as image_count,
    jsonb_pretty(images_enhanced) as enhanced_images_sample
FROM public.rooms 
WHERE images_enhanced IS NOT NULL AND jsonb_array_length(images_enhanced) > 0
LIMIT 2;

-- Test the helper functions
SELECT 
    room_number,
    get_image_urls_from_enhanced(images_enhanced) as extracted_urls,
    get_image_by_id(images_enhanced, images_enhanced->0->>'id') as first_image_details
FROM public.rooms 
WHERE images_enhanced IS NOT NULL AND jsonb_array_length(images_enhanced) > 0
LIMIT 1;

-- Final instructions
DO $$
BEGIN
    RAISE NOTICE '🎉 ROOM IMAGES FIX COMPLETED SUCCESSFULLY!';
    RAISE NOTICE '';
    RAISE NOTICE '✅ What was done:';
    RAISE NOTICE '- Added sample hotel room images to all rooms';
    RAISE NOTICE '- Converted images to enhanced format with unique IDs';
    RAISE NOTICE '- Created view "rooms_with_image_ids" for easy access';
    RAISE NOTICE '- Created helper functions for image management';
    RAISE NOTICE '';
    RAISE NOTICE '🚀 How to use in your app:';
    RAISE NOTICE '1. Update your queries to use the new view:';
    RAISE NOTICE '   supabase.from("rooms_with_image_ids").select("*")';
    RAISE NOTICE '';
    RAISE NOTICE '2. Or use the enhanced column directly:';
    RAISE NOTICE '   supabase.from("rooms").select("*, images_enhanced as images")';
    RAISE NOTICE '';
    RAISE NOTICE '3. Your images now have unique IDs for React keys!';
    RAISE NOTICE '   {room.images.map(img => <Image key={img.id} source={{uri: img.url}} />)}';
    RAISE NOTICE '';
    RAISE NOTICE '🔧 The missing image IDs issue is now FIXED!';
END;
$$;
