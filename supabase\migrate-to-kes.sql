-- Migration script to convert currency from NGN to KES
-- Run this script in Supabase SQL Editor after running the fix-recursion-final.sql

-- ============================================================================
-- STEP 1: Update existing payment records to use KES currency
-- ============================================================================

-- Update all existing payments from NGN to KES
UPDATE public.payments 
SET currency = 'KES' 
WHERE currency = 'NGN';

-- ============================================================================
-- STEP 2: Update room prices to appropriate KES amounts
-- ============================================================================

-- Convert room prices from NGN to KES (approximate conversion)
-- Standard rooms: 15000 NGN -> 8500 KES
-- Deluxe rooms: 25000 NGN -> 12500 KES  
-- Suite rooms: 45000 NGN -> 22000 KES
-- Presidential rooms: 85000 NGN -> 45000 KES

UPDATE public.rooms 
SET price_per_night = CASE 
    WHEN room_type = 'standard' THEN 8500.00
    WHEN room_type = 'deluxe' THEN 12500.00
    WHEN room_type = 'suite' THEN 22000.00
    WHEN room_type = 'presidential' THEN 45000.00
    ELSE price_per_night
END
WHERE price_per_night IN (15000.00, 25000.00, 45000.00, 85000.00);

-- ============================================================================
-- STEP 3: Update reservation amounts to KES equivalent
-- ============================================================================

-- Update reservation amounts based on new room prices
-- This will recalculate total amounts for existing reservations
UPDATE public.reservations 
SET total_amount = (
    SELECT 
        CASE 
            WHEN r.room_type = 'standard' THEN 8500.00 * (res.check_out_date - res.check_in_date)
            WHEN r.room_type = 'deluxe' THEN 12500.00 * (res.check_out_date - res.check_in_date)
            WHEN r.room_type = 'suite' THEN 22000.00 * (res.check_out_date - res.check_in_date)
            WHEN r.room_type = 'presidential' THEN 45000.00 * (res.check_out_date - res.check_in_date)
            ELSE res.total_amount
        END
    FROM public.rooms r 
    WHERE r.id = reservations.room_id
) 
FROM public.reservations res
JOIN public.rooms r ON r.id = res.room_id
WHERE reservations.id = res.id;

-- ============================================================================
-- STEP 4: Update payment amounts to match new reservation amounts
-- ============================================================================

-- Update payment amounts to match the updated reservation amounts
UPDATE public.payments 
SET amount = (
    SELECT r.total_amount 
    FROM public.reservations r 
    WHERE r.id = payments.reservation_id
)
WHERE EXISTS (
    SELECT 1 
    FROM public.reservations r 
    WHERE r.id = payments.reservation_id
);

-- ============================================================================
-- STEP 5: Create indexes for better performance (if not already exist)
-- ============================================================================

-- Create indexes for currency-related queries
CREATE INDEX IF NOT EXISTS idx_payments_currency ON public.payments(currency);
CREATE INDEX IF NOT EXISTS idx_rooms_price_per_night ON public.rooms(price_per_night);
CREATE INDEX IF NOT EXISTS idx_reservations_total_amount ON public.reservations(total_amount);

-- ============================================================================
-- STEP 6: Verify the migration
-- ============================================================================

-- Check payment currency distribution
SELECT 
    currency,
    COUNT(*) as payment_count,
    SUM(amount) as total_amount
FROM public.payments 
GROUP BY currency;

-- Check room price distribution by type
SELECT 
    room_type,
    COUNT(*) as room_count,
    MIN(price_per_night) as min_price,
    MAX(price_per_night) as max_price,
    AVG(price_per_night) as avg_price
FROM public.rooms 
GROUP BY room_type
ORDER BY room_type;

-- Check reservation amount distribution
SELECT 
    COUNT(*) as reservation_count,
    MIN(total_amount) as min_amount,
    MAX(total_amount) as max_amount,
    AVG(total_amount) as avg_amount
FROM public.reservations;

-- Success message
SELECT 'Currency migration to KES completed successfully!' as status;
