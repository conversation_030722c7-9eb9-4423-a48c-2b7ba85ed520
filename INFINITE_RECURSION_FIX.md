# Fix for Infinite Recursion in Supabase RLS Policies

## Problem Description

The error `"infinite recursion detected in policy for relation \"users\""` occurs when Row Level Security (RLS) policies on the `users` table reference the same table they're protecting, creating a circular dependency.

### Root Cause

The problematic policies were:

```sql
-- These policies cause infinite recursion
CREATE POLICY "<PERSON><PERSON> can view all users" ON public.users
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.users  -- ❌ Querying users table from users policy
            WHERE id = auth.uid() AND role IN ('admin', 'receptionist')
        )
    );

CREATE POLICY "Admins can manage all users" ON public.users
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.users  -- ❌ Querying users table from users policy
            WHERE id = auth.uid() AND role = 'admin'
        )
    );
```

When Supabase tries to execute these policies, it needs to access the `users` table to check the role, but accessing the `users` table triggers the same policies, creating infinite recursion.

## Solution Implemented

### 1. Immediate Fix - Remove Recursive Policies

The immediate fix removes the problematic policies and temporarily disables role-based access at the database level:

```sql
-- Remove recursive policies
DROP POLICY IF EXISTS "<PERSON><PERSON> can view all users" ON public.users;
DROP POLICY IF EXISTS "Admins can manage all users" ON public.users;
-- ... (other problematic policies)
```

### 2. Application-Level Permission System

Created a comprehensive permissions service (`src/services/permissionsService.ts`) that handles role-based access control at the application level:

```typescript
export class PermissionsService {
  // Handles all permission checks without database recursion
  canViewAllUsers(): boolean
  canManageUsers(): boolean
  canManageRooms(): boolean
  // ... other permission methods
}
```

### 3. Updated Auth Service Integration

Modified the auth service and store to initialize the permissions service when users sign in:

```typescript
// In authService.ensureUserProfile()
const userRole = user.user_metadata?.role || 'guest';
permissionsService.initialize(user.id, userRole);

// In authStore signOut()
permissionsService.clear();
```

## Files Modified

1. **`supabase/schema.sql`** - Removed recursive policies
2. **`supabase/fix-infinite-recursion.sql`** - Comprehensive fix with security definer function
3. **`supabase/fix-recursion-simple.sql`** - Simple fix removing role-based policies
4. **`src/services/permissionsService.ts`** - New permissions service
5. **`src/services/supabase.ts`** - Updated to use permissions service
6. **`src/store/authStore.ts`** - Updated to initialize/clear permissions

## How to Apply the Fix

### Option 1: Quick Fix (Recommended for immediate resolution)

1. **Start Docker Desktop** (required for Supabase local development)

2. **Apply the simple fix:**
   ```bash
   npx supabase start
   npx supabase db reset
   ```

3. **Or apply the fix manually:**
   ```bash
   npx supabase db push --include-all
   ```

### Option 2: Comprehensive Fix (Better long-term solution)

1. **Start Docker Desktop**

2. **Apply the comprehensive fix:**
   ```bash
   npx supabase start
   npx supabase db reset
   # Then run the fix-infinite-recursion.sql script
   ```

## Alternative Solutions Considered

### 1. Security Definer Function Approach
```sql
CREATE OR REPLACE FUNCTION public.get_user_role(user_id UUID)
RETURNS TEXT
SECURITY DEFINER
-- This bypasses RLS but requires careful security consideration
```

### 2. JWT Claims Approach
Using `auth.jwt()` to store role information in JWT tokens instead of querying the database.

### 3. Application-Level Only
Complete removal of role-based RLS policies and handling all permissions in the application layer (current implementation).

## Benefits of Current Solution

1. **Eliminates Recursion** - No more circular dependencies
2. **Maintains Security** - Application-level checks are still enforced
3. **Better Performance** - Fewer database queries for permission checks
4. **Easier Debugging** - Permission logic is in TypeScript, not SQL
5. **More Flexible** - Can implement complex permission logic easily

## Security Considerations

1. **Client-Side Validation** - The app now relies more on client-side permission checks
2. **API Security** - Ensure server-side validation for any API endpoints
3. **Role Updates** - Role changes require app restart or manual permission refresh
4. **Database Access** - Direct database access bypasses application permissions

## Testing the Fix

1. **Sign up a new user** - Should work without recursion errors
2. **Sign in existing users** - Should work normally
3. **Role-based navigation** - Admin/receptionist features should still be protected
4. **Permission checks** - Use `permissionsService.canManageRooms()` etc.

## Future Improvements

1. **Real-time Role Updates** - Implement role change notifications
2. **Server-Side Validation** - Add API-level permission checks
3. **Audit Logging** - Track permission-based actions
4. **Role Caching** - Cache role information for better performance

## Troubleshooting

If you still encounter issues:

1. **Clear browser cache** and app storage
2. **Restart Supabase** with `npx supabase stop && npx supabase start`
3. **Check Docker** is running properly
4. **Verify permissions service** is initialized in auth flow
5. **Check console logs** for permission-related errors

## Contact

If you need help implementing this fix or encounter any issues, please refer to the Supabase documentation on RLS policies and consider the trade-offs between database-level and application-level security.
