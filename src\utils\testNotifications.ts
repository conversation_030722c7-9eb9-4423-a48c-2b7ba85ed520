import { notificationService } from '../services/notifications';

/**
 * Test notification functions for development
 * These work perfectly in Expo Go with local notifications
 */

export const testNotifications = {
  // Test immediate notification
  async testImmediate() {
    console.log('🧪 Testing immediate notification...');
    await notificationService.sendLocalNotification({
      title: '✅ Test Successful!',
      body: 'Local notifications are working perfectly in Expo Go',
      data: { 
        test: true,
        timestamp: new Date().toISOString()
      }
    });
  },

  // Test scheduled notification (5 seconds)
  async testScheduled() {
    console.log('🧪 Testing scheduled notification (5 seconds)...');
    const notificationId = await notificationService.scheduleLocalNotification(
      {
        title: '⏰ Scheduled Test',
        body: 'This notification was scheduled 5 seconds ago!',
        data: { 
          test: true,
          type: 'scheduled',
          timestamp: new Date().toISOString()
        }
      },
      { seconds: 5 }
    );
    console.log('📅 Scheduled notification ID:', notificationId);
    return notificationId;
  },

  // Test hotel booking confirmation
  async testBookingConfirmation() {
    console.log('🧪 Testing booking confirmation...');
    await notificationService.sendBookingConfirmation(
      'TEST-123',
      'A101',
      new Date().toLocaleDateString()
    );
  },

  // Test payment reminder
  async testPaymentReminder() {
    console.log('🧪 Testing payment reminder...');
    await notificationService.sendPaymentReminder('TEST-123', 75000);
  },

  // Test check-in reminder
  async testCheckInReminder() {
    console.log('🧪 Testing check-in reminder...');
    await notificationService.sendCheckInReminder('A101');
  },

  // Test admin notification
  async testAdminNotification() {
    console.log('🧪 Testing admin notification...');
    await notificationService.sendNewBookingAlert('John Doe', 'B205');
  },

  // Test notification with actions
  async testNotificationWithActions() {
    console.log('🧪 Testing notification with action buttons...');
    await notificationService.sendLocalNotification({
      title: '🎯 Interactive Test',
      body: 'This notification has action buttons. Tap to see them!',
      data: { 
        test: true,
        hasActions: true
      },
      categoryId: 'booking' // This adds "View Details" and "Cancel" buttons
    });
  },

  // Test all notifications
  async testAll() {
    console.log('🚀 Running all notification tests...');
    
    await this.testImmediate();
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    await this.testBookingConfirmation();
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    await this.testPaymentReminder();
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    await this.testCheckInReminder();
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    await this.testAdminNotification();
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    await this.testNotificationWithActions();
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    await this.testScheduled();
    
    console.log('✅ All notification tests completed!');
    console.log('📱 Check your device for notifications');
  },

  // Get scheduled notifications
  async getScheduledNotifications() {
    const scheduled = await notificationService.getScheduledNotifications();
    console.log('📅 Scheduled notifications:', scheduled);
    return scheduled;
  },

  // Cancel all notifications
  async cancelAll() {
    await notificationService.cancelAllNotifications();
    console.log('🗑️ All notifications cancelled');
  }
};

// Export for easy testing in console
if (__DEV__) {
  // Make available globally for easy testing
  (global as any).testNotifications = testNotifications;
  console.log('🧪 Test notifications available globally: testNotifications');
  console.log('📝 Try: testNotifications.testAll()');
}
